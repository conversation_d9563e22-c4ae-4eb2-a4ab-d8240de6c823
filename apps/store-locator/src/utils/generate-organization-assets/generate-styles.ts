import { createFolder } from ':utils/create-folder';
import uniq from 'lodash/uniq';
import { promises as fs } from 'node:fs';
import path from 'node:path';

import type { GetStoreLocatorOrganizationConfigurationDto } from '@malou-io/package-dto';

export async function generateStyles(
    styles: GetStoreLocatorOrganizationConfigurationDto['styles'],
) {
    console.log('[STYLES] Generating styles...');

    const stylesDir = path.join('src', 'assets', 'generated', 'styles');
    await createFolder(stylesDir);

    const tailwindConfig = generateConfiguration(styles!);
    const tailwindClassesMap = generateMapping(styles!);

    await Promise.all([
        fs.writeFile(path.join(stylesDir, 'global.css'), tailwindConfig),
        fs.writeFile(
            path.join(stylesDir, 'classes.mapping.ts'),
            tailwindClassesMap,
        ),
    ]);

    console.log('[STYLES] Styles generated successfully.');
}

function generateConfiguration(
    storeLocatorOrganizationStyles: NonNullable<
        GetStoreLocatorOrganizationConfigurationDto['styles']
    >,
): string {
    const defaultFontFamily = storeLocatorOrganizationStyles.fonts[0]?.class;
    const tailwindConfig = `
@import 'tailwindcss';
/* As this file is git ignored, we have to set it as a source to be scanned by Tailwind */
@source "./classes.mapping.ts";

@theme {
${storeLocatorOrganizationStyles.colors
    .map((color) => {
        return `  --color-${color.class}: ${color.value};`;
    })
    .join('\n')}
    /* Define custom fonts */
    /* Font-sans is used to define default font family */
    --font-sans: ${defaultFontFamily ? `'${defaultFontFamily}', sans-serif` : 'sans-serif'};
${storeLocatorOrganizationStyles.fonts
    .map((font) => {
        return `  --font-${font.class}: '${font.class}', sans-serif;`;
    })
    .join('\n')}
}
    
@layer utilities {
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
}

html, body {
  overscroll-behavior: none;
}
  
`;

    return tailwindConfig;
}

function generateMapping(
    storeLocatorOrganizationStyles: GetStoreLocatorOrganizationConfigurationDto['styles'],
): string {
    const storePageClasses = (
        Object.values(storeLocatorOrganizationStyles.pages.store) as string[][]
    ).flat();
    const mapPageClasses = (
        Object.values(storeLocatorOrganizationStyles.pages.map) as string[][]
    ).flat();
    const organizationClasses = uniq([...storePageClasses, ...mapPageClasses]);

    // Create a map where keys and values are the same
    // This map will be used to tell Tailwind which CSS classes to generate
    const mapFromList = organizationClasses.reduce(
        (acc, curr) => {
            acc[curr] = curr;
            return acc;
        },
        {} as Record<string, string>,
    );

    // Create the content to be written to the TypeScript file
    return `// This mapping is necessary to help Tailwind scan active classes\nexport const classesMapping: Record<string, string> = ${JSON.stringify(mapFromList, null, 2)};`;
}
