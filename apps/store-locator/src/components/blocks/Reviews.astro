---
import ':assets/generated/styles/global.css';
import Star from ':assets/icons/star.svg';
import googleLogo from ':assets/logos/google.png';
import { initTranslationFunction } from ':i18n/index';
import type { IStorePage } from ':interfaces/pages.interfaces';
import { getStyles } from ':utils/get-element-styles';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    reviewsBlock: NonNullable<IStorePage['reviewsBlock']>;
    styles: IStorePage['styles'];
}

const { reviewsBlock, styles } = Astro.props as Props;
const getElementStyles = getStyles({ styles });
const t = await initTranslationFunction();
---

<div class={`${getElementStyles({ elementId: 'reviews-wrapper' })}`}>
    <div class="mx-auto max-w-[1600px] px-0 py-12 sm:px-6">
        <h2
            class={`${getElementStyles({ elementId: 'reviews-title' })} px-2 pb-8 text-center text-3xl font-extrabold sm:px-0 sm:text-4xl uppercase`}
        >
            {reviewsBlock.title}
        </h2>

        <div
            class="no-scrollbar mx-auto flex flex-nowrap overflow-x-auto xl:max-w-[1350px] xl:flex-wrap xl:justify-center"
        >
            {
                reviewsBlock.reviews.map((review) => (
                    <div class="m-2 min-h-[270px] w-[320px] flex-shrink-0 rounded-xl bg-white px-7 py-3 pb-7">
                        <div class="flex w-full items-center justify-between">
                            {'url' in review.picture ? (
                                <Picture
                                    src={review.picture.url}
                                    formats={['webp']}
                                    fallbackFormat="jpg"
                                    alt={t('reviews.google.profilePicture', {
                                        userName: review.userName,
                                    })}
                                    pictureAttributes={{
                                        class: 'rounded-full',
                                    }}
                                    width={35}
                                    height={35}
                                    densities={[1, 2, 3]}
                                />
                            ) : (
                                <div
                                    class="text-md flex h-[35px] w-[35px] items-center justify-center rounded-full text-white"
                                    style={`background-color: ${review.picture.color}`}
                                >
                                    {review.picture.initials}
                                </div>
                            )}
                            <div class="mr-2 ml-2 flex min-w-0 flex-1 flex-col items-start overflow-hidden">
                                <p class="w-full truncate text-sm font-semibold">
                                    {review.userName}
                                </p>
                                {review.publishedAt && (
                                    <p class="text-xs text-gray-500">
                                        {review.publishedAt}
                                    </p>
                                )}
                            </div>

                            <div class="flex flex-shrink-0 rounded-md bg-gray-100 p-2">
                                <Picture
                                    src={googleLogo}
                                    formats={['webp']}
                                    fallbackFormat="jpg"
                                    alt={t('reviews.google.logo')}
                                    width={24}
                                    height={24}
                                    densities={[1, 2, 3]}
                                />
                            </div>
                        </div>

                        <div class="my-4 flex">
                            {Array.from({ length: review.starsCount }).map(
                                () => (
                                    <span class="mx-1 text-yellow-500">
                                        <Star />
                                    </span>
                                ),
                            )}
                        </div>

                        <p class="line-clamp-8 text-xs text-gray-700">
                            {review.content}
                        </p>
                    </div>
                ))
            }
        </div>

        {
            reviewsBlock.cta && (
                <div class="mt-8 flex justify-center">
                    <a
                        aria-label={reviewsBlock.cta.text}
                        class={`${getElementStyles({ elementId: 'reviews-cta' })} ${reviewsBlock.cta.tracker ? 'analytics-tracker' : ''} my-3 block w-[320px] border p-4 text-center text-sm font-extralight uppercase`}
                        href={reviewsBlock.cta.url}
                        target="_blank"
                        {...(reviewsBlock.cta.tracker && {
                            'data-tracking-event-name':
                                reviewsBlock.cta.tracker.eventName,
                            'data-tracking-event-category':
                                reviewsBlock.cta.tracker.eventCategory,
                            'data-tracking-event-label':
                                reviewsBlock.cta.tracker.eventLabel,
                        })}
                    >
                        {reviewsBlock.cta.text}
                    </a>
                </div>
            )
        }
    </div>
</div>
