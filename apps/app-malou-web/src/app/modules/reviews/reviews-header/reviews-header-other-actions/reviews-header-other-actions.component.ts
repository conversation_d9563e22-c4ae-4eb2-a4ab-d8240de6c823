import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';

import { ReviewsService } from ':modules/reviews/reviews.service';
import * as ReviewsActions from ':modules/reviews/store/reviews.actions';
import { selectReviews } from ':modules/reviews/store/reviews.selectors';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-reviews-header-other-actions',
    templateUrl: './reviews-header-other-actions.component.html',
    styleUrls: ['./reviews-header-other-actions.component.scss'],
    imports: [MatButtonModule, MatIconModule, MatMenuModule, MatTooltipModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsHeaderOtherActionsComponent {
    readonly SvgIcon = SvgIcon;
    private readonly _store = inject(Store);
    private readonly _reviewsService = inject(ReviewsService);
    private readonly _selectedReviews = toSignal(this._store.select(selectReviews), { initialValue: [] });

    archiveAllReviews(): void {
        const reviewIds = this._selectedReviews().map((r) => r._id);
        this._reviewsService.archiveAllReviews(reviewIds).subscribe({
            next: () => {
                this._store.dispatch(ReviewsActions.onReviewsArchived());
            },
            error: (err) => {
                console.warn('err :>>', err);
            },
        });
    }
}
