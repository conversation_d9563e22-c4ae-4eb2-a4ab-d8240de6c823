import { CurrencyCode, ReviewCannotReplyExplanation, TranslationSource } from '@malou-io/package-utils';

import { MenuItemReview } from '../models';

export interface IReview {
    canBeEdited(): boolean;
    canBeReplied(): boolean;
    getCannotReplyExplanation(): ReviewCannotReplyExplanation | null;
    canHaveMultipleReplies(): boolean;
    getEaterTotalOrders(): number | undefined;
    getOrderTotal(): number | undefined;
    getOrderCurrencyCode(): CurrencyCode | undefined;
    getMenuItemReviews(): MenuItemReview[];
    getUbereatsPromotionAmountInHundredths(): number | undefined;
    getNbDaysUntilCantBeAnswered(): number | null;
    canBeRepliedBeforeTimeLimit(): boolean;
    getReviewDate(): string | undefined;
    getReviewOrderDate(): string | null;
    hasReply(): boolean;
    hasOnlyPendingComment(): boolean;
    hasRejectedComment(): boolean;
    hasAttachments(): boolean;
    isPrivate(): boolean;
    getDisplayName(): string;
    hasTranslations(lang: string): boolean;
    addTranslation(lang: string, translation: string, source: TranslationSource): void;
    getTranslation(lang: string): string;
    getFullReviewTextWithRatingTags(options?: { showTranslation: boolean; language: string }): string;
}
