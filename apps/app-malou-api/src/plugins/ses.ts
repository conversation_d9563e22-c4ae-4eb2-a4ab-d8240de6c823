import { chunk, uniq } from 'lodash';

import { waitFor } from '@malou-io/package-utils';

import { aws } from './aws';

export const sesService = new aws.SES();

/**
 *
 * Returns the list of all verified emails in SES
 */
export const getVerifiedEmailsInSES = async (emails: string[]): Promise<Record<string, boolean>> => {
    const uniqueEmails = uniq(emails);
    const emailsChunked = chunk(uniqueEmails, 100);
    const areEmailsVerifiedRecord: Record<string, boolean> = {};
    let doWait = false;
    for (const emailsChukedElement of emailsChunked) {
        const emailsWithStatus = await sesService
            .getIdentityVerificationAttributes({
                Identities: emailsChukedElement,
            })
            .promise();
        emailsChukedElement.forEach((email) => {
            areEmailsVerifiedRecord[email] = emailsWithStatus.VerificationAttributes[email]?.VerificationStatus === 'Success';
        });
        if (doWait) {
            await waitFor(1000);
        }
        doWait = true;
    }

    return areEmailsVerifiedRecord;
};

/**
 *
 * @param {string} templateName unique name of the email template
 * @param {string} content html of the email template
 * Creates a custom email template, necessary before sending
 */
export const createVerificationEmailInSES = async (templateName: string, content: string, templateSubject: string) => {
    await sesService.deleteCustomVerificationEmailTemplate({ TemplateName: templateName }).promise();
    const base64Name = Buffer.from("L'équipe Malou").toString('base64');
    const from = `=?UTF-8?B?${base64Name}?= <<EMAIL>>`;
    return sesService
        .createCustomVerificationEmailTemplate({
            TemplateName: templateName,
            FromEmailAddress: from,
            TemplateSubject: templateSubject,
            TemplateContent: content,
            SuccessRedirectionURL: `${process.env.BASE_URL}/email_verification_success`,
            FailureRedirectionURL: `${process.env.BASE_URL}/email_verification_failure`,
        })
        .promise();
};
