import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { objectIdValidator } from '@malou-io/package-dto';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { CommentsUseCases } from ':modules/comments/comments.use-cases';

const updatePlatformCommentsJobalidator = z.object({
    platformId: objectIdValidator,
    restaurantId: objectIdValidator,
});

@singleton()
export class UpdatePlatformCommentsJob extends GenericJobDefinition {
    constructor(private _commentsUseCases: CommentsUseCases) {
        super({
            agendaJobName: AgendaJobName.UPDATE_PLATFORM_COMMENTS,
            shouldDeleteJobOnSuccess: true,
        });
    }

    async executeJob(job: Job): Promise<void> {
        const data = updatePlatformCommentsJobalidator.parse(job.attrs.data);
        const { platformId, restaurantId } = data;

        await this._commentsUseCases.updateCommentsForPlatform(platformId, restaurantId);
    }
}
