import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';
import { DeepRequired } from 'utility-types';

import {
    AiOptimizeTextDto,
    AiPostGenerationSettingsDto,
    AnswerReviewPreviewBodyDto,
    answerReviewPreviewBodyValidator,
    ChoosePostHashtagsBodyDto,
    choosePostHashtagsBodyValidator,
    ChoosePostHashtagsDto,
    CountAiInteractionsDto,
    GeneratePostTextSettingsPreviewBodyDto,
    generatePostTextSettingsPreviewBodyValidator,
    GeneratePostTextSettingsPreviewResponseDto,
    GenerateReviewReplyBodyDto,
    generateReviewReplyBodyValidator,
    GenerateSeoPostTextSettingsBodyDto,
    generateSeoPostTextSettingsBodyValidator,
    GenerateSocialNetworkPostTextSettingsBodyDto,
    generateSocialNetworkPostTextSettingsBodyValidator,
    GenerateStoreLocatorDescriptionPreviewBodyDto,
    generateStoreLocatorDescriptionPreviewBodyValidator,
    OptimizeTextBodyDto,
    optimizeTextBodyValidator,
    TextTranslationBodyDto,
    textTranslationBodyValidator,
} from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { AiTextToOptimizeType, ApiResultV2, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body } from ':helpers/decorators/validators';
import { GeneratePostTextPreviewUseCase } from ':modules/ai/use-cases/generate-post-text-preview/generate-post-text-preview.use-case';
import { GenerateStoreLocatorDescriptionPreviewUseCase } from ':modules/ai/use-cases/generate-store-locator-description-preview/generate-store-locator-description-preview.use-case';
import { RestaurantAiSettings } from ':modules/restaurant-ai-settings/entities/restaurant-ai-settings.entity';

import {
    ChoosePostHashtagsUseCase,
    CountAiInteractionsByUserUseCase,
    GenerateReviewReplyUseCase,
    GenerateSeoPostTextUseCase,
    GenerateSocialNetworkPostTextUseCase,
    OptimizeReviewReplyUseCase,
    OptimizeSeoPostTextUseCase,
    OptimizeSocialPostTextUseCase,
    TranslateTextUseCase,
} from './use-cases';
import { AnswerReviewPreviewUseCase } from './use-cases/answer-review-preview/answer-review-preview.use-case';

@singleton()
export default class AiController {
    constructor(
        private readonly _generateReviewReplyUseCase: GenerateReviewReplyUseCase,
        private readonly _answerReviewPreviewUseCase: AnswerReviewPreviewUseCase,
        private readonly _generateSeoPostTextUseCase: GenerateSeoPostTextUseCase,
        private readonly _generateSocialNetworkPostTextUseCase: GenerateSocialNetworkPostTextUseCase,
        private readonly _choosePostHashtagsUseCase: ChoosePostHashtagsUseCase,
        private readonly _translateTextUseCase: TranslateTextUseCase,
        private readonly _optimizeSocialPostTextUseCase: OptimizeSocialPostTextUseCase,
        private readonly _optimizeSeoPostTextUseCase: OptimizeSeoPostTextUseCase,
        private readonly _optimizeReviewReplyUseCase: OptimizeReviewReplyUseCase,
        private readonly _generatePostTextPreviewUseCase: GeneratePostTextPreviewUseCase,
        private readonly _countAiInteractionsByUserUseCase: CountAiInteractionsByUserUseCase,
        private readonly _generateStoreLocatorDescriptionPreviewUseCase: GenerateStoreLocatorDescriptionPreviewUseCase
    ) {}

    @Body(generateReviewReplyBodyValidator)
    async handleGenerateReviewReply(req: Request<any, any, GenerateReviewReplyBodyDto>, res: Response, next: NextFunction) {
        try {
            const { reviewId } = req.body;
            const userId = req.user._id;

            const { completionText } = await this._generateReviewReplyUseCase.execute({
                reviewId,
                userId,
            });
            return res.json({ data: completionText });
        } catch (err) {
            next(err);
        }
    }

    @Body(generateSeoPostTextSettingsBodyValidator)
    async handleGenerateSeoPostTextUsingSettings(
        req: Request<any, any, GenerateSeoPostTextSettingsBodyDto>,
        res: Response<ApiResultV2<AiPostGenerationSettingsDto>>,
        next: NextFunction
    ) {
        try {
            const { postId, description, lang, restaurantId, shouldUseImageAnalysis } = req.body;
            const { _id: userId } = req.user;

            const result = await this._generateSeoPostTextUseCase.execute(
                toDbId(postId),
                description,
                lang,
                toDbId(restaurantId),
                toDbId(userId),
                shouldUseImageAnalysis
            );

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(generatePostTextSettingsPreviewBodyValidator)
    async handleGeneratePostTextPreviewUsingSettings(
        req: Request<any, any, DeepRequired<GeneratePostTextSettingsPreviewBodyDto>>,
        res: Response<ApiResultV2<GeneratePostTextSettingsPreviewResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { description, lang, restaurantId, postSettings, postSource } = req.body;
            const { _id: userId } = req.user;

            const data = await this._generatePostTextPreviewUseCase.execute({
                description,
                lang,
                restaurantId,
                postSource,
                postSettings,
                userId: userId.toString(),
            });

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Body(generateSocialNetworkPostTextSettingsBodyValidator)
    async handleGenerateSocialNetworkPostTextUsingSettings(
        req: Request<any, any, GenerateSocialNetworkPostTextSettingsBodyDto>,
        res: Response<ApiResultV2<AiPostGenerationSettingsDto>>,
        next: NextFunction
    ) {
        try {
            const { postId, description, restaurantId, lang, shouldUseImageAnalysis, mediaId } = req.body;
            const userId = req.user._id;

            const result = await this._generateSocialNetworkPostTextUseCase.execute(
                toDbId(postId),
                description,
                toDbId(restaurantId),
                lang,
                toDbId(userId),
                shouldUseImageAnalysis,
                mediaId ?? undefined
            );

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(choosePostHashtagsBodyValidator)
    async handleChoosePostHashtags(
        req: Request<any, any, ChoosePostHashtagsBodyDto>,
        res: Response<ApiResultV2<ChoosePostHashtagsDto>>,
        next: NextFunction
    ) {
        try {
            const { postId, restaurantId, postText, hashtags } = req.body;
            const userId = req.user._id;

            const postHashtags: string[] = await this._choosePostHashtagsUseCase.execute(
                toDbId(postId),
                toDbId(restaurantId),
                postText,
                hashtags,
                toDbId(userId)
            );

            return res.json({ data: postHashtags });
        } catch (err) {
            next(err);
        }
    }

    @Body(optimizeTextBodyValidator)
    async handleOptimizeText(
        req: Request<any, any, OptimizeTextBodyDto>,
        res: Response<ApiResultV2<AiOptimizeTextDto>>,
        next: NextFunction
    ) {
        try {
            const { relatedEntityId, textToOptimize, restaurantId, textToOptimizeType, lang } = req.body;
            const userId = req.user._id;

            let result: AiOptimizeTextDto;
            switch (textToOptimizeType) {
                case AiTextToOptimizeType.SEO_POST:
                    result = await this._optimizeSeoPostTextUseCase.execute(
                        toDbId(relatedEntityId),
                        textToOptimize,
                        toDbId(restaurantId),
                        toDbId(userId),
                        lang
                    );
                    break;
                case AiTextToOptimizeType.SOCIAL_NETWORK_POST:
                    result = await this._optimizeSocialPostTextUseCase.execute(
                        toDbId(relatedEntityId),
                        textToOptimize,
                        toDbId(restaurantId),
                        toDbId(userId),
                        lang
                    );
                    break;
                case AiTextToOptimizeType.REVIEW_ANSWER:
                    result = await this._optimizeReviewReplyUseCase.execute(relatedEntityId, textToOptimize, restaurantId, userId);
                    break;
                default:
                    throw new MalouError(MalouErrorCode.TEXT_TYPE_NOT_SUPPORTED, {
                        message: 'Text type not supported',
                        metadata: { textToOptimizeType },
                    });
            }

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(textTranslationBodyValidator)
    async handleTextTranslation(req: Request<any, any, TextTranslationBodyDto>, res: Response<ApiResultV2<string>>, next: NextFunction) {
        try {
            const { relatedEntityId, relatedEntityCollection, text, type, lang, restaurantId } = req.body;
            const userId = req.user._id;

            const translatedText = await this._translateTextUseCase.execute({
                relatedEntityId,
                aiInteractionRelatedEntityCollection: relatedEntityCollection,
                type,
                text,
                lang: lang ?? undefined,
                restaurantId,
                userId,
            });

            return res.status(200).json({ data: translatedText });
        } catch (err) {
            next(err);
        }
    }

    @Body(answerReviewPreviewBodyValidator)
    async handleAnswerReviewPreview(
        req: Request<any, any, AnswerReviewPreviewBodyDto>,
        res: Response<ApiResultV2<string>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId, restaurantAiSettings, text, lang, reviewerName, sourceLanguage } = req.body;
            const userId = req.user._id;
            const translatedText: string | undefined = await this._answerReviewPreviewUseCase.execute({
                restaurantId,
                restaurantAiSettings: RestaurantAiSettings.fromRestaurantAiSettingsValidator(restaurantAiSettings),
                sourceLanguage,
                text,
                reviewerName,
                lang,
                userId,
            });
            return res.status(200).json({ data: translatedText });
        } catch (err) {
            next(err);
        }
    }

    async handleCountAiInteractions(req: Request, res: Response<ApiResultV2<CountAiInteractionsDto>>, next: NextFunction) {
        try {
            const userId = req.user._id;

            const aiInteractionsCount = await this._countAiInteractionsByUserUseCase.execute(toDbId(userId));

            return res.status(200).json({ data: aiInteractionsCount });
        } catch (err) {
            next(err);
        }
    }

    @Body(generateStoreLocatorDescriptionPreviewBodyValidator)
    async handleGenerateStoreLocatorDescriptionPreview(
        req: Request<any, any, GenerateStoreLocatorDescriptionPreviewBodyDto>,
        res: Response<ApiResultV2<string>>,
        next: NextFunction
    ) {
        try {
            const { organizationId, aiSettings } = req.body;
            const userId = req.user._id;

            const descriptionPreview = await this._generateStoreLocatorDescriptionPreviewUseCase.execute({
                organizationId: organizationId,
                aiSettings,
                userId,
            });

            return res.status(200).json({ data: descriptionPreview });
        } catch (err) {
            next(err);
        }
    }
}
