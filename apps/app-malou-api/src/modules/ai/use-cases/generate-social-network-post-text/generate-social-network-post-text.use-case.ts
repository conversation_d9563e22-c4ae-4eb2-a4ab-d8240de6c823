import { singleton } from 'tsyringe';

import { AiPostGenerationSettingsDto } from '@malou-io/package-dto';
import { DbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    MalouErrorCode,
    MediaType,
    PostSource,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { GeneratePostTextService } from ':modules/ai/services/generate-post-text.service';
import { MediasRepository } from ':modules/media/medias.repository';
import PostsRepository from ':modules/posts/posts.repository';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import { PrefillAiPostSettingsService } from ':modules/restaurant-ai-settings/services/prefill-ai-post-settings.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class GenerateSocialNetworkPostTextUseCase {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _generatePostTextService: GeneratePostTextService,
        private readonly _restaurantAiSettingsRepository: RestaurantAiSettingsRepository,
        private readonly _prefillAiPostSettingsService: PrefillAiPostSettingsService
    ) {}

    async execute(
        postId: DbId,
        description: string,
        restaurantId: DbId,
        lang: string,
        userId: DbId,
        shouldUseImageAnalysis?: boolean,
        mediaId?: string
    ): Promise<AiPostGenerationSettingsDto> {
        const aiInteractionType = AiInteractionType.GENERATE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS;
        const postTemp = await this._postsRepository.findById(postId.toString());
        const aiInteraction = await this._aiInteractionsRepository.create({
            data: {
                type: aiInteractionType,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
                relatedEntityId: postId,
                relatedEntityBindingId: postTemp?.bindingId,
                userId,
            },
        });

        try {
            const restaurantWithCategory = await this._restaurantsRepository.findOne({
                filter: { _id: restaurantId },
                projection: { category: 1, name: 1, address: 1, _id: 1, ai: 1 },
                options: {
                    lean: true,
                    populate: [{ path: 'category' }],
                },
            });

            if (!restaurantWithCategory) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(restaurantWithCategory, AI_HARD_LIMIT_CALL_COUNT);

            const hasPostSettingsBeenGenerated = await this._prefillAiPostSettingsService.execute({
                restaurantId: restaurantWithCategory._id.toString(),
                source: PostSource.SOCIAL,
            });

            const postSettings =
                (await this._restaurantAiSettingsRepository.getPostSettingsByRestaurantId({
                    restaurantId: restaurantWithCategory._id.toString(),
                    source: PostSource.SOCIAL,
                })) ?? undefined;

            let photoDescription: string | undefined;
            if (shouldUseImageAnalysis) {
                if (mediaId) {
                    photoDescription = (await this._mediasRepository.findById(mediaId))?.aiDescription ?? undefined;
                } else {
                    const post = await this._postsRepository.findOne({
                        filter: { _id: postId },
                        options: { lean: true, populate: [{ path: 'attachments' }], projection: { attachments: 1 } },
                    });
                    photoDescription =
                        post?.attachments?.find((media) => media.type === MediaType.PHOTO && !!media.aiDescription)?.aiDescription ??
                        undefined;
                }
            }

            const { aiResponse, aiInteractionDetails } = await this._generatePostTextService.generate({
                description,
                lang,
                restaurantWithCategory,
                postSource: PostSource.SOCIAL,
                photoDescription,
                postSettings,
            });

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurantWithCategory._id,
                feature: AiInteractionType.GENERATE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS,
            });
            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                aiInteractionDetails[0],
                restaurantWithCategory._id
            );

            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction._id },
                update: updatedAiInteraction,
                options: { new: true },
            });

            return {
                captions: aiResponse.captions,
                hasPostSettingsBeenGenerated,
            };
        } catch (error: any) {
            logger.error('[AiUseCases] [generateSocialNetworkPostText] Error', { error, postId, description, restaurantId, lang });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction._id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }
}
