import { singleton } from 'tsyringe';

import { AiOptimizeTextDto } from '@malou-io/package-dto';
import { DbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    getApplicationLanguageDisplayName,
    MalouErrorCode,
    mapLanguageStringToApplicationLanguage,
    PostSource,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { AiTextGenerationService } from ':microservices/ai-text-generation.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { DEFAULT_SOCIAL_AI_POST_SETTINGS } from ':modules/ai/ai-constants';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers';
import {
    AiPayloadOptions,
    OptimizeSocialNetworkPostTextAdvancedSettingsPayload,
    OptimizeSocialNetworkPostTextPayload,
    RestaurantWithCategory,
} from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { AiCompletionLangService } from ':modules/ai/services/ai-completion-lang.service';
import { KeywordBricksUseCase } from ':modules/keywords/modules/keyword-bricks/keyword-bricks.use-case';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { PostAiSettings } from ':modules/restaurant-ai-settings/entities/post-ai-settings.entity';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class OptimizeSocialPostTextUseCase {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _aiTextGenerationService: AiTextGenerationService<
            OptimizeSocialNetworkPostTextPayload | OptimizeSocialNetworkPostTextAdvancedSettingsPayload
        >,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _aiCompletionLangService: AiCompletionLangService,
        private readonly _keywordBricksUseCase: KeywordBricksUseCase,
        private readonly _restaurantAiSettingsRepository: RestaurantAiSettingsRepository,
        private readonly _postsRepository: PostsRepository
    ) {}

    async execute(postId: DbId, textToOptimize: string, restaurantId: DbId, userId: DbId, defaultLang: string): Promise<AiOptimizeTextDto> {
        const aiInteractionType = AiInteractionType.OPTIMIZE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS;
        const post = await this._postsRepository.findById(postId.toString());
        const aiInteraction = await this._aiInteractionsRepository.create({
            data: {
                type: aiInteractionType,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
                relatedEntityId: postId,

                relatedEntityBindingId: post?.bindingId,
                userId,
            },
        });

        try {
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: restaurantId },
                options: { lean: true, populate: [{ path: 'category' }] },
            });

            if (!restaurant) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const optimizationLang = await this._aiCompletionLangService.getLangForSocialNetworkPostOptimization(
                restaurantId.toString(),
                postId.toString(),
                textToOptimize,
                defaultLang
            );

            const postSettings = await this._restaurantAiSettingsRepository.getPostSettingsByRestaurantId({
                restaurantId: restaurant._id.toString(),
                source: PostSource.SOCIAL,
            });

            const payload = await this._computePayload(textToOptimize, restaurant, optimizationLang, postSettings);
            const { aiResponse, aiInteractionDetails } = await this._aiTextGenerationService.generateCompletion(payload);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.OPTIMIZE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS,
            });
            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                aiInteractionDetails?.[0],
                restaurant._id
            );

            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction._id },
                update: updatedAiInteraction,
                options: { new: true },
            });

            return {
                optimizedText: aiResponse.toString(),
                lang: optimizationLang,
            };
        } catch (error: any) {
            logger.error('[AiUseCases] [optimizeSocialNetworkPostText] Error', { error, postId, textToOptimize, restaurantId });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction._id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _computePayload(
        textToOptimize: string,
        restaurant: RestaurantWithCategory,
        lang: string,
        postSettings: PostAiSettings | null
    ): Promise<OptimizeSocialNetworkPostTextPayload | OptimizeSocialNetworkPostTextAdvancedSettingsPayload> {
        const categoryName = restaurant.category?.categoryName;
        const category = categoryName?.[lang] || categoryName?.backup || AiPayloadOptions.defaultBusinessCategoryName;

        const keywordBricks = await this._keywordBricksUseCase.getSelectedKeywordsBricks(restaurant._id.toString(), lang);

        const languageDisplayName = getApplicationLanguageDisplayName(lang, 'en') || lang;
        const langAsApplicationLanguage = mapLanguageStringToApplicationLanguage(lang);

        const settings = postSettings ?? DEFAULT_SOCIAL_AI_POST_SETTINGS;

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
            type: AiInteractionType.OPTIMIZE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS,
            restaurantData: {
                restaurantName: restaurant.name,
                restaurantCategory: category,
                restaurantCity: restaurant.address?.locality ?? '',
                language: languageDisplayName,
                keywords: keywordBricks.map((brick) => brick.getTranslatedText(langAsApplicationLanguage)),
                textToOptimize,
                ...settings?.toLambdaDto(),
            },
        };
    }
}
