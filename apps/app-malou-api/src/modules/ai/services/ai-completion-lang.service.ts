import { countBy, maxBy } from 'lodash';
import { singleton } from 'tsyringe';

import { IReview, toDbId } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    ApplicationLanguage,
    DEFAULT_LANG_UNKNOWN,
    getLanguageFromCountryCode,
    isLangInApplicationLanguages,
    mapLanguageStringToApplicationLanguage,
} from '@malou-io/package-utils';

import { RestaurantAiSettings } from ':modules/restaurant-ai-settings/entities/restaurant-ai-settings.entity';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GenerateLanguageDetectionService } from ':services/text-translator/generate-language-detection.service';

@singleton()
export class AiCompletionLangService {
    constructor(
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _generateLanguageDetectionService: GenerateLanguageDetectionService
    ) {}

    async getLangForReviewReplyGeneration(review: IReview, restaurantAiSettings?: RestaurantAiSettings): Promise<string> {
        if (review.keywordsLang) {
            return review.keywordsLang;
        }
        if (!review.text && restaurantAiSettings?.defaultLanguageResponse) {
            return restaurantAiSettings.defaultLanguageResponse;
        }
        if (review.lang && review.lang !== DEFAULT_LANG_UNKNOWN) {
            return review.lang;
        }

        if (restaurantAiSettings?.defaultLanguageResponse) {
            return restaurantAiSettings.defaultLanguageResponse;
        }

        const mostOccurrencesKeywordsLang = await this._getMostOccurrencesKeywordsLang(review.restaurantId.toString());
        if (mostOccurrencesKeywordsLang) {
            return mostOccurrencesKeywordsLang;
        }

        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: review.restaurantId },
            options: { lean: true },
        });

        const countryCodeLang = getLanguageFromCountryCode(restaurant.address?.regionCode);
        return countryCodeLang;
    }

    async getLangForReviewReplyOptimization({
        reviewId,
        restaurantId,
        text,
    }: {
        reviewId: string;
        restaurantId: string;
        text: string;
    }): Promise<string> {
        try {
            const detectedLang = await this._generateLanguageDetectionService.execute({
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                relatedEntityId: reviewId,
                restaurantId,
                text,
            });

            if (detectedLang !== DEFAULT_LANG_UNKNOWN) {
                return detectedLang;
            }

            const mostOccurrencesKeywordsLang = await this._getMostOccurrencesKeywordsLang(restaurantId);
            if (mostOccurrencesKeywordsLang) {
                return mostOccurrencesKeywordsLang;
            }

            const restaurant = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: toDbId(restaurantId) },
                options: { lean: true },
            });

            const countryCodeLang = getLanguageFromCountryCode(restaurant.address?.regionCode);
            return countryCodeLang;
        } catch (err) {
            return DEFAULT_LANG_UNKNOWN;
        }
    }

    async getLangForSeoPostOptimization(
        restaurantId: string,
        postId: string,
        textToOptimize: string,
        defaultLang: string
    ): Promise<string> {
        try {
            const detectedLang = await this._generateLanguageDetectionService.execute({
                relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
                relatedEntityId: postId,
                restaurantId,
                text: textToOptimize,
            });

            if (detectedLang !== DEFAULT_LANG_UNKNOWN && isLangInApplicationLanguages(detectedLang)) {
                return detectedLang;
            }
            const mostOccurrencesKeywordsLang = await this._getMostOccurrencesKeywordsLang(restaurantId);
            if (mostOccurrencesKeywordsLang) {
                return mostOccurrencesKeywordsLang;
            }
            return defaultLang;
        } catch (err) {
            return defaultLang;
        }
    }

    async getLangForSocialNetworkPostOptimization(
        restaurantId: string,
        postId: string,
        textToOptimize: string,
        defaultLang: string
    ): Promise<string> {
        try {
            const detectedLang = await this._generateLanguageDetectionService.execute({
                relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
                relatedEntityId: postId,
                restaurantId,
                text: textToOptimize,
            });

            if (detectedLang !== DEFAULT_LANG_UNKNOWN && isLangInApplicationLanguages(detectedLang)) {
                return detectedLang;
            }
            return defaultLang;
        } catch (err) {
            return defaultLang;
        }
    }

    private async _getMostOccurrencesKeywordsLang(restaurantId: string): Promise<ApplicationLanguage | null> {
        const restaurantKeywords = await this._restaurantKeywordsRepository.findSelectedRestaurantKeywords(restaurantId);

        if (restaurantKeywords.length === 0) {
            return null;
        }
        const langs = restaurantKeywords.map((restaurantKeyword) => restaurantKeyword.keyword.language);
        const langOccurrences = countBy(langs);
        const langOccurrencesKeys = Object.keys(langOccurrences);
        const getMostOccurrencesKeywordsLang = maxBy(langOccurrencesKeys, (lang) => langOccurrences[lang]) ?? null;
        return getMostOccurrencesKeywordsLang ? mapLanguageStringToApplicationLanguage(getMostOccurrencesKeywordsLang) : null;
    }
}
