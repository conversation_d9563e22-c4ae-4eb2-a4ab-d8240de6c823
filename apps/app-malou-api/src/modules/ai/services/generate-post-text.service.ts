import { sampleSize } from 'lodash';
import { singleton } from 'tsyringe';

import { ICategory, IRestaurant } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    getApplicationLanguageDisplayName,
    mapLanguageStringToApplicationLanguage,
    PostPublicationStatus,
    PostSource,
} from '@malou-io/package-utils';

import { GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiPostGenerationResponse, AiTextGenerationService } from ':microservices/ai-text-generation.service';
import { DEFAULT_SEO_AI_POST_SETTINGS, DEFAULT_SOCIAL_AI_POST_SETTINGS } from ':modules/ai/ai-constants';
import { AiPayloadOptions } from ':modules/ai/interfaces/ai.interfaces';
import { KeywordBricksUseCase } from ':modules/keywords/modules/keyword-bricks/keyword-bricks.use-case';
import PostsRepository from ':modules/posts/posts.repository';
import { PostAiSettings } from ':modules/restaurant-ai-settings/entities/post-ai-settings.entity';

type GeneratePostTextParams = {
    description: string;
    lang: string;
    restaurantWithCategory: Pick<IRestaurant, '_id' | 'name' | 'address'> & {
        category: ICategory;
    };
    postSource: PostSource;
    postSettings?: PostAiSettings;
    photoDescription?: string;
};

@singleton()
export class GeneratePostTextService {
    constructor(
        private _aiTextGenerationService: AiTextGenerationService<any>,
        private _postsRepository: PostsRepository,
        private _keywordBricksUseCase: KeywordBricksUseCase
    ) {}

    async generate({
        description,
        lang,
        restaurantWithCategory,
        postSource,
        photoDescription,
        postSettings,
    }: GeneratePostTextParams): Promise<GenericAiServiceResponseType<AiPostGenerationResponse>> {
        const payload = await this._computePayload({
            description,
            lang,
            restaurantWithCategory,
            postSource,
            photoDescription,
            postSettings,
        });
        return this._aiTextGenerationService.generateCompletion(payload) as Promise<GenericAiServiceResponseType<AiPostGenerationResponse>>;
    }

    private async _computePayload({
        description,
        lang,
        restaurantWithCategory,
        postSource,
        photoDescription,
        postSettings,
    }: GeneratePostTextParams) {
        switch (postSource) {
            case PostSource.SEO:
                return this._computeSeoPayload({
                    description,
                    lang,
                    restaurantWithCategory,
                    photoDescription,
                    postSettings,
                });
            case PostSource.SOCIAL:
                return this._computeSocialPayload({
                    description,
                    lang,
                    restaurantWithCategory,
                    photoDescription,
                    postSettings,
                });
            default:
                return null;
        }
    }

    private async _computeSocialPayload({
        description,
        lang,
        restaurantWithCategory,
        postSettings,
        photoDescription,
    }: Omit<GeneratePostTextParams, 'postSource'>) {
        const previousPosts = await this._postsRepository.find({
            filter: {
                restaurantId: restaurantWithCategory._id,
                published: PostPublicationStatus.PUBLISHED,
                source: PostSource.SOCIAL,
                isStory: { $ne: true },
            },
            options: { lean: true, sort: { socialCreatedAt: -1 }, limit: 20 },
        });

        const previousPostsTexts = previousPosts.map((post) => post.text).filter((text) => !!text && !!text.length);

        const previousPostsSample = sampleSize(previousPostsTexts, AiPayloadOptions.previousPostsSampleSize);

        const categoryName = restaurantWithCategory.category?.categoryName;
        const category = categoryName?.[lang] || categoryName?.backup || AiPayloadOptions.defaultBusinessCategoryName;

        const languageDisplayName = getApplicationLanguageDisplayName(lang, 'en') ?? lang;

        const settings = postSettings ? postSettings : DEFAULT_SOCIAL_AI_POST_SETTINGS;

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
            type: AiInteractionType.GENERATE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS,
            restaurantData: {
                restaurantName: restaurantWithCategory.name,
                restaurantCategory: category,
                restaurantCity: restaurantWithCategory.address?.locality ?? '',
                language: languageDisplayName,
                previousPosts: previousPostsSample,
                description,
                photoDescription,
                ...settings.toLambdaDto(),
            },
        };
    }

    private async _computeSeoPayload({
        description,
        lang,
        restaurantWithCategory,
        postSettings,
        photoDescription,
    }: Omit<GeneratePostTextParams, 'postSource'>) {
        const keywordBricks = await this._keywordBricksUseCase.getSelectedKeywordsBricks(restaurantWithCategory._id.toString(), lang);

        const categoryName = restaurantWithCategory.category?.categoryName;
        const category = categoryName?.[lang] || categoryName?.backup || AiPayloadOptions.defaultBusinessCategoryName;

        const previousPosts = await this._postsRepository.find({
            filter: { restaurantId: restaurantWithCategory._id, published: PostPublicationStatus.PUBLISHED, source: PostSource.SEO },
            options: { lean: true, sort: { socialCreatedAt: -1 }, limit: 20 },
        });
        const previousPostsTexts = previousPosts.map((post) => post.text).filter((text) => !!text && !!text.length);
        const previousPostsSample = sampleSize(previousPostsTexts, AiPayloadOptions.previousPostsSampleSize);

        const languageDisplayName = getApplicationLanguageDisplayName(lang, 'en') ?? lang;
        const langAsApplicationLanguage = mapLanguageStringToApplicationLanguage(lang);

        const settings = postSettings ? postSettings : DEFAULT_SEO_AI_POST_SETTINGS;

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS,
            type: AiInteractionType.GENERATE_SEO_POST_ADVANCED_SETTINGS,
            restaurantData: {
                restaurantName: restaurantWithCategory.name,
                restaurantCategory: category,
                restaurantCity: restaurantWithCategory.address?.locality ?? '',
                previousPosts: previousPostsSample,
                description,
                language: languageDisplayName,
                keywords: keywordBricks.map((brick) => brick.getTranslatedText(langAsApplicationLanguage)),
                photoDescription,
                ...settings.toLambdaDto(),
            },
        };
    }
}
