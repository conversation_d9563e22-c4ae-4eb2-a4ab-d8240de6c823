import dot from 'dot-object';
import { filter, includes, intersectionWith, isNil, padStart, uniq } from 'lodash';
import { autoInjectable } from 'tsyringe';

import {
    DbId,
    IAttribute,
    ICategory,
    ID,
    IHoursType,
    IRestaurant,
    IRestaurantAddress,
    IRestaurantAttributeWithAttribute,
    IRestaurantOtherHour,
    IRestaurantRegularHour,
    IRestaurantSpecialHour,
    PopulateBuilderHelper,
} from '@malou-io/package-models';
import {
    COUNTRY_CODES,
    CountryCode,
    Day,
    DAYS,
    descriptionSize,
    GmbAttributesEnum,
    GmbOpeningStatus,
    isNotNil,
    isValidUrl,
    MalouAttributesEnum,
    MalouErrorCode,
    PlatformKey,
    platformsKeys,
    RestaurantAttributeValue,
    SocialNetworkKey,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { getCountryNameFrFromCountryCode, getGeolocationForRestaurant, getMalouCountryCode, mapToMalouPhone } from ':helpers/utils';
import CategoriesRepository from ':modules/categories/categories.repository';
import { AddOrUpdateCategoriesService } from ':modules/categories/services/add-or-update-category.service';
import { FetchedAccountLocationWithAccess } from ':modules/credentials/platforms/gmb/interfaces';
import { HourTypesRepository } from ':modules/hour-types/hour-types.repository';
import { Mapper } from ':modules/platforms/platforms.mapper';
import {
    GmbAddress,
    GmbAttribute,
    GmbDayOfWeek,
    GmbLocation,
    GmbMoreHours,
    GmbRegularHour,
    GmbSectionType,
    GmbSpecialHourPeriod,
    GmbSpecialHours,
    GmbTimeOfDay,
    GmbValue,
    IRestaurantSpecialHourDate,
    OpenForBusiness,
} from ':modules/platforms/platforms/gmb/gmb.types';
import { FindAttributeService } from ':modules/platforms/platforms/gmb/services/find-attribute.service';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

type Writeable<T> = { -readonly [P in keyof T]: T[P] };

type OriginObject<T> = Writeable<{
    [K in keyof T as T[K] extends { malouKey: infer Origin } ? (Origin extends string ? Origin : never) : never]: any;
}>;

/**
 * Mapper implementation for GMB
 */
@autoInjectable()
export class GmbMapper {
    supportedFields: string[] = [];
    platformSupportedFields: string[] = [];

    constructor(
        private readonly _addOrUpdateCategoriesService: AddOrUpdateCategoriesService,
        private readonly _categoriesRepository: CategoriesRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _hourTypesRepository: HourTypesRepository,
        private readonly _findAttributeService: FindAttributeService
    ) {
        this.setSupportedFields();
        this.setPlatformSupportedFields();
    }

    setSupportedFields(): void {
        this.supportedFields = Object.keys(this.mappingConfiguration);
    }

    isSupportedField(field: string): boolean {
        return this.supportedFields.includes(field);
    }

    isPlatformSupportedField(field: string): boolean {
        return isNotNil(this.platformSupportedFields.find((supportedField) => supportedField.includes(field)));
    }

    setPlatformSupportedFields(): void {
        this.platformSupportedFields = uniq(
            Object.keys(this.mappingConfiguration)
                .map((key) => this.mappingConfiguration[key]?.gmbKey)
                .filter(isNotNil)
        );
    }

    async toPlatformMapper(
        malouData?: Record<string, any> | null,
        restaurantId: string | null = null,
        diffFields?: string[]
    ): Promise<Partial<GmbLocation & { attributes: GmbAttribute[] }>> {
        if (!malouData || (Object.entries(malouData).length === 0 && malouData.constructor === Object)) {
            return {};
        }
        const res = {};
        const fields = diffFields ? intersectionWith(this.supportedFields, diffFields, includes) : this.supportedFields;
        for (const f of fields ?? []) {
            const mapped = this.mappingConfiguration[f];
            if (mapped.shouldBePublished !== undefined && !mapped.shouldBePublished) {
                continue;
            }
            if (mapped.gmbKey && malouData[f] !== undefined) {
                if (!res[mapped.gmbKey]) {
                    res[mapped.gmbKey] = 'toGmbFormat' in mapped ? await mapped.toGmbFormat(malouData[f], restaurantId) : malouData[f];
                } else if (Array.isArray(res[mapped.gmbKey])) {
                    res[mapped.gmbKey] = res[mapped.gmbKey].concat(
                        'toGmbFormat' in mapped ? await mapped.toGmbFormat(malouData[f], restaurantId) : malouData[f]
                    );
                }
            }
        }
        dot.object(res);
        return res;
    }

    async toMalouMapper(platData: Record<string, any>, desiredFields?: string[]): Promise<OriginObject<typeof this.mappingConfiguration>> {
        if (!platData || (Object.entries(platData).length === 0 && platData.constructor === Object)) {
            throw new MalouError(MalouErrorCode.PLATFORM_DATA_UNDEFINED_OR_EMPTY, { message: 'GMB' });
        }

        if (platData.openInfo?.status === OpenForBusiness.CLOSED_PERMANENTLY) {
            throw new MalouError(MalouErrorCode.RESTAURANT_CLOSED_PERMANENTLY, {
                metadata: { name: platData.title, socialId: platData.metadata.placeId },
            });
        }

        const final = {};
        const fields = desiredFields
            ? intersectionWith(this.platformSupportedFields, desiredFields, includes)
            : this.platformSupportedFields;

        await Promise.all(
            (fields ?? []).map(async (field) => {
                // Several malou fields can be mapped to the same gmb field ("attributes" for example)
                const mappedFields = Object.values(this.mappingConfiguration).filter((value) => value.gmbKey === field);

                return Promise.all(
                    mappedFields.map(async (mapped) => {
                        final[mapped.malouKey] = 'toMalouFormat' in mapped ? await mapped.toMalouFormat(platData) : platData[mapped.gmbKey];
                    })
                );
            })
        );

        return final as OriginObject<typeof this.mappingConfiguration>;
    }

    getPlatformFieldName(malouField: string): string | null {
        if (!this.isSupportedField(malouField)) {
            return null;
        }
        return this.mappingConfiguration[malouField].hasOwnProperty('shouldBePublished') &&
            !this.mappingConfiguration[malouField].shouldBePublished
            ? null
            : this.mappingConfiguration[malouField].gmbKey;
    }

    getMalouFieldName(platformField: string): string | null {
        if (!this.isPlatformSupportedField(platformField)) {
            return null;
        }
        const value = Object.values(this.mappingConfiguration)
            .filter((brainValue) => brainValue.gmbKey)
            .find((brainValue) => brainValue.gmbKey.includes(platformField));
        return !value || (value.hasOwnProperty('shouldBePublished') && !value.shouldBePublished) ? null : value.malouKey;
    }

    toMalouMapperSearch(searchResults: FetchedAccountLocationWithAccess[]): MalouRestaurantSearchResult[] {
        if (!searchResults || !Array.isArray(searchResults)) {
            throw new MalouError(MalouErrorCode.PLATFORM_SEARCH_RESULT_NOT_ARRAY, { message: 'GMB' });
        }
        return searchResults
            .map((r) => {
                const address = {
                    regionCode: r.storefrontAddress?.regionCode,
                    country: r.storefrontAddress?.regionCode ? getCountryNameFrFromCountryCode(r.storefrontAddress.regionCode) : null,
                    postalCode: r.storefrontAddress?.postalCode,
                    locality: r.storefrontAddress?.locality,
                    formattedAddress: r.storefrontAddress?.addressLines?.join(', '),
                };
                return {
                    socialId: r.metadata?.placeId,
                    name: r.title,
                    formattedAddress:
                        [address.formattedAddress, address.postalCode, address.locality, address.country].filter((l) => !!l)?.join(' ') ||
                        undefined,
                    address: {
                        ...address,
                        country: address.country || undefined,
                    },
                    socialUrl: `${platformsKeys.GMB.externalReviewLink}${r.metadata?.placeId}`,
                    accountId: r.accountId,
                    accountName: r.accountName,
                    access: r.access,
                    apiEndpointV2: r.name,
                    locationId: r.name?.split('/')[1],
                };
            })
            .filter(isNotNil);
    }

    get mappingConfiguration() {
        // https://stackoverflow.com/questions/********/declaring-static-constants-in-es6-classes
        /**
         * This object defines everything for GMB:
         * - origin is Malou's field name
         * - gmbKey is GMB's corresponding field name
         * - toGmbFormat defines how we map Malou's format to GMB's
         * - toMalouFormat defines how we map GMB's format to Malou's
         */
        return {
            name: {
                malouKey: 'name',
                gmbKey: 'title',
                shouldBePublished: true,
            },
            rating: { malouKey: 'rating', gmbKey: 'rating', shouldBePublished: true },
            isClaimed: {
                malouKey: 'isClaimed',
                toMalouFormat: () => true,
                gmbKey: 'isClaimed',
                shouldBePublished: false,
            },
            menuUrl: {
                shouldBePublished: true,
                malouKey: 'menuUrl',
                toMalouFormat: (data: Partial<{ attributes: GmbAttribute[] }>): IRestaurant['menuUrl'] => {
                    if (!data.attributes || !data.attributes.find((a) => a.name.split('/')[1] === GmbAttributesEnum.URL_MENU)) {
                        return null;
                    }

                    const urls = data.attributes.find((a) => a.name.split('/')[1] === GmbAttributesEnum.URL_MENU)?.uriValues;
                    if (!urls?.[0]?.uri) {
                        return null;
                    }
                    if (!isValidUrl(urls[0].uri, { allowEmpty: true })) {
                        return null;
                    }
                    return urls[0].uri;
                },
                gmbKey: 'attributes',
                toGmbFormat: (menuUrl: string): GmbAttribute[] => {
                    // todo remove ? i think we never have null or undefined values here
                    if (isNil(menuUrl)) {
                        return [];
                    }

                    // If url is empty, it means the value was unset
                    // So we keep it in the attributes mask but with an empty value
                    return [
                        {
                            name: `attributes/${GmbAttributesEnum.URL_MENU}`,
                            uriValues: menuUrl !== '' ? [{ uri: menuUrl }] : [],
                        },
                    ];
                },
            },
            orderUrl: {
                shouldBePublished: true,
                malouKey: 'orderUrl',
                toMalouFormat: (data: Partial<{ attributes: GmbAttribute[] }>): IRestaurant['orderUrl'] => {
                    if (!data.attributes || !data.attributes.find((a) => a.name.split('/')[1] === GmbAttributesEnum.URL_ORDER_AHEAD)) {
                        return null;
                    }

                    const urls = data.attributes.find((a) => a.name.split('/')[1] === GmbAttributesEnum.URL_ORDER_AHEAD)?.uriValues;
                    if (!urls?.[0]?.uri) {
                        return null;
                    }
                    if (!isValidUrl(urls[0].uri, { allowEmpty: true })) {
                        return null;
                    }

                    return urls[0].uri;
                },
                gmbKey: 'attributes',
                toGmbFormat: (orderUrl: string): GmbAttribute[] => {
                    // todo remove ? i think we never have null or undefined values here
                    if (isNil(orderUrl)) {
                        return [];
                    }

                    // If url is empty, it means the value was unset
                    // So we keep it in the attributes mask but with an empty value. We remove the field in case a valid field was set before, we want the gmb infos to be synced with what we have in the app
                    if (orderUrl === '') {
                        return [
                            {
                                name: `attributes/${GmbAttributesEnum.URL_ORDER_AHEAD}`,
                                uriValues: [],
                            },
                        ];
                    }

                    return [
                        {
                            name: `attributes/${GmbAttributesEnum.URL_ORDER_AHEAD}`,
                            uriValues: [{ uri: orderUrl }],
                        },
                    ];
                },
            },
            reservationUrl: {
                shouldBePublished: true,
                malouKey: 'reservationUrl',
                toMalouFormat: (data: Partial<{ attributes: GmbAttribute[] }>): IRestaurant['reservationUrl'] => {
                    if (!data.attributes || !data.attributes.find((a) => a.name.split('/')[1] === GmbAttributesEnum.URL_RESERVATIONS)) {
                        return null;
                    }

                    const urls = data.attributes.find((a) => a.name.split('/')[1] === GmbAttributesEnum.URL_RESERVATIONS)?.uriValues;

                    if (!urls?.[0]?.uri) {
                        return null;
                    }
                    if (!isValidUrl(urls[0].uri, { allowEmpty: true })) {
                        return null;
                    }
                    return urls[0].uri;
                },
                gmbKey: 'attributes',
                toGmbFormat: (reservationUrl: string): GmbAttribute[] => {
                    // todo remove ? i think we never have null or undefined values here
                    if (isNil(reservationUrl)) {
                        return [];
                    }

                    // If url is empty, it means the value was unset
                    // So we keep it in the attributes mask but with an empty value. We remove the field in case a valid field was set before, we want the gmb infos to be synced with what we have in the app
                    if (reservationUrl === '') {
                        return [
                            {
                                name: `attributes/${GmbAttributesEnum.URL_RESERVATIONS}`,
                                uriValues: [],
                            },
                        ];
                    }

                    // If url is empty, it means the value was unset
                    // So we keep it in the attributes mask but with an empty value
                    return [
                        {
                            name: `attributes/${GmbAttributesEnum.URL_RESERVATIONS}`,
                            uriValues: [{ uri: reservationUrl }],
                        },
                    ];
                },
            },
            socialNetworkUrls: {
                shouldBePublished: true,
                malouKey: 'socialNetworkUrls',
                toMalouFormat: (data: Partial<{ attributes: GmbAttribute[] }>): IRestaurant['socialNetworkUrls'] => {
                    if (!data.attributes) {
                        return [];
                    }

                    const socialNetworkUrlAttributes = filter(data.attributes, ({ name }) =>
                        [
                            GmbAttributesEnum.URL_FACEBOOK,
                            GmbAttributesEnum.URL_INSTAGRAM,
                            GmbAttributesEnum.URL_LINKEDIN,
                            GmbAttributesEnum.URL_PINTEREST,
                            GmbAttributesEnum.URL_TIKTOK,
                            GmbAttributesEnum.URL_TWITTER,
                            GmbAttributesEnum.URL_YOUTUBE,
                        ].includes(name.split('/')[1] as GmbAttributesEnum)
                    );

                    if (socialNetworkUrlAttributes.length === 0) {
                        return [];
                    }

                    const socialNetworkUrls = socialNetworkUrlAttributes.map(({ name, uriValues }) => {
                        const gmbAttribute = name.split('/')[1];
                        if (!gmbAttribute) {
                            return null;
                        }
                        const key = {
                            [GmbAttributesEnum.URL_FACEBOOK]: SocialNetworkKey.FACEBOOK,
                            [GmbAttributesEnum.URL_INSTAGRAM]: SocialNetworkKey.INSTAGRAM,
                            [GmbAttributesEnum.URL_LINKEDIN]: SocialNetworkKey.LINKEDIN,
                            [GmbAttributesEnum.URL_PINTEREST]: SocialNetworkKey.PINTEREST,
                            [GmbAttributesEnum.URL_TIKTOK]: SocialNetworkKey.TIKTOK,
                            [GmbAttributesEnum.URL_TWITTER]: SocialNetworkKey.X,
                            [GmbAttributesEnum.URL_YOUTUBE]: SocialNetworkKey.YOUTUBE,
                        }[gmbAttribute];

                        if (!key) {
                            return null;
                        }

                        const url = !uriValues?.[0]?.uri ? '' : isValidUrl(uriValues[0].uri) ? uriValues[0].uri : '';

                        return {
                            key,
                            url,
                        };
                    });

                    return socialNetworkUrls.filter(isNotNil);
                },
                gmbKey: 'attributes',
                toGmbFormat: (socialNetworkUrls: IRestaurant['socialNetworkUrls']): GmbAttribute[] => {
                    // todo remove ? i think we never have null or undefined values here
                    if (!socialNetworkUrls) {
                        return [];
                    }

                    // We're mapping all the social network keys. If a social network is not present, we should keep it for the attributes mask but with an empty value to remove it from gmb attributes
                    const gmbAttributes = Object.values(SocialNetworkKey).map((key) => {
                        const gmbAttribute = {
                            [SocialNetworkKey.FACEBOOK]: GmbAttributesEnum.URL_FACEBOOK,
                            [SocialNetworkKey.INSTAGRAM]: GmbAttributesEnum.URL_INSTAGRAM,
                            [SocialNetworkKey.LINKEDIN]: GmbAttributesEnum.URL_LINKEDIN,
                            [SocialNetworkKey.PINTEREST]: GmbAttributesEnum.URL_PINTEREST,
                            [SocialNetworkKey.TIKTOK]: GmbAttributesEnum.URL_TIKTOK,
                            [SocialNetworkKey.X]: GmbAttributesEnum.URL_TWITTER,
                            [SocialNetworkKey.YOUTUBE]: GmbAttributesEnum.URL_YOUTUBE,
                        }[key];
                        const url = socialNetworkUrls.find((socialNetworkUrl) => socialNetworkUrl.key === key)?.url;

                        return {
                            name: `attributes/${gmbAttribute}`,
                            uriValues: url ? [{ uri: url }] : [],
                        };
                    });

                    return gmbAttributes;
                },
            },
            placeId: {
                shouldBePublished: true,
                malouKey: 'placeId',
                toMalouFormat: (data) => {
                    if (!data.metadata || !data.metadata.placeId) {
                        throw new MalouError(MalouErrorCode.PLATFORM_MAPPER_MISSING_PARAM, {
                            message: 'placeId is required for any malou restaurant in gmbMapper',
                        });
                    }
                    return data.metadata.placeId;
                },
                gmbKey: 'metadata.placeId',
            },
            latlng: {
                // https://developers.google.com/my-business/reference/businessinformation/rest/v1/ErrorCode?hl=fr
                // LAT_LNG_UPDATES_NOT_PERMITTED
                shouldBePublished: false,
                malouKey: 'latlng',
                toMalouFormat: async (data: { latlng?: { latitude: number; longitude: number } }) => {
                    if (!data.latlng) {
                        const geoCodedData = await getGeolocationForRestaurant(data);
                        return { lat: geoCodedData.latitude, lng: geoCodedData.longitude };
                    }
                    return { lat: data.latlng.latitude, lng: data.latlng.longitude };
                },
                gmbKey: 'latlng',
                toGmbFormat: (data) => {
                    if (!data.lat || !data.lng) {
                        return null;
                    }

                    return {
                        latitude: data.lat,
                        longitude: data.lng,
                    };
                },
            },
            phone: {
                shouldBePublished: true,
                malouKey: 'phone',
                toMalouFormat: (data) => {
                    if (!data.phoneNumbers || !data.phoneNumbers.primaryPhone) {
                        return {
                            prefix: null,
                            digits: null,
                        };
                    }
                    const phoneString = data.phoneNumbers.primaryPhone.replace(/\s/g, ''); // .slice(1);
                    const country = COUNTRY_CODES.find((c) => c === data.storefrontAddress?.regionCode) || CountryCode.FRANCE;
                    return mapToMalouPhone(phoneString, country);
                },
                gmbKey: 'phoneNumbers',
                toGmbFormat: (el) => {
                    if (!el || !el.prefix || !el.digits) {
                        return { primaryPhone: '' };
                    }
                    return {
                        primaryPhone: `+${el.prefix}${el.digits}`,
                        additionalPhones: [],
                    };
                },
            },
            descriptions: {
                shouldBePublished: true,
                malouKey: 'descriptions',
                toMalouFormat: (data) => {
                    if (!data.profile || !data.profile.description) {
                        return [];
                    }
                    return [
                        {
                            text: data.profile.description,
                            active: true,
                            language: Mapper.getDescriptionLanguage(data.profile.description),
                            size: descriptionSize.LONG.key,
                        },
                    ];
                },
                gmbKey: 'profile.description',
                toGmbFormat: (el) => {
                    if (!el) {
                        return null;
                    }
                    const activeDesc = el.find((elt) => elt.size === descriptionSize.LONG.key && elt.text?.length);
                    if (!activeDesc) {
                        return null;
                    }
                    return activeDesc.text;
                },
            },
            website: {
                shouldBePublished: true,
                malouKey: 'website',
                toMalouFormat: (data) => data.websiteUri,
                gmbKey: 'websiteUri',
            },
            openingDate: {
                shouldBePublished: true,
                malouKey: 'openingDate',
                toMalouFormat: (data) => {
                    if (isNil(data.openInfo?.openingDate)) {
                        return null;
                    }

                    const openingMonth = (data.openInfo.openingDate.month ?? 1) - 1;
                    const openingDay = data.openInfo.openingDate.day ?? 1;

                    const openingDate = new Date(Date.UTC(data.openInfo.openingDate.year, openingMonth, openingDay));
                    return !isNaN(openingDate.getTime()) ? openingDate : null;
                },
                gmbKey: 'openInfo.openingDate',
                toGmbFormat: (el) => {
                    if (!el) {
                        return null;
                    }
                    let date;
                    if (typeof el === 'string') {
                        date = new Date(el);
                    } else if (!isNaN(el.getTime())) {
                        date = el;
                    } else {
                        throw new MalouError(MalouErrorCode.PLATFORM_MAPPER_CANNOT_PARSE_DATA, {
                            message: 'Cannot parse date in gmbMapper',
                        });
                    }
                    return { year: date.getFullYear(), month: date.getMonth() + 1, day: date.getDate() }; // we need to offset month to match gmb format
                },
            },
            address: {
                shouldBePublished: true,
                malouKey: 'address',
                toMalouFormat: (data) => {
                    if (!data.storefrontAddress) {
                        throw new MalouError(MalouErrorCode.PLATFORM_MAPPER_MISSING_PARAM, { message: 'address is required in gmbMapper' });
                    }
                    const address = this.mapAddressToOrigin(data.storefrontAddress);
                    return address;
                }, // todo: not implemented yet
                gmbKey: 'storefrontAddress',
                toGmbFormat: (el) => {
                    // all locations must have an address - can't be null
                    if (!el) {
                        throw new MalouError(MalouErrorCode.PLATFORM_MAPPER_MISSING_PARAM, { message: 'address is required in gmbMapper' });
                    }
                    const address = this.mapAddressToTarget(el);
                    return address;
                },
            },
            attributeList: {
                shouldBePublished: true,
                malouKey: 'attributeList',
                toMalouFormat: async (data) => {
                    const { attributes } = data;
                    if (!attributes) {
                        return [];
                    }
                    try {
                        return this.mapGmbAttributesToRestaurantAttributes(attributes); // data aren't linked to restaurant yet, need restaurantId before
                    } catch (e) {
                        logger.warn('[GMB_MAPPER_ATTRIBUTE_ERROR]', e);
                    }
                },
                gmbKey: 'attributes',
                toGmbFormat: (attributes: IRestaurantAttributeWithAttribute[]): GmbAttribute[] => this.getGmbAttributesList(attributes),
            },
            category: {
                shouldBePublished: true,
                malouKey: 'category',
                toMalouFormat: async (data) => {
                    const { primaryCategory, additionalCategories } = data.categories;
                    if (!primaryCategory) {
                        throw new MalouError(MalouErrorCode.PLATFORM_MAPPER_MISSING_PARAM, { message: 'Category is needed in gmbMapper' });
                    }

                    const categories: { categoryId: string; categoryName: string }[] = [primaryCategory, ...(additionalCategories ?? [])]
                        // Format for GMB category names is categories/gcid:xxx
                        .map((cat) => ({
                            categoryId: cat.name?.split('/')[1],
                            categoryName: cat.displayName,
                        }))
                        .filter(({ categoryId }) => !!categoryId);

                    await this._addOrUpdateCategoriesService.execute({
                        categories,
                        platformKey: PlatformKey.GMB,
                    });

                    const principalCategory = await this._categoriesRepository.findOne({
                        filter: {
                            categoryId: primaryCategory.name?.split('/')[1],
                            platformKey: PlatformKey.GMB,
                        },
                        options: { lean: true },
                    });

                    return principalCategory;
                },
                gmbKey: 'categories',
                toGmbFormat: async (el: ICategory, restaurantId: ID) => {
                    if (!el || !el.categoryName || !el.categoryId) {
                        throw new MalouError(MalouErrorCode.PLATFORM_MAPPER_MISSING_PARAM, {
                            message: `Category is needed in gmbMapper`,
                            metadata: { data: String(el) },
                        });
                    }
                    const restaurant = await this._restaurantsRepository.findOne({
                        filter: { _id: restaurantId },
                        options: { populate: [{ path: 'categoryList' }], lean: true },
                    });
                    const categories = restaurant?.categoryList;
                    return {
                        primaryCategory: {
                            displayName: el.categoryName.backup,
                            name: `categories/${el.categoryId}`,
                        },
                        additionalCategories: categories?.map((cat) => ({
                            displayName: cat.categoryName.backup,
                            name: `categories/${cat.categoryId}`,
                        })),
                    };
                },
            },
            categoryList: {
                shouldBePublished: true,
                malouKey: 'categoryList',
                toMalouFormat: async (data) => {
                    const { additionalCategories } = data.categories;
                    if (!additionalCategories) {
                        return [];
                    }
                    const gmbCats = await this._categoriesRepository.find({
                        filter: {
                            platformKey: PlatformKey.GMB,
                            categoryId: { $in: additionalCategories.map((el) => el.name?.split('/')[1]) },
                        },
                        options: { lean: true },
                    });

                    return gmbCats;
                },
                gmbKey: 'categories',
                toGmbFormat: async (data, restaurantId) => {
                    const restaurant = await this._restaurantsRepository.findOne({
                        filter: { _id: restaurantId },
                        options: { populate: [{ path: 'category' }], lean: true },
                    });
                    const category = restaurant?.category;

                    return {
                        primaryCategory: {
                            displayName: category?.categoryName.backup,
                            name: `categories/${category?.categoryId}`,
                        },
                        additionalCategories: data.map((cat) => ({
                            displayName: cat.categoryName.backup,
                            name: `categories/${cat.categoryId}`,
                        })),
                    };
                },
            },
            regularHours: {
                shouldBePublished: true,
                malouKey: 'regularHours',
                toMalouFormat: (data) => {
                    if (!data.regularHours) {
                        return null;
                    }
                    const regularHours = this.mapRegularHours(data.regularHours.periods);
                    return this.addClosedDays(regularHours);
                },
                // https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#Location.BusinessHours
                gmbKey: 'regularHours.periods',
                toGmbFormat: (regularHours) => {
                    if (!regularHours) {
                        return [];
                    }
                    return this._formatHoursToGmbFormat(regularHours);
                },
            },
            specialHours: {
                shouldBePublished: true,
                malouKey: 'specialHours',
                toMalouFormat: (data) => {
                    if (!data.specialHours || !data.specialHours.specialHourPeriods || data.specialHours.specialHourPeriods.length === 0) {
                        return [];
                    }
                    return this.mapSpecialHoursToOrigin(data.specialHours);
                },
                gmbKey: 'specialHours.specialHourPeriods',
                toGmbFormat: (el) => {
                    if (!el) {
                        return [];
                    }
                    return this.mapSpecialHoursToTarget(el);
                },
            },
            isClosedTemporarily: {
                shouldBePublished: true,
                malouKey: 'isClosedTemporarily',
                toMalouFormat: (data) => {
                    if (data.openInfo.status === OpenForBusiness.OPEN) {
                        return false;
                    }
                    if (data.openInfo.status === OpenForBusiness.CLOSED_TEMPORARILY) {
                        return true;
                    }
                    return null;
                },
                gmbKey: 'openInfo.status',
                toGmbFormat: (isClosedTemporarily) => (isClosedTemporarily ? GmbOpeningStatus.CLOSED_TEMPORARILY : GmbOpeningStatus.OPEN),
            },
            menu: {
                shouldBePublished: false,
                malouKey: 'menu',
                toMalouFormat: (data) => {
                    if (!data.priceLists || !data.priceLists.find((el) => el.labels[0].displayName === 'Menu')) {
                        return null;
                    } // (Deprecated): doesn't exist anymore
                    const menuPriceList = data.priceLists.find((el) => el.labels[0].displayName === 'Menu');
                    const malouMenu = {
                        sections: menuPriceList.sections.map((s) => ({
                            label: s.labels[0].displayName,
                            socialId: s.sectionId,
                            items: s.items.map((i) => ({
                                label: i.labels[0].displayName,
                                description: i.labels[0].description,
                                socialId: i.itemId,
                                price: i.price,
                            })),
                        })),
                        socialId: menuPriceList.priceListId,
                    };
                    return malouMenu;
                },
                gmbKey: 'priceLists',
                toGmbFormat: (el) => {
                    if (!el) {
                        return null;
                    }
                    const gmbMenu = [
                        {
                            priceListId: el.socialId,
                            labels: [{ displayName: 'Menu' }],
                            sections: el.sections.map((s) => ({
                                sectionId: s.socialId,
                                labels: [{ displayName: s.label }],
                                sectionType: GmbSectionType.FOOD,
                                items: s.items.map((i) => ({
                                    itemId: i.socialId,
                                    labels: [{ displayName: i.label, description: i.description }],
                                    price: i.price,
                                })),
                            })),
                        },
                    ];
                    return gmbMenu;
                },
            },
            availableHoursTypeIds: {
                shouldBePublished: false,
                malouKey: 'availableHoursTypeIds',
                toMalouFormat: async (data) => {
                    const { primaryCategory, additionalCategories } = data.categories;
                    if (!primaryCategory) {
                        throw new MalouError(MalouErrorCode.PLATFORM_MAPPER_MISSING_PARAM, { message: 'Category is needed in gmbMapper' });
                    }

                    const otherHoursTypes = (primaryCategory.moreHoursTypes ?? []).map((moreHoursType) => moreHoursType.hoursTypeId);
                    (additionalCategories ?? []).forEach((category) => {
                        const moreHoursTypes = (category.moreHoursTypes ?? []).map((moreHoursType) => moreHoursType.hoursTypeId);
                        otherHoursTypes.push(...moreHoursTypes);
                    });
                    const hoursTypes = await this._hourTypesRepository.find({
                        filter: { hoursType: { $in: [...new Set(otherHoursTypes)] } },
                        projection: { _id: 1, hoursType: 1 },
                        options: { lean: true },
                    });
                    return hoursTypes?.map((hoursType) => hoursType._id);
                },
                gmbKey: 'availableHoursTypeIds',
            },
            otherHours: {
                shouldBePublished: true,
                malouKey: 'otherHours',
                toMalouFormat: (data) => {
                    if (!data.moreHours || data.moreHours.length === 0) {
                        return [];
                    }
                    return this.mapOtherHoursToOrigin(data.moreHours);
                },
                gmbKey: 'moreHours',
                toGmbFormat: async (el: IRestaurantOtherHour[]) => {
                    if (!el) {
                        return [];
                    }
                    return this.mapOtherHoursToTarget(el);
                },
            },
        } as const satisfies Record<
            string,
            { shouldBePublished: boolean; malouKey: string; gmbKey: string; toGmbFormat?: Function; toMalouFormat?: Function }
        >;
    }

    dateToSpecialHourPeriod(
        hour: IRestaurantSpecialHour,
        startDate: IRestaurantSpecialHourDate,
        endDate: IRestaurantSpecialHourDate
    ): GmbSpecialHourPeriod {
        return {
            openTime: hour.openTime ? this.formatHourStringToGmbTimeOfDay(hour.openTime) : undefined,
            closeTime: hour.closeTime ? this.formatHourStringToGmbTimeOfDay(hour.closeTime) : undefined,
            closed: hour.isClosed,
            startDate,
            endDate,
        };
    }

    toGmbDate(date: Date): IRestaurantSpecialHourDate {
        return { day: date.getDate(), month: date.getMonth(), year: date.getFullYear() };
    }

    private _formatGmbTimeOfDayToHourString(timeOfDay: GmbTimeOfDay): string {
        return `${this._pad(timeOfDay?.hours, 2)}:${this._pad(timeOfDay?.minutes, 2)}`;
    }

    formatTimePeriodToMalouPeriod(hour: GmbRegularHour): Omit<Partial<IRestaurantRegularHour>, 'isClosed'> | null {
        const openTime = hour.openTime ? this._formatGmbTimeOfDayToHourString(hour.openTime) : undefined;
        const closeTime = hour.closeTime ? this._formatGmbTimeOfDayToHourString(hour.closeTime) : undefined;
        const openDay = hour.openDay ? this.mapGmbDayToDay(hour.openDay) : undefined;
        const closeDay = hour.closeDay ? this.mapGmbDayToDay(hour.closeDay) : undefined;
        if (!openDay || !closeDay) {
            return null;
        }
        return { openDay, closeDay, openTime, closeTime };
    }

    mapGmbDayToDay(day: GmbDayOfWeek): Day | undefined {
        return Day[day];
    }

    formatHourStringToGmbTimeOfDay(hourTime: string): GmbTimeOfDay {
        return {
            hours: parseInt(hourTime?.split(':')[0], 10) ?? 0,
            minutes: parseInt(hourTime?.split(':')[1], 10) ?? 0,
        };
    }

    private _formatMalouPeriodToTimePeriod(hour: IRestaurantRegularHour): GmbRegularHour | null {
        const openTime = hour.openTime ? this.formatHourStringToGmbTimeOfDay(hour.openTime) : undefined;
        const closeTime = hour.closeTime ? this.formatHourStringToGmbTimeOfDay(hour.closeTime) : undefined;
        const openDay = this.mapDayToGmbDay(hour.openDay);
        const closeDay = this.mapDayToGmbDay(hour.closeDay);
        if (!openDay || !closeDay) {
            return null;
        }
        return { openDay, closeDay, openTime, closeTime };
    }

    mapDayToGmbDay(day: Day): GmbDayOfWeek | undefined {
        switch (day) {
            case Day.MONDAY:
                return GmbDayOfWeek.MONDAY;
            case Day.TUESDAY:
                return GmbDayOfWeek.TUESDAY;
            case Day.WEDNESDAY:
                return GmbDayOfWeek.WEDNESDAY;
            case Day.THURSDAY:
                return GmbDayOfWeek.THURSDAY;
            case Day.FRIDAY:
                return GmbDayOfWeek.FRIDAY;
            case Day.SATURDAY:
                return GmbDayOfWeek.SATURDAY;
            case Day.SUNDAY:
                return GmbDayOfWeek.SUNDAY;
            default:
                return undefined;
        }
    }

    mapRegularHours(regularHours: GmbRegularHour[]): Omit<Partial<IRestaurantRegularHour>, 'isClosed'>[] {
        return (
            regularHours
                .map((hour) => this.formatTimePeriodToMalouPeriod(hour))
                .filter(isNotNil)
                .filter((hour) => hour.openTime !== '00:00')
                .map((hour) => {
                    // Google separates before and after midnight periods: concat for Malou format
                    if (hour.closeTime === '24:00') {
                        for (let i = 0; i < regularHours.length; i += 1) {
                            const day = regularHours[i].openDay;
                            const previousDay = day ? this.getPreviousDay(day) : null; // search periods of closing at 24:00 and opening at 00:00 the next day
                            const previousRegularHour = this.formatTimePeriodToMalouPeriod(regularHours[i]);
                            if (
                                previousDay &&
                                previousRegularHour &&
                                hour.closeDay === previousDay &&
                                previousRegularHour.openTime === '00:00'
                            ) {
                                hour.closeTime = previousRegularHour.closeTime;
                                hour.closeDay = previousRegularHour.closeDay;
                                break;
                            }
                        }
                    }
                    return hour;
                })
                // .filter(hour => hour.openTime !== '00:00') // todo : make change to take into consideration restaurants opening at midgnight ?
                .map((hour) => ({ ...hour, isClosed: false }))
        );
    }

    getPreviousDay(day: GmbDayOfWeek): Day | null {
        switch (day) {
            case GmbDayOfWeek.MONDAY:
                return Day.SUNDAY;
            case GmbDayOfWeek.TUESDAY:
                return Day.MONDAY;
            case GmbDayOfWeek.WEDNESDAY:
                return Day.TUESDAY;
            case GmbDayOfWeek.THURSDAY:
                return Day.WEDNESDAY;
            case GmbDayOfWeek.FRIDAY:
                return Day.THURSDAY;
            case GmbDayOfWeek.SATURDAY:
                return Day.FRIDAY;
            case GmbDayOfWeek.SUNDAY:
                return Day.SATURDAY;
            default:
                return null;
        }
    }

    addClosedDays(regularHours: any[]): IRestaurantRegularHour[] {
        DAYS.forEach((d) => {
            const matchingDay = regularHours.find((p) => p.openDay === d);
            if (matchingDay) {
                return;
            }
            // push only results for closed days
            regularHours.push({ openDay: d, closeDay: d, isClosed: true });
        });
        return regularHours;
    }

    // See: https://developers.google.com/my-business/reference/businessinformation/rest/v1/accounts.locations#timeperiod
    private _formatHoursToGmbFormat(regularHours: IRestaurantRegularHour[]): GmbRegularHour[] {
        return (
            regularHours
                // Only open hours are relevant
                .filter((regularHour) => !regularHour.isClosed)
                // Map to GMB format
                .map((hour) => this._formatMalouPeriodToTimePeriod(hour))
                .filter(isNotNil)
        );
    }

    mapSpecialHourDateFromGmbToMalou(specialHourDate: IRestaurantSpecialHourDate): IRestaurantSpecialHourDate {
        // month - 1 because google does not follow standard Date format (month starts at 1 for them)
        return {
            day: specialHourDate.day,
            month: specialHourDate.month - 1,
            year: specialHourDate.year,
        };
    }

    mapSpecialHourDateFromMalouToGmb(specialHourDate: IRestaurantSpecialHourDate): IRestaurantSpecialHourDate {
        // month +1 because google does not follow standard Date format (month starts at 1 for them)
        return {
            day: specialHourDate.day,
            month: specialHourDate.month + 1,
            year: specialHourDate.year,
        };
    }

    /**
     *
     * @param {*} gmbDates Gmb format dates
     * from gmb to MalouApp format
     */
    mapSpecialHoursToOrigin(specialHours: GmbSpecialHours): IRestaurantSpecialHour[] {
        return specialHours.specialHourPeriods
            .map((hour) => {
                const startDate = hour.startDate ? this.mapSpecialHourDateFromGmbToMalou(hour.startDate) : null;
                const endDate = hour.endDate ? this.mapSpecialHourDateFromGmbToMalou(hour.endDate) : startDate;

                return startDate && endDate
                    ? {
                          startDate,
                          endDate,
                          isClosed: hour.closed ?? false,
                          openTime: hour.openTime ? this._formatGmbTimeOfDayToHourString(hour.openTime) : '00:00',
                          closeTime: hour.closeTime ? this._formatGmbTimeOfDayToHourString(hour.closeTime) : '00:00',
                      }
                    : null;
            })
            .filter(isNotNil);
    }

    /**
     *
     * @param {*} malouAppDates Malou's format dates
     * from MalouApp to gmb format
     */

    mapSpecialHoursToTarget(malouAppDates: IRestaurantSpecialHour[]): GmbSpecialHourPeriod[] {
        const specialHourPeriods: GmbSpecialHourPeriod[] = [];
        malouAppDates.forEach((h) => {
            // month +1 because google does not follow standard Date format (month starts at 1 for them)
            const gmbFormatStartDate: IRestaurantSpecialHourDate = this.mapSpecialHourDateFromMalouToGmb(h.startDate);
            if (h.isClosed) {
                specialHourPeriods.push({ closed: true, startDate: gmbFormatStartDate });
            } else if (h.endDate) {
                const gmbFormatEndDate: IRestaurantSpecialHourDate = this.mapSpecialHourDateFromMalouToGmb(h.endDate);
                specialHourPeriods.push(this.dateToSpecialHourPeriod(h, gmbFormatStartDate, gmbFormatEndDate));
            }
        });

        return specialHourPeriods;
    }

    /**
     *
     * @param {*} gmbAttributes Gmb attributes
     * from gmb to Malou format as RestaurantAttribute
     */
    mapGmbAttributesToRestaurantAttributes(gmbAttributes: GmbAttribute[]) {
        if (!gmbAttributes) {
            return;
        }

        const promises: Promise<PopulateBuilderHelper<IAttribute, [{ path: 'platformAttributes' }]> | null>[] = [];
        const valuesList: ('yes' | 'no')[] = [];
        gmbAttributes.forEach((attribute) => {
            // AttributeValueType: https://developers.google.com/my-business/reference/businessinformation/rest/v1/AttributeValueType
            switch (attribute.valueType) {
                case 'REPEATED_ENUM':
                    if (attribute.repeatedEnumValue?.setValues) {
                        attribute.repeatedEnumValue.setValues.forEach((enumValuesTrue) => {
                            promises.push(this._findAttributeService.execute(enumValuesTrue));
                            valuesList.push('yes');
                        });
                    }
                    if (attribute.repeatedEnumValue?.unsetValues) {
                        attribute.repeatedEnumValue.unsetValues.forEach((enumValuesFalse) => {
                            promises.push(this._findAttributeService.execute(enumValuesFalse));
                            valuesList.push('no');
                        });
                    }
                    break;
                // The attribute has a predetermined list of available values that can be used
                case 'ENUM':
                    attribute.values?.forEach((valueId: GmbValue) => {
                        if (valueId) {
                            promises.push(this._findAttributeService.execute(valueId.toString()));
                            valuesList.push('yes');
                        }
                    });
                    break;
                case 'BOOL':
                    promises.push(this._findAttributeService.execute(attribute.name?.split('/')[1]));
                    const value = attribute.values?.length && attribute.values[0] ? 'yes' : 'no';
                    valuesList.push(value);
                    break;
                case 'URL':
                default:
                    break;
            }
        });

        return Promise.all(promises).then((attributeIdsList) => {
            const attributeIdsListFiltered = attributeIdsList.filter(isNotNil);
            const restaurantAttributesList: { attributeId: DbId; attributeValue: 'yes' | 'no' }[] = [];
            attributeIdsListFiltered.forEach((attribute, index) => {
                restaurantAttributesList.push({
                    attributeId: attribute?._id,
                    attributeValue: valuesList[index],
                });
            });
            return restaurantAttributesList;
        });
    }

    mapAddressToOrigin(targetAddress: GmbAddress): IRestaurantAddress | null {
        if (!targetAddress.regionCode) {
            return null;
        }
        const country = getCountryNameFrFromCountryCode(targetAddress.regionCode);
        const malouCountryCode = getMalouCountryCode(targetAddress.regionCode);

        return country && malouCountryCode
            ? {
                  country,
                  regionCode: malouCountryCode,
                  postalCode: targetAddress.postalCode,
                  locality: targetAddress.locality,
                  formattedAddress: targetAddress.addressLines?.join(', '),
                  administrativeArea: targetAddress.administrativeArea,
                  route: targetAddress.addressLines?.[0],
                  streetNumber: targetAddress.addressLines?.[0]?.split(' ')?.[0],
              }
            : null;
    }

    mapAddressToTarget(originAddress: IRestaurantAddress): GmbAddress {
        const targetAddress: GmbAddress = {
            regionCode: originAddress?.regionCode,
            postalCode: originAddress?.postalCode,
            locality: originAddress?.locality,
            addressLines: originAddress?.formattedAddress?.toLowerCase() ? [originAddress.formattedAddress.toLowerCase()] : undefined,
        };
        if (originAddress?.administrativeArea) {
            targetAddress.administrativeArea = originAddress.administrativeArea;
        }
        return targetAddress;
    }

    async mapOtherHoursToOrigin(otherHours: GmbMoreHours[]): Promise<IRestaurantOtherHour[]> {
        const hoursTypes = await this._hourTypesRepository.find({
            filter: { hoursType: { $in: otherHours.map((hour) => hour.hoursTypeId) } },
            projection: { _id: 1, hoursType: 1 },
            options: { lean: true },
        });
        return otherHours.reduce((acc, hour) => {
            const hoursType = hoursTypes?.find((hourType) => hourType.hoursType === hour.hoursTypeId);
            const regularHours = this.mapRegularHours(hour.periods);
            const newHour: IRestaurantOtherHour | undefined = hoursType
                ? {
                      hoursTypeId: hoursType._id.toString(),
                      periods: this.addClosedDays(regularHours),
                  }
                : undefined;
            if (newHour) {
                acc.push(newHour);
            }
            return acc;
        }, [] as IRestaurantOtherHour[]);
    }

    async mapOtherHoursToTarget(otherHours: IRestaurantOtherHour[]): Promise<GmbMoreHours[]> {
        const hoursTypes = await this._hourTypesRepository.find({
            filter: { _id: { $in: otherHours.map((hour) => hour.hoursTypeId) } },
            projection: { _id: 1, hoursType: 1 },
            options: { lean: true },
        });

        return otherHours
            .filter((otherHour) => otherHour?.periods?.length > 0)
            .filter((hour) => isNotNil(hoursTypes?.find((hourType) => hourType._id.toString() === hour.hoursTypeId.toString())))
            .map((otherHour) => {
                const hoursType = hoursTypes?.find((hourType) => hourType._id.toString() === otherHour.hoursTypeId.toString());
                return {
                    hoursTypeId: (hoursType as IHoursType).hoursType,
                    periods: this._formatHoursToGmbFormat(otherHour.periods),
                };
            });
    }

    // adds a 0 at the beginning of a number to get the right hours:minutes format
    private _pad(number: number, size: number): string {
        return padStart(number?.toString(), size, '0');
    }

    getGmbAttributesList(attributesList: IRestaurantAttributeWithAttribute[]): GmbAttribute[] {
        const gmbAttributes: GmbAttribute[] = [];

        if (!attributesList) {
            return gmbAttributes;
        }

        // Get GMB attribute related to Wifi
        const attributeForWifi = this.getGmbAttributeForWifi(attributesList);
        if (attributeForWifi) {
            gmbAttributes.push(attributeForWifi);
        }
        // Get GMB attribute related to Credit Cards
        const attributeForCreditCards = this.getGmbAttributeForCreditCards(attributesList);
        if (attributeForCreditCards) {
            gmbAttributes.push(attributeForCreditCards);
        }

        attributesList
            .filter(
                (attr) =>
                    ![
                        // Handled by getGmbAttributeForWifi
                        MalouAttributesEnum.FREE_WI_FI,
                        MalouAttributesEnum.PAID_WI_FI,
                        // Handled by getGmbAttributeForCreditCards
                        MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                        MalouAttributesEnum.AMERICAN_EXPRESS,
                        MalouAttributesEnum.CHINA_UNION_PAY,
                        MalouAttributesEnum.DINERS_CLUB,
                        MalouAttributesEnum.DISCOVER,
                        MalouAttributesEnum.JCB,
                    ].includes(attr?.attribute?.attributeId as MalouAttributesEnum)
            )
            .forEach((attribute) => {
                gmbAttributes.push({
                    name: attribute?.attribute?.attributeId,
                    values: [attribute.attributeValue === RestaurantAttributeValue.YES],
                });
            });

        return gmbAttributes
            .filter(isNotNil)
            .filter((attr) => attr.name)
            .map((attr) => ({ ...attr, name: `attributes/${attr.name}` }));
    }

    getGmbAttributeForWifi(attributes: IRestaurantAttributeWithAttribute[]): GmbAttribute | undefined {
        const values: string[] = [];

        attributes
            .filter((attribute) =>
                [MalouAttributesEnum.FREE_WI_FI, MalouAttributesEnum.PAID_WI_FI].includes(
                    attribute?.attribute?.attributeId as MalouAttributesEnum
                )
            )
            .forEach((attribute) => {
                if (attribute.attributeValue === RestaurantAttributeValue.YES) {
                    values.push(attribute?.attribute?.attributeId as MalouAttributesEnum);
                }
            });

        if (values.length === 0) {
            return undefined;
        }

        return {
            name: GmbAttributesEnum.WI_FI,
            values,
        };
    }

    getGmbAttributeForCreditCards(attributes: IRestaurantAttributeWithAttribute[]): GmbAttribute | undefined {
        const setValuesList: string[] = [];
        const unsetValuesList: string[] = [];

        attributes
            .filter((attribute) =>
                [
                    MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
                    MalouAttributesEnum.AMERICAN_EXPRESS,
                    MalouAttributesEnum.CHINA_UNION_PAY,
                    MalouAttributesEnum.DINERS_CLUB,
                    MalouAttributesEnum.DISCOVER,
                    MalouAttributesEnum.JCB,
                ].includes(attribute?.attribute?.attributeId as MalouAttributesEnum)
            )
            .forEach((attribute) => {
                const { setValues: attributeSetValues, unsetValues: attributeUnsetValues } =
                    this.getMappedAttributeForCreditCards(attribute);
                setValuesList.push(...(attributeSetValues ?? []));
                unsetValuesList.push(...(attributeUnsetValues ?? []));
            });

        if (setValuesList.length === 0 && unsetValuesList.length === 0) {
            return undefined;
        }

        return {
            name: GmbAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED,
            repeatedEnumValue: {
                setValues: setValuesList,
                unsetValues: unsetValuesList,
            },
        };
    }

    getMappedAttributeForCreditCards(attribute: IRestaurantAttributeWithAttribute): {
        setValues?: string[];
        unsetValues?: string[];
    } {
        const localAttributeName = attribute?.attribute?.attributeId;
        const isAttributeValueTrue = attribute.attributeValue === RestaurantAttributeValue.YES;
        if (localAttributeName === MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED) {
            if (isAttributeValueTrue) {
                return { setValues: ['visa', 'mastercard'] };
            }
            return { unsetValues: ['visa', 'mastercard'] };
        }
        if (isAttributeValueTrue) {
            return { setValues: [localAttributeName] };
        }
        return { unsetValues: [localAttributeName] };
    }
}
