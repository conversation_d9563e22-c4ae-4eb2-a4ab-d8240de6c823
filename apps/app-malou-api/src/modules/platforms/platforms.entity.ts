import { PlatformDto } from '@malou-io/package-dto';
import { DbId, ID, IPlatform } from '@malou-io/package-models';
import { changePlatformUrlDomain, getPlatformDefinition, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Address } from ':modules/restaurants/entities/address.entity';

export class Platform {
    _id: DbId;
    id?: string;
    key: PlatformKey;
    restaurantId: ID;
    socialId?: string;
    socialLink?: string;
    website?: string;
    credentials?: string[];
    lockedFields?: string[];
    apiEndpoint?: string;
    apiEndpointV2?: string;
    isClosedTemporarily?: boolean;
    hasTransitionedToNewPageExperience?: boolean;
    name?: string;
    platformPropertiesToUpdate?: string[];
    profilePictureUrl?: string;
    drnId?: string; // Only for Deliveroo, needed to fetch reviews without comments
    doordashBusinessId?: string; // Only for Doordash, needed to fetch data
    address?: Address;
    venueId?: string; // Only for SevenRooms, needed to fetch reviews

    constructor(data: IPlatform & { id?: string }) {
        this._id = data._id;
        this.id = data.id ?? data._id.toString();
        this.key = data.key;
        this.restaurantId = data.restaurantId;
        this.socialId = data.socialId ?? undefined;
        this.socialLink = data.socialLink ?? undefined;
        this.website = data.website ?? undefined;
        this.credentials = data.credentials?.map((credential) => credential.toString());
        this.lockedFields = data.lockedFields;
        this.apiEndpoint = data.apiEndpoint ?? undefined;
        this.apiEndpointV2 = data.apiEndpointV2;
        this.isClosedTemporarily = data.isClosedTemporarily ?? undefined;
        this.hasTransitionedToNewPageExperience = data.hasTransitionedToNewPageExperience;
        this.name = data.name ?? undefined;
        this.platformPropertiesToUpdate = data.platformPropertiesToUpdate;
        this.profilePictureUrl = data.profilePictureUrl ?? undefined;
        this.drnId = data.drnId;
        this.doordashBusinessId = data.doordashBusinessId;
        this.address = data.address ? new Address(data.address as Address) : undefined;
        this.venueId = data.venueId;
    }

    /**
     * Get the redirection URL for the platform review page
     */
    getRedirectionUrl = (langs?: string[]): string | undefined => {
        const link = getPlatformDefinition(this.key)?.externalReviewLink;
        switch (this.key) {
            case PlatformKey.GMB:
            case PlatformKey.YELP:
                return link + (this?.socialId ?? '');
            case PlatformKey.TRIPADVISOR:
                const tripAdvisorUrl = this.socialLink?.replace(/Restaurant_Review/, 'UserReviewEdit').replace(/Reviews-/, '');
                return langs ? changePlatformUrlDomain(tripAdvisorUrl, this.key, langs) : tripAdvisorUrl;
            case PlatformKey.FOURSQUARE:
            case PlatformKey.INSTAGRAM:
            case PlatformKey.FACEBOOK:
                return this.socialLink;
            default:
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: 'Platform not found',
                    metadata: { platformKey: this.key },
                });
        }
    };

    getSocialLink(): string | null {
        if (this.socialLink) {
            return this.socialLink;
        }

        const accessLink = getPlatformDefinition(this.key)?.accessLink;

        if (!accessLink || !this.socialId) {
            return null;
        }

        return accessLink(this.socialId);
    }

    toPartialDto(): Partial<PlatformDto> {
        return {
            id: this.id ?? this._id.toString(),
            key: this.key,
            restaurantId: this.restaurantId.toString(),
            socialId: this.socialId,
            socialLink: this.socialLink,
            website: this.website,
            credentials: this.credentials,
            lockedFields: this.lockedFields,
            apiEndpoint: this.apiEndpoint,
            apiEndpointV2: this.apiEndpointV2,
            isClosedTemporarily: this.isClosedTemporarily,
            hasTransitionedToNewPageExperience: this.hasTransitionedToNewPageExperience,
            name: this.name,
            platformPropertiesToUpdate: this.platformPropertiesToUpdate,
            profilePictureUrl: this.profilePictureUrl,
        };
    }
}
