import { Job } from 'agenda';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { AgendaJobData } from ':helpers/validators/jobs/job-data.types';
import { MatchReviewsToScansUseCase } from ':modules/reviews/use-cases/match-reviews-to-scans/match-reviews-to-scans.use-case';
import ScansRepository from ':modules/scans/repository/scans.repository';

/*
 * Time period in the past (starting from the current date at runtime) during which scans are still processed
 * even if their `isCheckedForReviewsMatching` boolean is set to true.
 * This matters for cases where a scan was already checked and no matching review was found at the time,
 * but the corresponding review is fetched and appears on the MalouApp at a later point.
 */
const PERIOD_TO_KEEP_PROCEEDING_SCANS_IN_DAYS = 7;
// For each run of this task, we will process scans from a time window of up to this many days.
// This helps ensure that each execution remains relatively fast and does not consume too many resources.
const MAX_PERIOD_BETWEEN_SCANS_BATCH_IN_DAYS = 7;

@singleton()
export class MatchReviewToScanJob extends GenericJobDefinition {
    constructor(
        private readonly _matchReviewsToScansUseCase: MatchReviewsToScansUseCase,
        private readonly _scansRepository: ScansRepository
    ) {
        super({
            agendaJobName: AgendaJobName.MATCH_REVIEW_TO_SCAN,
        });
    }

    async executeJob(job: Job<AgendaJobData[AgendaJobName.MATCH_REVIEW_TO_SCAN]>): Promise<void> {
        const minDateToBypassCheckFlag = DateTime.now().minus({ days: PERIOD_TO_KEEP_PROCEEDING_SCANS_IN_DAYS }).toJSDate();

        const minRedirectedAt = job.attrs?.data?.minRedirectedAt ?? (await this._getMinRedirectedAt(minDateToBypassCheckFlag));
        const maxRedirectedAt = job.attrs?.data?.maxRedirectedAt ?? this._getMaxRedirectedAt(minRedirectedAt);

        await this._matchReviewsToScansUseCase.execute({ minRedirectedAt, maxRedirectedAt }, minDateToBypassCheckFlag);
    }

    private async _getMinRedirectedAt(minDateToBypassCheckFlag: Date): Promise<Date> {
        const scansWithMinRedirectedAt = await this._scansRepository.find({
            filter: {
                matchedReviewSocialId: null,
                redirectedAt: { $exists: true },
                $or: [{ isCheckedForMatchingReview: { $ne: true } }, { redirectedAt: { $gte: minDateToBypassCheckFlag } }],
            },
            options: {
                sort: {
                    redirectedAt: 1,
                },
                limit: 1,
                lean: true,
            },
        });
        const minRedirectedAt: Date = scansWithMinRedirectedAt[0]?.redirectedAt ?? new Date();
        return minRedirectedAt;
    }

    private _getMaxRedirectedAt(minRedirectedAt: Date): Date {
        return DateTime.fromJSDate(minRedirectedAt).plus({ days: MAX_PERIOD_BETWEEN_SCANS_BATCH_IN_DAYS }).toJSDate();
    }
}
