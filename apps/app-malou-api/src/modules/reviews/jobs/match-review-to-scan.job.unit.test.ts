import { Job } from 'agenda';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { NfcType, PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MatchReviewToScanJob } from ':modules/reviews/jobs/match-review-to-scan.job';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { PERIOD_BETWEEN_SCAN_AND_REVIEW_TO_MATCH_IN_MINUTES } from ':modules/reviews/use-cases/match-reviews-to-scans/match-reviews-to-scans.use-case';
import ScansRepository from ':modules/scans/repository/scans.repository';
import { getDefaultNfcSnapshot, getDefaultScan } from ':modules/scans/tests/scans.builder';
import { getDefaultRestaurantWheelsOfFortune } from ':modules/wheels-of-fortune/tests/wheels-of-fortune.builder';

const DEFAULT_JOB = {
    attrs: {
        data: {},
    },
    fail: () => {
        console.log('fail');
    },
    remove: () => {
        console.log('remove');
    },
} as unknown as Job;

describe('MatchReviewToScanJob', () => {
    beforeAll(() => {
        registerRepositories(['ScansRepository', 'ReviewsRepository', 'WheelsOfFortuneRepository']);
    });

    describe('executeJob', () => {
        afterAll(() => {
            jest.resetAllMocks();
        });

        it('should update scan with reviewId if there is a match', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(new Date())
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [getDefaultReview().restaurantId(restaurantId).rating(rating).build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        matchedReviewSocialId: dependencies.reviews[0].socialId,
                        isCheckedForMatchingReview: true,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan).toEqual(expectedResult);
        });

        it('should not match with review if review is made before the scan', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now();
            const tenMinutesAgo = now.minus({ minutes: 10 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [getDefaultReview().restaurantId(restaurantId).rating(rating).socialCreatedAt(tenMinutesAgo).build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        isCheckedForMatchingReview: true,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan).toEqual(expectedResult);
        });

        it('should not match with review if review is made too long after the scan', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now();
            const tenMinutesAfterTheDelay = now.plus({ minutes: PERIOD_BETWEEN_SCAN_AND_REVIEW_TO_MATCH_IN_MINUTES + 10 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .rating(rating)
                                    .socialCreatedAt(tenMinutesAfterTheDelay)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        isCheckedForMatchingReview: true,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan).toEqual(expectedResult);
        });

        it('should match a review with only one scan', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now().toJSDate();
            const nowPlus1Minute = DateTime.now().plus({ minutes: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(nowPlus1Minute)
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .build(),
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now)
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [getDefaultReview().restaurantId(restaurantId).rating(rating).build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [
                        {
                            nfcId: dependencies.scans[0].nfcId,
                            scannedAt: dependencies.scans[0].scannedAt,
                            nfcSnapshot: {
                                _id: dependencies.scans[0].nfcSnapshot._id,
                                name: dependencies.scans[0].nfcSnapshot.name,
                                chipName: dependencies.scans[0].nfcSnapshot.chipName,
                                createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                                updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                                restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                                active: dependencies.scans[0].nfcSnapshot.active,
                                platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                                redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                                starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                                type: NfcType.TOTEM,
                            },
                            starClicked: dependencies.scans[0].starClicked,
                            redirectedAt: dependencies.scans[0].redirectedAt,
                            createdAt: dependencies.scans[0].createdAt,
                            isCheckedForMatchingReview: true,
                            matchedReviewSocialId: dependencies.reviews[0].socialId,
                        },
                        {
                            nfcId: dependencies.scans[1].nfcId,
                            scannedAt: dependencies.scans[1].scannedAt,
                            nfcSnapshot: {
                                _id: dependencies.scans[1].nfcSnapshot._id,
                                name: dependencies.scans[1].nfcSnapshot.name,
                                chipName: dependencies.scans[1].nfcSnapshot.chipName,
                                createdAt: dependencies.scans[1].nfcSnapshot.createdAt,
                                updatedAt: dependencies.scans[1].nfcSnapshot.updatedAt,
                                restaurantId: dependencies.scans[1].nfcSnapshot.restaurantId,
                                active: dependencies.scans[1].nfcSnapshot.active,
                                platformKey: dependencies.scans[1].nfcSnapshot.platformKey,
                                redirectionLink: dependencies.scans[1].nfcSnapshot.redirectionLink,
                                starsRedirected: dependencies.scans[1].nfcSnapshot.starsRedirected,
                                type: NfcType.TOTEM,
                            },
                            starClicked: dependencies.scans[1].starClicked,
                            redirectedAt: dependencies.scans[1].redirectedAt,
                            createdAt: dependencies.scans[1].createdAt,
                            isCheckedForMatchingReview: true,
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = await scansRepository.find({
                filter: {},
                projection: {
                    _id: 0,
                    __v: 0,
                    updatedAt: 0,
                },
                options: { lean: true, sort: { redirectedAt: 1 } },
            });
            expect(scan).toIncludeAllMembers(expectedResult);
        });

        it('should not match a review that is already matched with another scan', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const tenMinutesAgo = DateTime.now().minus({ minutes: 10 }).toJSDate();
            const now = DateTime.now().toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview().restaurantId(restaurantId).rating(rating).socialCreatedAt(tenMinutesAgo).build()];
                        },
                    },
                    scans: {
                        data(dependencies) {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(tenMinutesAgo)
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .isCheckedForMatchingReview(true)
                                    .matchedReviewSocialId(dependencies.reviews()[0].socialId)
                                    .build(),
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now)
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [
                        {
                            nfcId: dependencies.scans[0].nfcId,
                            scannedAt: dependencies.scans[0].scannedAt,
                            nfcSnapshot: {
                                _id: dependencies.scans[0].nfcSnapshot._id,
                                name: dependencies.scans[0].nfcSnapshot.name,
                                chipName: dependencies.scans[0].nfcSnapshot.chipName,
                                createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                                updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                                restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                                active: dependencies.scans[0].nfcSnapshot.active,
                                platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                                redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                                starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                                type: NfcType.TOTEM,
                            },
                            starClicked: dependencies.scans[0].starClicked,
                            redirectedAt: dependencies.scans[0].redirectedAt,
                            createdAt: dependencies.scans[0].createdAt,
                            matchedReviewSocialId: dependencies.scans[0].matchedReviewSocialId,
                            isCheckedForMatchingReview: true,
                        },
                        {
                            nfcId: dependencies.scans[1].nfcId,
                            scannedAt: dependencies.scans[1].scannedAt,
                            nfcSnapshot: {
                                _id: dependencies.scans[1].nfcSnapshot._id,
                                name: dependencies.scans[1].nfcSnapshot.name,
                                chipName: dependencies.scans[1].nfcSnapshot.chipName,
                                createdAt: dependencies.scans[1].nfcSnapshot.createdAt,
                                updatedAt: dependencies.scans[1].nfcSnapshot.updatedAt,
                                restaurantId: dependencies.scans[1].nfcSnapshot.restaurantId,
                                active: dependencies.scans[1].nfcSnapshot.active,
                                platformKey: dependencies.scans[1].nfcSnapshot.platformKey,
                                redirectionLink: dependencies.scans[1].nfcSnapshot.redirectionLink,
                                starsRedirected: dependencies.scans[1].nfcSnapshot.starsRedirected,
                                type: NfcType.TOTEM,
                            },
                            starClicked: dependencies.scans[1].starClicked,
                            redirectedAt: dependencies.scans[1].redirectedAt,
                            createdAt: dependencies.scans[1].createdAt,
                            isCheckedForMatchingReview: true,
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = await scansRepository.find({
                filter: {},
                projection: {
                    _id: 0,
                    __v: 0,
                    updatedAt: 0,
                },
                options: { lean: true, sort: { redirectedAt: 1 } },
            });
            expect(scan).toEqual(expectedResult);
        });

        it('should update scan with reviewId if there is a match - Tripadvisor review case', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(new Date())
                                    .nfcSnapshot(
                                        getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).platformKey(PlatformKey.TRIPADVISOR).build()
                                    )
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [getDefaultReview().key(PlatformKey.TRIPADVISOR).restaurantId(restaurantId).rating(rating).build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        matchedReviewSocialId: dependencies.reviews[0].socialId,
                        isCheckedForMatchingReview: true,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan).toEqual(expectedResult);
        });

        it('should not match with review if review is made before the scan - Tripadvisor review case', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now();
            const oneDayAgo = now.minus({ day: 10 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(
                                        getDefaultNfcSnapshot(nfcId).platformKey(PlatformKey.TRIPADVISOR).restaurantId(restaurantId).build()
                                    )
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .key(PlatformKey.TRIPADVISOR)
                                    .restaurantId(restaurantId)
                                    .rating(rating)
                                    .socialCreatedAt(oneDayAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        isCheckedForMatchingReview: true,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan).toEqual(expectedResult);
        });

        it('should not match with review if review is made too long after the scan - Tripadvisor review case', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now();
            const twoDaysAfterTheDelay = now.plus({ days: 2 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(
                                        getDefaultNfcSnapshot(nfcId).platformKey(PlatformKey.TRIPADVISOR).restaurantId(restaurantId).build()
                                    )
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .key(PlatformKey.TRIPADVISOR)
                                    .restaurantId(restaurantId)
                                    .rating(rating)
                                    .socialCreatedAt(twoDaysAfterTheDelay)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        isCheckedForMatchingReview: true,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan).toEqual(expectedResult);
        });

        it('should match review from Tripadvisor, with scan that is not from wheel of fortune, even if ratio has been reached for the day', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const scanId = newDbId();

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now();
            const today = now.setZone('utc').startOf('day').toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .key(PlatformKey.TRIPADVISOR)
                                    .socialId('review-social-id-1')
                                    .restaurantId(restaurantId)
                                    .rating(rating)
                                    .socialCreatedAt(today)
                                    .build(),
                                getDefaultReview()
                                    .key(PlatformKey.TRIPADVISOR)
                                    .socialId('review-social-id-2')
                                    .restaurantId(restaurantId)
                                    .rating(rating)
                                    .socialCreatedAt(today)
                                    .build(),
                            ];
                        },
                    },
                    scans: {
                        data(dependencies) {
                            return [
                                getDefaultScan()
                                    ._id(scanId)
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(
                                        getDefaultNfcSnapshot(nfcId).platformKey(PlatformKey.TRIPADVISOR).restaurantId(restaurantId).build()
                                    )
                                    .starClicked(rating)
                                    .build(),
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(
                                        getDefaultNfcSnapshot(nfcId).platformKey(PlatformKey.TRIPADVISOR).restaurantId(restaurantId).build()
                                    )
                                    .starClicked(rating)
                                    .isCheckedForMatchingReview(true)
                                    .matchedReviewSocialId(dependencies.reviews()[1].socialId)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        isCheckedForMatchingReview: true,
                        matchedReviewSocialId: dependencies.reviews[0].socialId,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = await scansRepository.findOne({
                filter: { _id: scanId },
                projection: {
                    _id: 0,
                    __v: 0,
                    updatedAt: 0,
                },
                options: { lean: true },
            });
            expect(scan).toStrictEqual(expectedResult);
        });

        it('should not match with review if review is from Tripadvisor, scan is from wheel of fortune, and ratio has been reached for the day', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now();
            const today = now.setZone('utc').startOf('day').toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews' | 'wheelsOfFortune'>({
                seeds: {
                    wheelsOfFortune: {
                        data() {
                            return [getDefaultRestaurantWheelsOfFortune().restaurantId(restaurantId).build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .key(PlatformKey.TRIPADVISOR)
                                    .restaurantId(restaurantId)
                                    .rating(rating)
                                    .socialCreatedAt(today)
                                    .build(),
                                getDefaultReview()
                                    .key(PlatformKey.TRIPADVISOR)
                                    .restaurantId(restaurantId)
                                    .rating(rating)
                                    .socialCreatedAt(today)
                                    .build(),
                            ];
                        },
                    },
                    scans: {
                        data(dependencies) {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(
                                        getDefaultNfcSnapshot(nfcId)
                                            .platformKey(PlatformKey.TRIPADVISOR)
                                            .restaurantId(restaurantId)
                                            .redirectionLink(
                                                // eslint-disable-next-line max-len
                                                `https://app.malou.io/wheel-of-fortune?wofId=${dependencies.wheelsOfFortune()[0]._id}&restaurantId=${restaurantId}&isFromTotem=true`
                                            )
                                            .build()
                                    )
                                    .starClicked(rating)
                                    .build(),
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(
                                        getDefaultNfcSnapshot(nfcId)
                                            .platformKey(PlatformKey.TRIPADVISOR)
                                            .restaurantId(restaurantId)
                                            .redirectionLink(
                                                // eslint-disable-next-line max-len
                                                `https://app.malou.io/wheel-of-fortune?wofId=${dependencies.wheelsOfFortune()[0]._id}&restaurantId=${restaurantId}&isFromTotem=true`
                                            )
                                            .build()
                                    )
                                    .starClicked(rating)
                                    .isCheckedForMatchingReview(true)
                                    .matchedReviewSocialId(dependencies.reviews()[1].socialId)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        nfcId: dependencies.scans[0].nfcId,
                        scannedAt: dependencies.scans[0].scannedAt,
                        nfcSnapshot: {
                            _id: dependencies.scans[0].nfcSnapshot._id,
                            name: dependencies.scans[0].nfcSnapshot.name,
                            chipName: dependencies.scans[0].nfcSnapshot.chipName,
                            createdAt: dependencies.scans[0].nfcSnapshot.createdAt,
                            updatedAt: dependencies.scans[0].nfcSnapshot.updatedAt,
                            restaurantId: dependencies.scans[0].nfcSnapshot.restaurantId,
                            active: dependencies.scans[0].nfcSnapshot.active,
                            platformKey: dependencies.scans[0].nfcSnapshot.platformKey,
                            redirectionLink: dependencies.scans[0].nfcSnapshot.redirectionLink,
                            starsRedirected: dependencies.scans[0].nfcSnapshot.starsRedirected,
                            type: NfcType.TOTEM,
                        },
                        starClicked: dependencies.scans[0].starClicked,
                        redirectedAt: dependencies.scans[0].redirectedAt,
                        createdAt: dependencies.scans[0].createdAt,
                        isCheckedForMatchingReview: true,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan).toStrictEqual(expectedResult);
        });

        it('should match with the review that is the closest to the scan', async () => {
            const scansJob = container.resolve(MatchReviewToScanJob);

            const restaurantId = newDbId();
            const nfcId = newDbId();
            const rating = 5;
            const now = DateTime.now();
            const inTenMinutes = now.plus({ minutes: 10 }).toJSDate();
            const inTwentyMinutes = now.plus({ minutes: 20 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
                seeds: {
                    scans: {
                        data() {
                            return [
                                getDefaultScan()
                                    .nfcId(nfcId)
                                    .redirectedAt(now.toJSDate())
                                    .nfcSnapshot(getDefaultNfcSnapshot(nfcId).restaurantId(restaurantId).build())
                                    .starClicked(rating)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview().restaurantId(restaurantId).rating(rating).socialCreatedAt(inTenMinutes).build(),
                                getDefaultReview().restaurantId(restaurantId).rating(rating).socialCreatedAt(inTwentyMinutes).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string {
                    return dependencies.reviews[0].socialId.toString();
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            await scansJob.executeJob(DEFAULT_JOB);

            const scansRepository = container.resolve(ScansRepository);
            const scan = (
                await scansRepository.find({
                    filter: {},
                    projection: {
                        _id: 0,
                        __v: 0,
                        updatedAt: 0,
                    },
                    options: { lean: true },
                })
            )[0];
            expect(scan.matchedReviewSocialId?.toString()).toEqual(expectedResult);
        });
    });
});
