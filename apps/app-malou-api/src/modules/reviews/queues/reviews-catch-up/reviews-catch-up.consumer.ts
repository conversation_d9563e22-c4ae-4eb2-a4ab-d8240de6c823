import { isNil } from 'lodash';
import { SQSMessage } from 'sqs-consumer';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { StartReviewsCatchUpUseCase } from ':modules/reviews/use-cases/start-reviews-catch-up/start-reviews-catch-up.use-case';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsConsumer } from ':queues/sqs-template/generic-sqs-consumer';

@singleton()
export class ReviewsCatchUpConsumer extends GenericSqsConsumer {
    constructor(private readonly _startReviewsCatchUpUseCase: StartReviewsCatchUpUseCase) {
        super({
            useCaseQueueTag: UseCaseQueueTag.REVIEWS_CATCH_UP,
            queueUrl: Config.services.sqs.reviewsCatchUpQueueUrl,
        });
    }

    async handleMessage(msg: SQSMessage): Promise<void> {
        const body = msg.Body ? JSON.parse(msg.Body) : null;
        if (isNil(body)) {
            throw new MalouError(MalouErrorCode.SQS_MESSAGE_NOT_FOUND, { message: 'body is nil' });
        }

        const { platformKey } = body;
        if (!platformKey) {
            throw new MalouError(MalouErrorCode.SQS_INVALID_MESSAGE, { message: 'platformKey not found' });
        }

        logger.info('Starting reviews catch up for platform', { platformKey });
        await this._startReviewsCatchUpUseCase.execute(platformKey);
    }
}
