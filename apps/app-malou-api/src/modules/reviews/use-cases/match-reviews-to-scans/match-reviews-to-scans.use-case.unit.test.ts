import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { GiftClaimStartDateOption, NextDrawEnabledDelay, NfcType, PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import ScansRepository from ':modules/scans/repository/scans.repository';
import { getDefaultScan } from ':modules/scans/tests/scans.builder';
import { getDefaultWheelOfFortuneSnapshot } from ':modules/wheel-of-fortune-snapshots/tests/wheel-of-fortune-snapshots.builder';

import { MatchReviewsToScansUseCase } from './match-reviews-to-scans.use-case';

describe('MatchReviewsToScansUseCase', () => {
    beforeAll(() => {
        registerRepositories(['ScansRepository', 'ReviewsRepository', 'WheelOfFortuneSnapshotsRepository']);
    });

    it('should match review to scan with same platform key', async () => {
        const matchReviewsToScansUseCase = container.resolve(MatchReviewsToScansUseCase);
        const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
        const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
        const scanDate = new Date();
        const reviewDate = DateTime.fromJSDate(scanDate).plus({ minutes: 5 }).toJSDate();
        const restaurantId = newDbId();

        const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
            seeds: {
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .redirectedAt(scanDate)
                                .nfcSnapshot({
                                    _id: newDbId(),
                                    platformKey: PlatformKey.GMB,
                                    redirectionLink: 'https://gmb.com',
                                    chipName: 'chipName',
                                    restaurantId,
                                    name: 'name',
                                    type: NfcType.TOTEM,
                                    active: true,
                                    starsRedirected: [4, 5],
                                    createdAt: new Date(),
                                    updatedAt: new Date(),
                                })
                                .matchedReviewSocialId(undefined)
                                .starClicked(5)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()
                                .socialId('review-social-id')
                                .rating(5)
                                .restaurantId(restaurantId)
                                .key(PlatformKey.GMB)
                                .socialCreatedAt(reviewDate)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): string {
                return dependencies.reviews[0].socialId;
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const scan = seededObjects.scans[0];

        await matchReviewsToScansUseCase.execute({ minRedirectedAt: yesterday, maxRedirectedAt: tomorrow }, yesterday);

        const scanRepository = container.resolve(ScansRepository);
        const updatedScan = await scanRepository.findOneOrFail({ filter: { _id: scan._id }, options: { lean: true } });

        const expectedResult = testCase.getExpectedResult();
        expect(updatedScan.matchedReviewSocialId).toEqual(expectedResult);
    });

    it('should match review to wheel of fortune scan with GMB redirection settings', async () => {
        const matchReviewsToScansUseCase = container.resolve(MatchReviewsToScansUseCase);
        const scanDate = new Date();
        const reviewDate = DateTime.fromJSDate(scanDate).plus({ minutes: 5 }).toJSDate();
        const wofId = newDbId();
        const restaurantId = newDbId();
        const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
        const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

        const testCase = new TestCaseBuilderV2<'scans' | 'reviews' | 'wheelOfFortuneSnapshots'>({
            seeds: {
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .redirectedAt(scanDate)
                                .redirectedAt(scanDate)
                                .nfcSnapshot({
                                    _id: newDbId(),
                                    platformKey: PlatformKey.GMB,
                                    redirectionLink: `${process.env.BASE_URL}/wheel-of-fortune?wofId=${wofId.toString()}&restaurantId=123&isFromTotem=true`,
                                    chipName: 'chipName',
                                    restaurantId,
                                    name: 'name',
                                    type: NfcType.TOTEM,
                                    active: true,
                                    starsRedirected: [4, 5],
                                    createdAt: new Date(),
                                    updatedAt: new Date(),
                                })
                                .starClicked(undefined)
                                .matchedReviewSocialId(undefined)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()
                                .restaurantId(restaurantId)
                                .socialId('review-social-id')
                                .key(PlatformKey.GMB)
                                .socialCreatedAt(reviewDate)
                                .build(),
                        ];
                    },
                },
                wheelOfFortuneSnapshots: {
                    data() {
                        return [
                            getDefaultWheelOfFortuneSnapshot()
                                .wheelOfFortuneId(wofId)
                                .parameters({
                                    primaryColor: '#6A52FD',
                                    secondaryColor: '#AC32B7',
                                    mediaId: null,
                                    giftClaimStartDateOption: GiftClaimStartDateOption.NOW,
                                    giftClaimDurationInDays: 30,
                                    redirectionSettings: {
                                        shouldRedirect: true,
                                        platforms: [{ platformKey: PlatformKey.GMB, order: 0 }],
                                        nextDrawEnabledDelay: NextDrawEnabledDelay.NEVER,
                                    },
                                })
                                .createdAt(yesterday)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): string {
                return dependencies.reviews[0].socialId;
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const scan = seededObjects.scans[0];

        await matchReviewsToScansUseCase.execute({ minRedirectedAt: yesterday, maxRedirectedAt: tomorrow }, yesterday);

        const scanRepository = container.resolve(ScansRepository);
        const updatedScan = await scanRepository.findOne({ filter: { _id: scan._id }, options: { lean: true } });

        const expectedResult = testCase.getExpectedResult();
        expect(updatedScan?.matchedReviewSocialId).toEqual(expectedResult);
    });

    it('should not match review if wheel of fortune snapshot has redirection disabled', async () => {
        const matchReviewsToScansUseCase = container.resolve(MatchReviewsToScansUseCase);
        const scanDate = new Date();
        const reviewDate = DateTime.fromJSDate(scanDate).plus({ minutes: 5 }).toJSDate();
        const wofId = newDbId();
        const restaurantId = newDbId();
        const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
        const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

        const testCase = new TestCaseBuilderV2<'scans' | 'reviews' | 'wheelOfFortuneSnapshots'>({
            seeds: {
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .redirectedAt(scanDate)
                                .redirectedAt(scanDate)
                                .nfcSnapshot({
                                    _id: newDbId(),
                                    platformKey: PlatformKey.GMB,
                                    redirectionLink: `${process.env.BASE_URL}/wheel-of-fortune?wofId=${wofId}&restaurantId=123&isFromTotem=true`,
                                    chipName: 'chipName',
                                    restaurantId,
                                    name: 'name',
                                    type: NfcType.TOTEM,
                                    active: true,
                                    starsRedirected: [4, 5],
                                    createdAt: new Date(),
                                    updatedAt: new Date(),
                                })
                                .starClicked(undefined)
                                .matchedReviewSocialId(undefined)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [getDefaultReview().restaurantId(restaurantId).key(PlatformKey.GMB).socialCreatedAt(reviewDate).build()];
                    },
                },
                wheelOfFortuneSnapshots: {
                    data() {
                        return [
                            getDefaultWheelOfFortuneSnapshot()
                                .wheelOfFortuneId(wofId)
                                .parameters({
                                    primaryColor: '#6A52FD',
                                    secondaryColor: '#AC32B7',
                                    mediaId: null,
                                    giftClaimStartDateOption: GiftClaimStartDateOption.NOW,
                                    giftClaimDurationInDays: 30,
                                    redirectionSettings: {
                                        shouldRedirect: false,
                                        platforms: [{ platformKey: PlatformKey.GMB, order: 0 }],
                                        nextDrawEnabledDelay: NextDrawEnabledDelay.NEVER,
                                    },
                                })
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const scan = seededObjects.scans[0];

        await matchReviewsToScansUseCase.execute({ minRedirectedAt: yesterday, maxRedirectedAt: tomorrow }, yesterday);

        const scanRepository = container.resolve(ScansRepository);
        const updatedScan = await scanRepository.findOne({ filter: { _id: scan._id }, options: { lean: true } });
        expect(updatedScan?.matchedReviewSocialId).toBeUndefined();
    });

    it('should not match review if wheel of fortune snapshot is not found', async () => {
        const matchReviewsToScansUseCase = container.resolve(MatchReviewsToScansUseCase);
        const scanDate = new Date();
        const reviewDate = DateTime.fromJSDate(scanDate).plus({ minutes: 5 }).toJSDate();
        const wofId = newDbId();
        const restaurantId = newDbId();
        const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
        const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

        const testCase = new TestCaseBuilderV2<'scans' | 'reviews'>({
            seeds: {
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .redirectedAt(scanDate)
                                .redirectedAt(scanDate)
                                .nfcSnapshot({
                                    _id: newDbId(),
                                    platformKey: PlatformKey.GMB,
                                    redirectionLink: `${process.env.BASE_URL}/wheel-of-fortune?wofId=${wofId}&restaurantId=123&isFromTotem=true`,
                                    chipName: 'chipName',
                                    restaurantId,
                                    name: 'name',
                                    type: NfcType.TOTEM,
                                    active: true,
                                    starsRedirected: [4, 5],
                                    createdAt: new Date(),
                                    updatedAt: new Date(),
                                })
                                .starClicked(undefined)
                                .matchedReviewSocialId(undefined)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [getDefaultReview().restaurantId(restaurantId).key(PlatformKey.GMB).socialCreatedAt(reviewDate).build()];
                    },
                },
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const scan = seededObjects.scans[0];

        await matchReviewsToScansUseCase.execute({ minRedirectedAt: yesterday, maxRedirectedAt: tomorrow }, yesterday);

        const scanRepository = container.resolve(ScansRepository);
        const updatedScan = await scanRepository.findOne({ filter: { _id: scan._id }, options: { lean: true } });

        expect(updatedScan?.matchedReviewSocialId).toBeUndefined();
    });
});
