import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsight } from ':modules/post-insights/v2/entities';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { RefreshPlatformPostInsightsService } from ':modules/post-insights/v2/services/refresh-platform-post-insights/refresh-platform-post-insights.service';

@singleton()
export class RefreshPlatformPostInsightsByIdsUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postInsightsRepository: PostInsightRepository,
        private readonly _refreshPlatformPostInsightsService: RefreshPlatformPostInsightsService
    ) {}

    async execute({ platformId, postInsightIds }: { platformId: string; postInsightIds: string[] }): Promise<void> {
        logger.info(`[RefreshPlatformPostInsightsByIdsUseCase] Starting refresh for platformId:
                ${platformId} with ${postInsightIds.length} post insights`);

        const platform = await this._platformsRepository.getPlatformById(platformId);
        if (!platform || !platform.socialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'platform not connected',
                metadata: { platformId },
            });
        }

        const postInsightsToRefresh = await this._postInsightsRepository.getByIds(postInsightIds);
        if (postInsightsToRefresh.length === 0) {
            return;
        }

        await this._refreshPlatformPostInsights({ postInsights: postInsightsToRefresh, platform });
    }

    private async _refreshPlatformPostInsights({
        postInsights,
        platform,
    }: {
        postInsights: PostInsight[];
        platform: Platform;
    }): Promise<PostInsight[]> {
        assert(platform.socialId, 'Platform socialId is required for post insights refresh');
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                message: '[RefreshPlatformPostInsightsByIdsUseCase] Credentials not found in instagram synchronize use case',
            });
        }
        switch (platform.key) {
            case PlatformKey.FACEBOOK:
                return this._refreshPlatformPostInsightsService.refreshFbPostInsights({
                    postInsights,
                    platformSocialId: platform.socialId,
                    credentialId: credentialId,
                });
            case PlatformKey.INSTAGRAM:
                return this._refreshPlatformPostInsightsService.refreshIgPostInsights({
                    postInsights,
                    platformSocialId: platform.socialId,
                    credentialId: credentialId,
                });
            case PlatformKey.TIKTOK:
                return this._refreshPlatformPostInsightsService.refreshTiktokPostInsights({
                    postInsights,
                    restaurantId: platform.restaurantId.toString(),
                });
            default:
                return [];
        }
    }
}
