import { chunk } from 'lodash';
import { singleton } from 'tsyringe';

import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { RefreshPlatformPostInsightsByIdsUseCase } from ':modules/post-insights/v2/use-cases/refresh-platform-post-insights-by-ids/refresh-platform-post-insights-by-ids.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsConsumer } from ':queues/sqs-template/generic-sqs-consumer';

/*
    development: https://eu-west-3.console.aws.amazon.com/scheduler/home?region=eu-west-3#schedules/default/DailyRefreshPostInsightsDevelopment
    staging: https://eu-west-3.console.aws.amazon.com/scheduler/home?region=eu-west-3#schedules/default/DailyRefreshPostInsightsStaging
    production: https://eu-west-3.console.aws.amazon.com/scheduler/home?region=eu-west-3#schedules/default/DailyRefreshPostInsightsProduction
*/

@singleton()
export class DailyRefreshPostInsightsConsumer extends GenericSqsConsumer {
    private readonly PLATFORMS_TO_PROCESS = [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK, PlatformKey.TIKTOK];
    private readonly CHUNK_SIZE = 3;

    constructor(
        private readonly _postInsightsRepository: PostInsightRepository,
        private readonly _platformRepository: PlatformsRepository,
        private readonly _restaurantRepository: RestaurantsRepository,
        private readonly _refreshPlatformPostInsightsByIdsUseCase: RefreshPlatformPostInsightsByIdsUseCase
    ) {
        super({
            queueUrl: Config.services.sqs.dailyRefreshPostInsightsQueueUrl,
            useCaseQueueTag: UseCaseQueueTag.DAILY_REFRESH_POST_INSIGHTS,
            shouldAwaitExecution: false,
        });
    }

    async handleMessage(): Promise<void> {
        const restaurantIds = await this._restaurantRepository.getAllIds();
        const platforms = await this._platformRepository.getPlatformsByRestaurantIdsAndPlatformKeys(
            restaurantIds,
            this.PLATFORMS_TO_PROCESS
        );

        const platformSocialIds = platforms.map((platform) => platform.socialId).filter(isNotNil);

        if (!platformSocialIds.length) {
            logger.info('[DailyRefreshPostInsightsConsumer] No platforms to process');
            return;
        }

        const postInsightToRefresh: { [platformSocialId: string]: string[] } =
            await this._postInsightsRepository.getIdsToRefreshByPlatformSocialId({ platformSocialIds });

        logger.info('[DailyRefreshPostInsightsConsumer] - post insights to refresh', {
            platformsCount: Object.keys(postInsightToRefresh).length,
            postInsightsCount: Object.values(postInsightToRefresh).reduce((acc, ids) => acc + ids.length, 0),
        });

        const chunks = chunk(Object.entries(postInsightToRefresh), this.CHUNK_SIZE);

        for (const chunkedEntries of chunks) {
            await Promise.all(
                chunkedEntries.map(([platformSocialId, postInsightIds]) => {
                    const platform = platforms.find((p) => p.socialId === platformSocialId);
                    const platformId = platform!._id.toString();

                    return this._refreshPlatformPostInsightsByIdsUseCase.execute({ platformId, postInsightIds }).catch((error) =>
                        logger.error(
                            `\n\n[RefreshPlatformPostInsightsByIdsUseCase] - error refreshing post insights for platformId ${platformId}`,
                            {
                                error: JSON.stringify(error),
                                platformId,
                                postInsightIds,
                            }
                        )
                    );
                })
            );
        }
    }
}
