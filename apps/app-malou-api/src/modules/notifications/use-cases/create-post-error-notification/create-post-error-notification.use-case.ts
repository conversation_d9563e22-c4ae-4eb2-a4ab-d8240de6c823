import assert from 'node:assert/strict';
import { inject, registry, singleton } from 'tsyringe';

import { NotificationType, PictureSize } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { MediasRepository } from ':modules/media/medias.repository';
import { NotificationPost } from ':modules/notifications/entities/child-entities/notification-post.entity';
import { NotificationUser } from ':modules/notifications/entities/child-entities/notification-user.entity';
import { PostErrorNotification } from ':modules/notifications/entities/post-error-notification.entity';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { SendNotificationsToChannelsService } from ':modules/notifications/services/notifications-sender-service/send-notifications-to-channels.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import PostsRepository from ':modules/posts/posts.repository';
import { UsersRepository } from ':modules/users/users.repository';

@singleton()
@registry([
    {
        token: InjectionToken.NotificationSenderService,
        useClass: SendNotificationsToChannelsService,
    },
])
export class CreatePostErrorNotificationUseCase {
    constructor(
        private readonly _notificationsRepository: NotificationsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _platformsRepository: PlatformsRepository,
        @inject(InjectionToken.NotificationSenderService)
        private readonly _notificationSenderService: NotificationSenderService
    ) {}
    async execute({ postId }: { postId: string }): Promise<void> {
        const post = await this._postsRepository.findOne({
            filter: { _id: postId },
            options: { lean: true, populate: [{ path: 'restaurant', projection: { internalName: 1, name: 1 } }] },
        });
        if (!post) {
            return;
        }
        assert(post.platformId, '[CreatePostErrorNotificationUseCase] Post platform id is required');
        const platform = await this._platformsRepository.getPlatformById(post.platformId?.toString());

        assert(post.attachments?.[0], '[CreatePostErrorNotificationUseCase] Post attachments are required');

        const firstMedia = await this._mediasRepository.findById(post.attachments[0]?.toString());
        assert(firstMedia, 'Media not found');

        assert(post.author, '[CreatePostErrorNotificationUseCase] Post author is required');
        const [author] = await this._usersRepository
            .getUsersByIds([post.author._id?.toString()])
            .then((users) => users.map((user) => NotificationUser.fromUser(user)));
        assert(author, 'Author not found');

        const notifications = author.getReceivableNotificationChannels(NotificationType.POST_ERROR).map((channel) => {
            const assertedPlatform =
                platform && platform.id
                    ? {
                          id: platform.id,
                          key: platform.key ?? post.key,
                          name: platform.name ?? '',
                          socialLink: platform.socialLink,
                      }
                    : undefined;
            return new PostErrorNotification({
                channel,
                type: NotificationType.POST_ERROR,
                userId: author.id,
                user: author,
                data: {
                    mainRestaurantName: post.restaurant?.internalName ?? post.restaurant?.name,
                    post: new NotificationPost({
                        id: post._id.toString(),
                        source: post.source,
                        errorMessage: post.errorData ?? undefined,
                        plannedPublicationDate: post.plannedPublicationDate ?? undefined,
                        restaurantId: post.restaurantId?.toString(),
                        postCreatedAt: post.createdAt,
                        postUpdatedAt: post.updatedAt,
                        attachments: {
                            [PictureSize.SMALL]: firstMedia.getMediaPictureUrl(PictureSize.SMALL),
                            [PictureSize.IG_FIT]: firstMedia.getMediaPictureUrl(PictureSize.IG_FIT),
                        },
                        platform: assertedPlatform,
                        text: post.text ?? '',
                        isStory: post.isStory,
                    }),
                    restaurantIds: post.restaurantId?.toString() ? [post.restaurantId?.toString()] : [],
                },
            });
        });

        if (!notifications.length) {
            return;
        }
        await this._notificationsRepository.createManyNotifications(notifications);

        await this._notificationSenderService.sendNotificationsToChannels(notifications);
    }
}
