import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    createCampaignBodyValidator,
    CreateCampaignDto,
    DeleteCampaignsByIdsBodyDto,
    deleteCampaignsByIdsBodyValidator,
    DeleteCampaignsByRestaurantIdQueryDto,
    deleteCampaignsByRestaurantIdQueryValidator,
    GetVerifiedEmailsInSESBodyDto,
    getVerifiedEmailsInSESBodyValidator,
    GetVerifiedEmailsInSESResponseDto,
} from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import {
    ApiResultV2,
    CaslAction,
    CaslSubject,
    ContactMode,
    EmailCategory,
    EmailType,
    MalouErrorCode,
    PlatformKey,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body, Query } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { RequestWithPermissions } from ':helpers/utils.types';
import MailingUseCases from ':modules/mailing/use-cases';
import { getVerifiedEmailsInSES } from ':plugins/ses';

import CampaignsUseCases from './campaigns.use-cases';

@singleton()
export default class CampaignsController {
    constructor(
        private _campaignsUseCases: CampaignsUseCases,
        private _mailingUseCases: MailingUseCases
    ) {}

    @Body(createCampaignBodyValidator)
    async handleCreateCampaign(req: RequestWithPermissions<any, any, CreateCampaignDto>, res: Response, next: NextFunction) {
        try {
            const data = req.body;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.CAMPAIGN, { restaurantId: data.restaurantId })
            );
            const campaign = await this._campaignsUseCases.createCampaign(data);
            logger.info('[CREATE_CAMPAIGN] Campaign created', { campaign });
            return res.json({ msg: 'Campaign created', data: campaign });
        } catch (err) {
            next(err);
        }
    }

    async handleSendCampaignEmails(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { campaign_id: campaignId, restaurant_id: restaurantId } = req.params;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.CAMPAIGN, { restaurantId })
            );

            await this._campaignsUseCases.sendCampaignEmails(campaignId, restaurantId);
            logger.info('[SEND_CAMPAIGN_MAILS] Mails sent', { campaignId, restaurantId });
            return res.json({ msg: 'Mails sent' });
        } catch (error) {
            logger.warn('[SEND_CAMPAIGN_MAILS] Failed', { data: req.body.data, error });
            next(error);
        }
    }

    async handleSendReviewBoosterTestEmail(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId, platform_key: platformKey } = req.params;
            const { data } = req.body;
            assert(req.user, 'User not found');
            const { _id: userId } = req.user;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.CAMPAIGN, { restaurantId })
            );
            await this._campaignsUseCases.sendReviewBoosterTestEmail(restaurantId, userId, platformKey, data);
            return res.json({ msg: 'Email sent' });
        } catch (err) {
            next(err);
        }
    }

    async handleSendVerificationEmail(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { email } = req.body;
            const { restaurant_id: restaurantId } = req.query;

            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.CAMPAIGN, { restaurantId })
            );

            await this._mailingUseCases.sendEmail(EmailCategory.REVIEW_BOOSTER, EmailType.EMAIL_VERIFICATION, {
                emailData: { to: email },
                user: req.user,
            });

            return res.json({ msg: 'Email sent' });
        } catch (err) {
            next(err);
        }
    }

    @Body(getVerifiedEmailsInSESBodyValidator)
    async handleGetVerifiedEmailsInSES(
        req: Request<any, any, GetVerifiedEmailsInSESBodyDto>,
        res: Response<ApiResultV2<GetVerifiedEmailsInSESResponseDto>>,
        next: NextFunction
    ) {
        try {
            const areEmailsVerifiedRecord = await getVerifiedEmailsInSES(req.body.emails);
            res.json({ data: areEmailsVerifiedRecord });
        } catch (err) {
            next(err);
        }
    }

    async handleGetCampaignsForRestaurant(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            const campaignsForRestaurant = await this._campaignsUseCases.getCampaignsForRestaurant(toDbId(restaurantId));
            return res.json({ msg: 'Campaigns retrieved', data: campaignsForRestaurant });
        } catch (err) {
            next(err);
        }
    }

    @Query(deleteCampaignsByRestaurantIdQueryValidator)
    @Body(deleteCampaignsByIdsBodyValidator)
    async handleDeleteCampaignsByIds(
        req: RequestWithPermissions<any, any, DeleteCampaignsByIdsBodyDto, DeleteCampaignsByRestaurantIdQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { campaignIds } = req.body;
            const { restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.CAMPAIGN, { restaurantId })
            );
            await this._campaignsUseCases.deleteCampaignsByIds(campaignIds);
            return res.json({ msg: 'Campaigns deleted' });
        } catch (err) {
            next(err);
        }
    }

    async handleDeleteCampaignById(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { campaign_id: campaignId } = req.params;
            const { restaurant_id: restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.CAMPAIGN, { restaurantId })
            );
            await this._campaignsUseCases.deleteCampaignById(toDbId(campaignId));
            return res.json({ msg: 'Campaign deleted' });
        } catch (err) {
            next(err);
        }
    }

    async handleUpdateCampaignEndDate(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { campaign_id: campaignId } = req.params;
            const { restaurant_id: restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.CAMPAIGN, { restaurantId })
            );
            const campaignEnded = await this._campaignsUseCases.updateCampaignEndDate(toDbId(campaignId));
            return res.json({ msg: 'Campaign ended', data: campaignEnded });
        } catch (err) {
            next(err);
        }
    }

    async handleGetCampaignSources(req: Request, res: Response, next: NextFunction) {
        try {
            const { campaign_id: campaignId } = req.params;
            const campaignSources = await this._campaignsUseCases.getCampaignSources(toDbId(campaignId));
            res.json({ msg: 'Campaign sources retrieved', data: campaignSources });
        } catch (err) {
            next(err);
        }
    }

    async handleUnsubscribeClientFromCampaign(
        req: Request<any, any, any, { restaurant_id: string; contact_mode: ContactMode }>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { campaign_id: campaignId, client_id: clientId } = req.params;
            const { contact_mode: contactMode, restaurant_id: restaurantId } = req.query;

            if (campaignId !== 'test' && clientId !== 'test') {
                await this._campaignsUseCases.saveUnsubscribedClientInCampaign(campaignId, clientId);
                await this._campaignsUseCases.updateClientUnsubscribedFromCampaign(toDbId(clientId), contactMode);
            }

            const restaurant = await this._campaignsUseCases.getRestaurantNameById(restaurantId);

            res.redirect(`${process.env.BASE_URL}/unsubscribe_campaign?restaurant_name=${restaurant.name}`);
        } catch (e) {
            res.redirect(`${process.env.BASE_URL}/unsubscribe_campaign?error=true`);
            next(e);
        }
    }

    async handleSaveClientInteractionReview(
        req: Request<any, any, any, { restaurant_id: string; api_key: string; platform_key: PlatformKey; star: string }>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { campaign_id: campaignId, client_id: clientId } = req.params;
            const { api_key: apiKey, platform_key: platformKey, restaurant_id: restaurantId, star } = req.query;
            if (!star) {
                throw new MalouError(MalouErrorCode.CAMPAIGN_MISSING_QUERY_PARAM, {
                    message: 'Missing query param',
                    metadata: { param: 'star' },
                });
            }
            const starNb = parseInt(star.toString(), 10);

            const privateReviewRatings = await this._campaignsUseCases.initRedirectFrom(campaignId, clientId, starNb);
            const langs = req.acceptsLanguages() ?? [];

            const redirectionUrl = await this._campaignsUseCases.getRedirectionUrl({
                clientId,
                restaurantId,
                campaignId,
                starNb,
                privateReviewRatings,
                platformKey,
                apiKey,
                langs,
            });

            assert(redirectionUrl, 'Redirection url is undefined');

            return res.redirect(redirectionUrl);
        } catch (e) {
            res.redirect(`${process.env.BASE_URL}/campaign_review/redirect?error=true`);
            next(e);
        }
    }
}
