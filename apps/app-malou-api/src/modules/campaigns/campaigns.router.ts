import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import { ContactMode, PlatformKey } from '@malou-io/package-utils';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { casl } from ':helpers/casl/middlewares';
import { api<PERSON>eyAuthorize } from ':modules/api-keys/middlewares';
import { authorize } from ':plugins/passport';

import CampaignsController from './campaigns.controller';

@singleton()
export default class CampaignsRouter extends AbstractRouter {
    constructor(private _campaignsController: CampaignsController) {
        super();
    }

    init(): Router {
        this.router.post('/', authorize(), casl(), (req, res, next) => this._campaignsController.handleCreateCampaign(req, res, next));

        this.router.post('/restaurants/:restaurant_id/send_mails/:campaign_id', authorize(), casl(), (req, res, next) =>
            this._campaignsController.handleSendCampaignEmails(req, res, next)
        );

        this.router.post('/restaurants/:restaurant_id/send_test_mail/:platform_key', authorize(), casl(), (req, res, next) =>
            this._campaignsController.handleSendReviewBoosterTestEmail(req, res, next)
        );

        this.router.post('/send_verification_mail', authorize(), casl(), (req, res, next) =>
            this._campaignsController.handleSendVerificationEmail(req, res, next)
        );

        this.router.post('/aws_verified_emails', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._campaignsController.handleGetVerifiedEmailsInSES(req, res, next)
        );

        this.router.get('/restaurants/:restaurant_id', authorize(), (req, res, next) =>
            this._campaignsController.handleGetCampaignsForRestaurant(req, res, next)
        );

        this.router.post('/delete', authorize(), casl(), (req, res, next) =>
            this._campaignsController.handleDeleteCampaignsByIds(req, res, next)
        );

        this.router.delete('/:campaign_id', authorize(), casl(), (req, res, next) =>
            this._campaignsController.handleDeleteCampaignById(req, res, next)
        );

        this.router.put('/:campaign_id/end', authorize(), casl(), (req, res, next) =>
            this._campaignsController.handleUpdateCampaignEndDate(req, res, next)
        );

        this.router.get('/:campaign_id/clients_sources', authorize(), casl(), (req, res, next) =>
            this._campaignsController.handleGetCampaignSources(req, res, next)
        );

        this.router.get(
            '/:campaign_id/unsubscribed/:client_id',
            apiKeyAuthorize,
            (
                req: Request<any, any, any, { restaurant_id: string; contact_mode: ContactMode }>,
                res: Response,
                next: NextFunction
            ): Promise<void> => this._campaignsController.handleUnsubscribeClientFromCampaign(req, res, next)
        );

        this.router.get(
            '/:campaign_id/review/:client_id',
            apiKeyAuthorize,
            (
                req: Request<any, any, any, { restaurant_id: string; api_key: string; platform_key: PlatformKey; star: string }>,
                res: Response,
                next: NextFunction
            ): Promise<void> => this._campaignsController.handleSaveClientInteractionReview(req, res, next)
        );

        return this.router;
    }
}
