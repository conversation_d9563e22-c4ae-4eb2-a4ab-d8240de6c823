import { singleton } from 'tsyringe';

import { IPostInsight } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { WebhookStoryInsights } from ':modules/post-insights/instagram/instagram-post-insights.interface';
import { InstagramPostInsightsMapper } from ':modules/post-insights/instagram/instagram-post-insights.mapper';
import { PostInsightsRepository } from ':modules/post-insights/post-insights.repository';

@singleton()
export class InstagramWebhookPostInsightsUseCases {
    constructor(
        private _postInsightsRepository: PostInsightsRepository,
        private _instagramPostInsightsMapper: InstagramPostInsightsMapper
    ) {}

    handleIgStoryInsights = async (value: WebhookStoryInsights, pageId: string): Promise<IPostInsight> => {
        const mappedStoryInsights = this._instagramPostInsightsMapper.mapWebhookInsightsToMalouPostInsight(
            value,
            pageId,
            PlatformKey.INSTAGRAM
        );
        return this._postInsightsRepository.upsert({
            filter: { socialId: value.media_id, platformKey: PlatformKey.INSTAGRAM },
            update: mappedStoryInsights,
            options: {
                lean: true,
            },
        });
    };
}
