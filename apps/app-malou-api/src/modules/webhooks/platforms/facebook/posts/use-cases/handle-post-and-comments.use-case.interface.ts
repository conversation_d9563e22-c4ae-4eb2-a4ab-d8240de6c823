import z from 'zod';

const reactionValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('reaction'),
    parent_id: z.string().optional(),
    reaction_type: z.string(), // received 'like', 'love' and 'haha' for example
    verb: z.string(), // received 'add' for example
});

const statusValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('status'),
    message: z.string().optional(),
    photos: z.array(z.string()).optional(),
    parent_id: z.string().optional(),
    verb: z.string(), // received 'edited' for example
});

const commentValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('comment'),
    post: z.unknown(), // object but we don't use it
    message: z.string().optional(), // not present on comment removal
    comment_id: z.string(),
    parent_id: z.string().optional(),
    verb: z.string(), // received 'add' and 'remove' for example
});

const videoValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    link: z.string().optional(),
    message: z.string().optional(),
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('video'),
    video_id: z.string().optional(), // not present on video removal
    recipient_id: z.string().optional(),
    parent_id: z.string().optional(),
    published: z.unknown().optional(), // received 1 but we don't use it
    verb: z.string(), // received 'add' and 'edited' for example
});

const photoValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    link: z.string().optional(),
    message: z.string().optional(),
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('photo'),
    photo_id: z.string(),
    parent_id: z.string().optional(),
    published: z.unknown().optional(), // received 1 but we don't use it
    verb: z.string(), // received 'add' and 'edited' for example
});

const postValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('post'),
    message: z.string().optional(),
    recipient_id: z.string(),
    verb: z.string(), // received 'remove' and 'edit' for example
});

const shareValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    link: z.string(),
    message: z.string().optional(),
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('share'),
    share_id: z.string(),
    parent_id: z.string().optional(),
    published: z.unknown().optional(), // received 1 but we don't use it
    verb: z.string(), // received 'add' and 'edited' for example
});

const eventValidator = z.object({
    post_id: z.string(),
    created_time: z.number(),
    item: z.literal('event'),
    message: z.string().optional(),
    story: z.string().optional(),
    event_id: z.string(),
    parent_id: z.string().optional(),
    verb: z.string(), // received 'add' and 'edited' for example
});

const albumValidator = z.object({
    from: z.object({
        id: z.string(),
        name: z.string().optional(),
    }),
    album_id: z.string(),
    created_time: z.number(),
    item: z.literal('album'),
    verb: z.string(), // received 'add' for example
});

export const webhookValueValidator = z.union([
    reactionValidator,
    statusValidator,
    commentValidator,
    videoValidator,
    photoValidator,
    postValidator,
    shareValidator,
    eventValidator,
    albumValidator,
]);
