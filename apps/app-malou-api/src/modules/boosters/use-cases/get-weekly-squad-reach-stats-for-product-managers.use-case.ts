import { GoogleSpreadsheetWorksheet } from 'google-spreadsheet';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { DbId } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { GoogleSpreadsheetService } from ':microservices/google-spread-sheet.service';
import { GiftDrawsRepository } from ':modules/gift-draws/gift-draws.repository';
import { TotemsRepository } from ':modules/nfc/totems/totems.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ScansRepository from ':modules/scans/repository/scans.repository';
import { RestaurantWheelsOfFortuneRepository } from ':modules/wheels-of-fortune/restaurant-wheels-of-fortune/restaurant-wheels-of-fortune.repository';

interface WeeklyStats {
    /**  The number of active locations */
    activeLocationCount: number;
    /** The number of locations that have boosters */
    numberOfLocationsWithBoosters: number;
    /** Their ids */
    locationsWithBoosterIds: string[];
    /** The number of locations with at least one active totems */
    numberOfLocationsWithActiveTotems: number;
    /** The number of locations with at least one active wheel of fortune */
    locationsWithActiveWheelOfFortuneCount: number;
    /** The number of location with 5+ totems scans OR 5+ played wheel of fortune */
    numberOfLocationsWithAtLeastFiveTotemScansOrFiveWofDraws: number;
    /** The number of locations with 5+ totems scans */
    numberOfLocationsWithAtLeastFiveTotemScans: number;
    /** The number of locations with 5+ played wheel of fortune */
    numberOfLocationsWithAtLeastFiveWofDraws: number;
    /** The number of locations that published at least one Post/Reel/Story */
    numberOfLocationsThatPublishedOnSocialNetworks: number;
    /** The number of locations that published at least one Post or Reel */
    numberOfLocationsThatPublishedPostsOrReels: number;
    /** The number of locations that published at least one Story */
    numberOfLocationsThatPublishedStories: number;
    /** The number of locations currently with a Tiktok platform */
    numberOfLocationsConnectedToTiktok: number;
    /** The number of locations that published at least once on Tiktok */
    numberOfLocationsThatPublishedOnTiktok: number;
    /** The number of locations currently with a social platform (Facebook / Instagram / Mapstr premium / TikTok) */
    numberOfLocationsConnectedToAtLeastOneSocialNetwork: number;
    /** The week number */
    weekNumber: number;
    /** The start date of the week */
    startDate: Date;
    /** The end date of the week */
    endDate: Date;
}

@singleton()
export class GetWeeklySquadReachStatsForProductManagersUseCase {
    constructor(
        private readonly _giftDrawsRepository: GiftDrawsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _totemsRepository: TotemsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _restaurantWheelsOfFortuneRepository: RestaurantWheelsOfFortuneRepository,
        private readonly _scansRepository: ScansRepository,
        private readonly _googleSpreadsheetService: GoogleSpreadsheetService
    ) {}

    async execute(options?: { isManuallyTriggered?: boolean; forcedDate?: Date }): Promise<void> {
        logger.info('[GetWeeklySquadReachStatsForProductManagersUseCase] Starting');

        logger.info('[GetWeeklySquadReachStatsForProductManagersUseCase] Try to access Google Spreadsheet document');
        const privateKeys = Config.businessNotificationsAccountKey;
        const spreadSheetAuth = {
            client_email: privateKeys.client_email as string,
            private_key: privateKeys.private_key?.split('||n||').join('\n'),
        };
        const doc = await this._googleSpreadsheetService.getGoogleSpreadsheet(
            '1McmTW6_Pcb5j_0yhWl122ldjAbAPbi6aIQmeZ6drbmc',
            spreadSheetAuth
        );
        const sheet = doc.sheetsByTitle['Weekly Stats'];
        logger.info('[GetWeeklySquadReachStatsForProductManagersUseCase] Google Spreadsheet document accessed');

        if (!options?.isManuallyTriggered) {
            const isWeekNumberAlreadyPresent = await this._isWeekNumberAlreadyPresent(sheet, options?.forcedDate);
            if (isWeekNumberAlreadyPresent) {
                logger.info('[GetWeeklySquadReachStatsForProductManagersUseCase] Week number already present, stopping execution');
                return;
            }
        }

        const data = await this._getWeeklyStats(options?.forcedDate);
        logger.info('[GetWeeklySquadReachStatsForProductManagersUseCase] Week data retrieved', data);

        const dataToSpreadSheet = {
            Semaine: data.weekNumber,
            'Date début': DateTime.fromJSDate(data.startDate).toFormat('dd/MM/yyyy'),
            'Date fin': DateTime.fromJSDate(data.endDate).toFormat('dd/MM/yyyy'),
            'Etablissements actifs': data.activeLocationCount,
            'Locations avec Boosters activé': data.numberOfLocationsWithBoosters,
            'Locations avec totems actifs': data.numberOfLocationsWithActiveTotems,
            'Locations avec roue active': data.locationsWithActiveWheelOfFortuneCount,
            'Locations best users': data.numberOfLocationsWithAtLeastFiveTotemScansOrFiveWofDraws,
            'Locations best users totems': data.numberOfLocationsWithAtLeastFiveTotemScans,
            'Locations best users rdf': data.numberOfLocationsWithAtLeastFiveWofDraws,
            'Locations avec au moins une publication': data.numberOfLocationsThatPublishedOnSocialNetworks,
            'Locations avec au moins une publication (Post/Reel)': data.numberOfLocationsThatPublishedPostsOrReels,
            'Locations avec au moins une publication (Story)': data.numberOfLocationsThatPublishedStories,
            'Locations connectées à Tiktok': data.numberOfLocationsConnectedToTiktok,
            'Locations avec au moins une publication sur Tiktok': data.numberOfLocationsThatPublishedOnTiktok,
            'Locations avec une connexion RS': data.numberOfLocationsConnectedToAtLeastOneSocialNetwork,
        };
        logger.info('[GetWeeklySquadReachStatsForProductManagersUseCase] Adding week data to Weekly Stats Spreadsheet', dataToSpreadSheet);
        await sheet.addRow(dataToSpreadSheet);

        logger.info('[GetWeeklySquadReachStatsForProductManagersUseCase] Done');
    }

    private async _isWeekNumberAlreadyPresent(sheet: GoogleSpreadsheetWorksheet, forcedDate?: Date): Promise<boolean> {
        const rows = await sheet.getRows();
        const weekNumbers = rows.map((row) => (row['Semaine'] ?? '').toString());

        const initialDate = forcedDate ? DateTime.fromJSDate(forcedDate) : DateTime.now();
        const startOfWeek = initialDate.startOf('week');
        const startDate = startOfWeek.minus({ weeks: 1 }).toJSDate();
        const currentWeekNumber = DateTime.fromJSDate(startDate).weekNumber.toString();

        return weekNumbers.includes(currentWeekNumber);
    }

    private async _getWeeklyStats(forcedDate?: Date): Promise<WeeklyStats> {
        const initialDate = forcedDate ? DateTime.fromJSDate(forcedDate) : DateTime.now();
        const startOfWeek = initialDate.startOf('week');
        const endDate = startOfWeek.minus({ seconds: 1 }).toJSDate();
        const startDate = startOfWeek.minus({ weeks: 1 }).toJSDate();

        const activeLocationCount = await this._getActiveLocations();
        const { count: numberOfLocationsWithBoosters, ids: locationsWithBoosterIds } = await this._getBoostersLocations();
        const numberOfLocationsWithActiveTotems = await this._getLocationsWithActiveTotems();
        const locationsWithActiveWheelOfFortuneCount = await this._getLocationsWithActiveWheelOfFortune();
        const {
            numberOfLocationsWithAtLeastFiveTotemScansOrFiveWofDraws,
            numberOfLocationsWithAtLeastFiveTotemScans,
            numberOfLocationsWithAtLeastFiveWofDraws,
        } = await this._getLocationsBestUsers(startDate, endDate);
        const {
            numberOfLocationsThatPublishedOnSocialNetworks,
            numberOfLocationsThatPublishedPostsOrReels,
            numberOfLocationsThatPublishedStories,
            numberOfLocationsConnectedToTiktok,
            numberOfLocationsThatPublishedOnTiktok,
            numberOfLocationsConnectedToAtLeastOneSocialNetwork,
        } = await this._getSocialNetworksStats(startDate, endDate);

        const weekNumber = DateTime.fromJSDate(startDate).weekNumber;

        return {
            activeLocationCount,
            numberOfLocationsWithBoosters,
            locationsWithBoosterIds,
            numberOfLocationsWithActiveTotems,
            locationsWithActiveWheelOfFortuneCount,
            numberOfLocationsWithAtLeastFiveTotemScansOrFiveWofDraws,
            numberOfLocationsWithAtLeastFiveTotemScans,
            numberOfLocationsWithAtLeastFiveWofDraws,
            numberOfLocationsThatPublishedOnSocialNetworks,
            numberOfLocationsThatPublishedPostsOrReels,
            numberOfLocationsThatPublishedStories,
            numberOfLocationsConnectedToTiktok,
            numberOfLocationsThatPublishedOnTiktok,
            numberOfLocationsConnectedToAtLeastOneSocialNetwork,
            weekNumber,
            startDate,
            endDate,
        };
    }

    private _getActiveLocations(): Promise<number> {
        return this._restaurantsRepository.countDocuments({ filter: { active: true } });
    }

    private async _getBoostersLocations(): Promise<{ count: number; ids: string[] }> {
        const boostersLocations = await this._restaurantsRepository.find({
            filter: { active: true, 'boosterPack.activated': true },
            projection: { _id: 1 },
            options: { lean: true },
        });
        return { count: boostersLocations.length, ids: boostersLocations.map((r) => r._id.toString()) };
    }

    private async _getLocationsWithActiveTotems(): Promise<number> {
        const aggregation = [
            { $match: { active: true } },
            { $group: { _id: '$restaurantId' } },
            {
                $lookup: {
                    from: 'restaurants',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                    preserveNullAndEmptyArrays: false,
                },
            },
            { $match: { 'restaurant.active': true } },
            { $count: 'count' },
        ];
        const result = (await this._totemsRepository.aggregate(aggregation)) as { count: number }[];
        return result[0]?.count ?? 0;
    }

    private async _getLocationsWithActiveWheelOfFortune(): Promise<number> {
        const now = new Date();
        const aggregation = [
            {
                $lookup: {
                    from: 'restaurants',
                    localField: 'restaurantId',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                    preserveNullAndEmptyArrays: false,
                },
            },
            { $match: { 'restaurant.active': true } },
            {
                $lookup: {
                    from: 'wheelsoffortune',
                    localField: 'aggregatedWheelOfFortuneId',
                    foreignField: '_id',
                    as: 'aggregatedWheelOfFortune',
                },
            },
            {
                $unwind: {
                    path: '$aggregatedWheelOfFortune',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $match: {
                    $or: [
                        {
                            startDate: { $ne: null, $lt: now },
                            $or: [{ endDate: null }, { endDate: { $gt: now } }],
                        },
                        {
                            aggregatedWheelOfFortune: { $ne: null },
                            'aggregatedWheelOfFortune.startDate': {
                                $ne: null,
                                $lt: now,
                            },
                            $or: [
                                { 'aggregatedWheelOfFortune.endDate': null },
                                {
                                    'aggregatedWheelOfFortune.endDate': {
                                        $gt: now,
                                    },
                                },
                            ],
                        },
                    ],
                },
            },
            { $count: 'count' },
        ];

        const result = (await this._restaurantWheelsOfFortuneRepository.aggregate(aggregation)) as { count: number }[];
        return result[0]?.count ?? 0;
    }

    private async _getLocationsBestUsers(
        startDate: Date,
        endDate: Date
    ): Promise<{
        numberOfLocationsWithAtLeastFiveTotemScansOrFiveWofDraws: number;
        numberOfLocationsWithAtLeastFiveTotemScans: number;
        numberOfLocationsWithAtLeastFiveWofDraws: number;
    }> {
        const bestUsersTotemsIds = await this._getLocationsBestUsersTotems(startDate, endDate);
        const bestUsersWheelOfFortuneIds = await this._getLocationsBestUsersWheelOfFortune(startDate, endDate);

        const bestUsersIds = Array.from(new Set([...bestUsersTotemsIds, ...bestUsersWheelOfFortuneIds]));

        return {
            numberOfLocationsWithAtLeastFiveTotemScansOrFiveWofDraws: bestUsersIds.length,
            numberOfLocationsWithAtLeastFiveTotemScans: bestUsersTotemsIds.length,
            numberOfLocationsWithAtLeastFiveWofDraws: bestUsersWheelOfFortuneIds.length,
        };
    }

    private async _getLocationsBestUsersTotems(startDate: Date, endDate: Date): Promise<string[]> {
        const MIN_SCAN_COUNT_TO_BE_BEST_USER = 5;
        const aggregation = [
            {
                $match: {
                    scannedAt: {
                        $gt: startDate,
                        $lt: endDate,
                    },
                },
            },
            { $group: { _id: '$nfcId', count: { $sum: 1 } } },
            { $match: { count: { $gte: MIN_SCAN_COUNT_TO_BE_BEST_USER } } },
            {
                $lookup: {
                    from: 'nfcs',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'nfc',
                },
            },
            {
                $unwind: {
                    path: '$nfc',
                    preserveNullAndEmptyArrays: false,
                },
            },
            { $group: { _id: '$nfc.restaurantId' } },
            {
                $lookup: {
                    from: 'restaurants',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                    preserveNullAndEmptyArrays: false,
                },
            },
            { $match: { 'restaurant.active': true } },
            { $project: { _id: 1 } },
        ];

        const result = (await this._scansRepository.aggregate(aggregation)) as { _id: DbId }[];
        return result.map((r) => r._id.toString());
    }

    private async _getLocationsBestUsersWheelOfFortune(startDate: Date, endDate: Date): Promise<string[]> {
        const MIN_GIFT_DRAWS_COUNT_TO_BE_BEST_USER = 5;
        const aggregation = [
            {
                $match: {
                    createdAt: {
                        $gt: startDate,
                        $lt: endDate,
                    },
                },
            },
            {
                $group: {
                    _id: '$restaurantId',
                    count: {
                        $sum: 1,
                    },
                },
            },
            { $match: { count: { $gte: MIN_GIFT_DRAWS_COUNT_TO_BE_BEST_USER } } },
            {
                $lookup: {
                    from: 'restaurants',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                    preserveNullAndEmptyArrays: false,
                },
            },
            { $match: { 'restaurant.active': true } },
            { $project: { _id: 1 } },
        ];

        const result = (await this._giftDrawsRepository.aggregate(aggregation)) as { _id: DbId }[];
        return result.map((r) => r._id.toString());
    }

    private async _getSocialNetworksStats(
        startDate: Date,
        endDate: Date
    ): Promise<{
        numberOfLocationsThatPublishedOnSocialNetworks: number;
        numberOfLocationsThatPublishedPostsOrReels: number;
        numberOfLocationsThatPublishedStories: number;
        numberOfLocationsConnectedToTiktok: number;
        numberOfLocationsThatPublishedOnTiktok: number;
        numberOfLocationsConnectedToAtLeastOneSocialNetwork: number;
    }> {
        const groupByRestaurantIdStage = {
            $group: {
                _id: '$restaurantId',
            },
        };
        const countStage = {
            $count: 'count',
        };

        const numberOfLocationsThatPublishedOnSocialNetworksResult = (await this._postsRepository.aggregate([
            {
                $match: {
                    published: PostPublicationStatus.PUBLISHED,
                    socialCreatedAt: {
                        $gte: startDate,
                        $lte: endDate,
                    },
                    source: PostSource.SOCIAL,
                    author: { $ne: null },
                },
            },
            groupByRestaurantIdStage,
            countStage,
        ])) as { count: number }[];
        const numberOfLocationsThatPublishedOnSocialNetworks = numberOfLocationsThatPublishedOnSocialNetworksResult[0]?.count ?? 0;

        const numberOfLocationsThatPublishedPostsOrReelsResult = (await this._postsRepository.aggregate([
            {
                $match: {
                    published: PostPublicationStatus.PUBLISHED,
                    socialCreatedAt: {
                        $gte: startDate,
                        $lte: endDate,
                    },
                    source: PostSource.SOCIAL,
                    isStory: { $ne: true },
                    author: { $ne: null },
                },
            },
            groupByRestaurantIdStage,
            countStage,
        ])) as { count: number }[];
        const numberOfLocationsThatPublishedPostsOrReels = numberOfLocationsThatPublishedPostsOrReelsResult[0]?.count ?? 0;

        const numberOfLocationsThatPublishedStoriesResult = (await this._postsRepository.aggregate([
            {
                $match: {
                    published: PostPublicationStatus.PUBLISHED,
                    socialCreatedAt: {
                        $gte: startDate,
                        $lte: endDate,
                    },
                    source: PostSource.SOCIAL,
                    isStory: true,
                    author: { $ne: null },
                },
            },
            groupByRestaurantIdStage,
            countStage,
        ])) as { count: number }[];
        const numberOfLocationsThatPublishedStories = numberOfLocationsThatPublishedStoriesResult[0]?.count ?? 0;

        const numberOfLocationsThatPublishedOnTiktokResult = (await this._postsRepository.aggregate([
            {
                $match: {
                    published: PostPublicationStatus.PUBLISHED,
                    socialCreatedAt: {
                        $gte: startDate,
                        $lte: endDate,
                    },
                    key: PlatformKey.TIKTOK,
                    author: { $ne: null },
                },
            },
            groupByRestaurantIdStage,
            countStage,
        ])) as { count: number }[];
        const numberOfLocationsThatPublishedOnTiktok = numberOfLocationsThatPublishedOnTiktokResult[0]?.count ?? 0;

        const numberOfLocationsConnectedToTiktok = await this._getNumberOfActiveLocationsConnectedToTiktok();

        const numberOfLocationsConnectedToAtLeastOneSocialNetwork =
            await this._getNumberOfActiveLocationsConnectedToAtLeastOneSocialNetwork();

        return {
            numberOfLocationsThatPublishedOnSocialNetworks,
            numberOfLocationsThatPublishedPostsOrReels,
            numberOfLocationsThatPublishedStories,
            numberOfLocationsConnectedToTiktok,
            numberOfLocationsThatPublishedOnTiktok,
            numberOfLocationsConnectedToAtLeastOneSocialNetwork,
        };
    }

    private async _getNumberOfActiveLocationsConnectedToTiktok(): Promise<number> {
        const aggregation = [
            {
                $match: {
                    key: PlatformKey.TIKTOK,
                },
            },
            {
                $lookup: {
                    from: 'restaurants',
                    localField: 'restaurantId',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                    preserveNullAndEmptyArrays: false,
                },
            },
            { $match: { 'restaurant.active': true } },
            { $count: 'count' },
        ];

        const result = (await this._platformsRepository.aggregate(aggregation)) as { count: number }[];
        return result[0]?.count ?? 0;
    }

    private async _getNumberOfActiveLocationsConnectedToAtLeastOneSocialNetwork(): Promise<number> {
        const aggregation = [
            {
                $match: {
                    $or: [
                        {
                            key: { $in: [PlatformKey.TIKTOK, PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM] },
                        },
                        {
                            key: PlatformKey.MAPSTR,
                            credentials: { $exists: true, $ne: [] },
                        },
                    ],
                },
            },
            { $group: { _id: '$restaurantId' } },
            {
                $lookup: {
                    from: 'restaurants',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'restaurant',
                },
            },
            {
                $unwind: {
                    path: '$restaurant',
                    preserveNullAndEmptyArrays: false,
                },
            },
            { $match: { 'restaurant.active': true } },
            { $count: 'count' },
        ];

        const result = (await this._platformsRepository.aggregate(aggregation)) as { count: number }[];
        return result[0]?.count ?? 0;
    }
}
