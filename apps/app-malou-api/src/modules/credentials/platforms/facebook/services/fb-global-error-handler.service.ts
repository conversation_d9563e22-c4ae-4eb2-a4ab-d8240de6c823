import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { CreatePlatformDisconnectedNotificationProducer } from ':modules/notifications/queues/create-platform-disconnected-notification/create-platform-disconnected-notification.producer';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

export interface FbError {
    message: string;
    type: string;
    code: number;
    error_subcode: number;
    error_user_title: string;
    error_user_msg: string;
    fbtrace_id: string;
}

export enum FbErrorCode {
    TokenExpired = 190,
    SessionExpired = 102,
    TokenInvalid = 463,
    TokenRevoked = 467,
}

export enum FbErrorSubcode {
    PasswordChanged = 460,
    AppNotInstalled = 458,
    UserCheckpoint = 459,
    UserUnconfirmed = 464,
    SessionInvalid = 492,
}

// todo posts-v2 This Service needs a fix, we desactivated it while finding a solution
export class FbGlobalErrorHandlerService {
    constructor(
        private readonly _createPlatformDisconnectedNotificationProducer: CreatePlatformDisconnectedNotificationProducer,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async handle(error: FbError, credentialId: string): Promise<void> {
        /*
         * Handle Facebook error and remove credential from platform if it's a token error
         * 1. if the credential is at index > 0 in platform.credentials, remove it from the platform
         * 2. if the credential is at index 0 in platform.credentials, remove the userAccessToken from the credential and send a notification to the concerned users
         */
        if (this._isTokenError(error)) {
            const platform = await this._platformsRepository.findOne({
                filter: { credentials: { $elemMatch: { $eq: toDbId(credentialId) } } },
                options: { lean: true },
            });
            if (!platform) {
                logger.error(`[FbGlobalErrorHandlerService] - Platform not found for credential ${credentialId}`);
                return;
            }
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: platform.restaurantId },
                options: { lean: true },
            });

            if (!restaurant?.active) {
                logger.info(`[FbGlobalErrorHandlerService] - Restaurant ${restaurant?._id} is not active`);
                await this._platformsRepository.deleteOne({ filter: { _id: platform._id } });
                logger.info(`[FbGlobalErrorHandlerService] - Deleted platform ${platform._id}`);
                return;
            }

            if ((platform.credentials?.indexOf(toDbId(credentialId)) ?? 0) > 0) {
                await this._platformsRepository.findOneAndUpdate({
                    filter: { _id: platform._id },
                    update: { $pull: { credentials: toDbId(credentialId) } },
                });
                logger.info(`[FbGlobalErrorHandlerService] - Removed credential ${credentialId} from platform ${platform._id}`);
                return;
            }

            if (platform.credentials?.map((c) => c.toString()).indexOf(credentialId) === 0) {
                await this._createPlatformDisconnectedNotificationProducer.sendMessage({
                    disconnectedPlatforms: [
                        {
                            platform: {
                                id: platform._id.toString(),
                                key: platform.key,
                            },
                            restaurant: {
                                id: restaurant._id.toString(),
                                name: restaurant.name,
                            },
                        },
                    ],
                });
            }
        }
    }

    private _isTokenError(error: FbError): boolean {
        const tokenErrorCodes = [FbErrorCode.TokenExpired, FbErrorCode.SessionExpired, FbErrorCode.TokenInvalid, FbErrorCode.TokenRevoked];
        const tokenErrorSubCodes = [
            FbErrorSubcode.PasswordChanged,
            FbErrorSubcode.UserCheckpoint,
            FbErrorSubcode.UserUnconfirmed,
            FbErrorSubcode.SessionInvalid,
        ];

        const { code, error_subcode } = error;
        return tokenErrorCodes.includes(code) || tokenErrorSubCodes.includes(error_subcode);
    }
}
