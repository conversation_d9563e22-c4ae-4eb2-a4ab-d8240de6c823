import { singleton } from 'tsyringe';

import { EntityRepository, FacebookCredentialModel, IFacebookCredential, toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { FacebookCredential } from ':modules/credentials/platforms/facebook/entities/facebook-credential.entity';

@singleton()
export class FacebookCredentialsRepository extends EntityRepository<IFacebookCredential> {
    constructor() {
        super(FacebookCredentialModel);
    }

    async getCredentialById(id: string): Promise<FacebookCredential> {
        const res = await this.findOneOrFail({
            filter: { _id: toDbId(id) },
            options: { lean: true },
        });
        return this._toEntity(res);
    }

    async getCredentialByIdSafely(id: string): Promise<FacebookCredential | null> {
        const res = await this.findOne({
            filter: { _id: toDbId(id) },
            options: { lean: true },
        });
        if (!res) {
            return null;
        }
        return this._toEntity(res);
    }

    async getLastSeenWorkingCredentials(limit: number = 10): Promise<FacebookCredential[]> {
        const res = await this.find({
            filter: {},
            options: { lean: true, limit, sort: { lastSeenWorking: -1 } },
        });
        return res.map(this._toEntity);
    }

    getFbCredentialOrderedByPageAccessWorking = async (fbPageId?: string): Promise<FacebookCredential[] | undefined> => {
        if (!fbPageId) return;

        const res = await this.aggregate([
            {
                $match: {
                    key: PlatformKey.FACEBOOK,
                    pageAccess: { $exists: true },
                    'pageAccess.fbPageId': fbPageId,
                    active: true,
                },
            },
            {
                $addFields: {
                    order: {
                        $filter: {
                            input: '$pageAccess',
                            as: 'p',
                            cond: { $eq: ['$$p.fbPageId', fbPageId] },
                        },
                    },
                },
            },
            { $sort: { 'order.lastSeenWorking': -1 } },
        ]);
        return res.map(this._toEntity);
    };

    private _toEntity(data: IFacebookCredential): FacebookCredential {
        return new FacebookCredential(data);
    }
}
