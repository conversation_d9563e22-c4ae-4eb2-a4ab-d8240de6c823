import { isNil, sampleSize } from 'lodash';
import { singleton } from 'tsyringe';

import { InstagramPageInfoDto } from '@malou-io/package-dto';
import { FacebookApiMediaType, isNotNil, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersInstagram } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { InstagramPagePostsDetails } from ':modules/diagnostics/diagnostic.interfaces';
import { PostInsight } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { InstagramPostsUseCases } from ':modules/posts/platforms/instagram/use-cases';

interface FacebookCredentials {
    userAccessToken: string;
    igPageId: string;
}

export interface InstagramPagePostsDetailsForStoreLocator {
    business_discovery: {
        name: string;
        followers_count: number;
        media_count: number;
        profile_picture_url: string;
        media: {
            data: {
                id: string;
                like_count: number;
                permalink: string;
                media_type: FacebookApiMediaType;
                caption: string;
                media_url: string;
                thumbnail_url: string;
                children: {
                    data: {
                        media_url: string;
                        thumbnail_url: string;
                        media_type: FacebookApiMediaType;
                    }[];
                };
            }[];
        };
        id: string;
    };
    id: string;
}

@singleton()
export class InstagramPageDiscoveryService {
    private CREDENTIALS: FacebookCredentials[] = [];
    private readonly SAMPLE_SIZE = 10;

    constructor(
        private readonly _instagramPostsUseCase: InstagramPostsUseCases,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository
    ) {}

    async getPage(instagramAccountName: string): Promise<InstagramPageInfoDto | null> {
        const credentials = await this._getFacebookCredentials();
        const retryLimit = credentials.length > this.SAMPLE_SIZE ? this.SAMPLE_SIZE : credentials.length;
        let retry = 0;
        while (retry < retryLimit) {
            const credential = credentials[retry];
            if (!credential) {
                logger.error('[MALOUPE] [INSTAGRAM_PAGE_DISCOVERY] credential is undefined', {
                    retry,
                });
                retry++;
                continue;
            }
            try {
                return await facebookCredentialsUseCases.igGetPageInfoForDiagnostic(
                    credential.userAccessToken,
                    credential.igPageId,
                    instagramAccountName
                );
            } catch (error: any) {
                if (error.code === 190 && error.error_subcode === 460) {
                    logger.error('[MALOUPE] [INSTAGRAM_PAGE_DISCOVERY] Error getting Instagram page info, credential not valid', {
                        pageId: credential.igPageId,
                    });
                    retry++;
                } else if (error.code === 100 && error.error_subcode === 33) {
                    logger.error(
                        '[MALOUPE] [INSTAGRAM_PAGE_DISCOVERY] Error getting Instagram page info, credential doesnt have the correct authorization',
                        {
                            pageId: credential.igPageId,
                        }
                    );
                    retry++;
                } else if (error.code === 110 && error.error_subcode === 2207013) {
                    return null;
                } else {
                    throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                        message: '[MALOUPE] Error getting Instagram page info',
                        metadata: { error },
                    });
                }
            }
        }
        throw new MalouError(MalouErrorCode.UNAUTHORIZED, {
            message: 'No Valid credentials',
        });
    }

    async getPagePostsDetails(instagramAccountName: string): Promise<InstagramPagePostsDetails | null> {
        // ! this is an unwanted behavior, we should have only one credential
        const credentials = await this._getFacebookCredentials();
        const retryLimit = credentials.length > this.SAMPLE_SIZE ? this.SAMPLE_SIZE : credentials.length;
        let retry = 0;
        while (retry < retryLimit) {
            const credential = credentials[retry];
            if (!credential) {
                logger.error('[MALOUPE] [INSTAGRAM_PAGE_DISCOVERY] credential is undefined', {
                    retry,
                });
                retry++;
                continue;
            }
            try {
                return await facebookCredentialsUseCases.igGetPagePostsForDiagnostic(
                    credential.userAccessToken,
                    credential.igPageId,
                    instagramAccountName
                );
            } catch (error: any) {
                if (error.code === 190 && error.error_subcode === 460) {
                    logger.error('[MALOUPE] [INSTAGRAM_PAGE_DISCOVERY] Error getting Instagram page posts details, credential not valid ', {
                        pageId: credential.igPageId,
                    });
                    retry++;
                } else {
                    return null;
                }
            }
        }
        logger.error('[MALOUPE] Error getting Instagram page posts details, no valid credentials ');
        return null;
    }

    async getPagePostsDetailsForStoreLocator({
        accountName,
    }: {
        accountName: string;
    }): Promise<InstagramPagePostsDetailsForStoreLocator | null> {
        // ! this is an unwanted behavior, we should have only one credential
        const credentials = await this._getFacebookCredentials();
        const retryLimit = credentials.length > this.SAMPLE_SIZE ? this.SAMPLE_SIZE : credentials.length;

        let retry = 0;
        while (retry < retryLimit) {
            const credential = credentials[retry];
            if (!credential) {
                logger.error('[MALOUPE] [INSTAGRAM_PAGE_DISCOVERY] credential is undefined', {
                    retry,
                });
                retry++;
                continue;
            }
            try {
                return await facebookCredentialsUseCases.igGetPagePostsForStoreLocator({
                    token: credential.userAccessToken,
                    pageId: credential.igPageId,
                    accountName,
                });
            } catch (error: any) {
                if (error.code === 190 && error.error_subcode === 460) {
                    logger.error(
                        '[STORE_LOCATOR] [INSTAGRAM_PAGE_DISCOVERY] Error getting Instagram page posts details, credential not valid',
                        {
                            pageId: credential.igPageId,
                        }
                    );
                    retry++;
                } else {
                    return null;
                }
            }
        }
        logger.error('[STORE_LOCATOR] Error getting Instagram page posts details, no valid credentials');
        return null;
    }

    async getRestaurantPostsDetails(restaurantId: string, filters: PlatformInsightFiltersInstagram): Promise<PostInsight[] | null> {
        try {
            return await this._instagramPostsUseCase.fetchPostsWithInsights(restaurantId, filters, false);
        } catch (error) {
            logger.error('[MALOUPE] [UPDATE_DIAGNOSTIC_WITH_INSTAGRAM_RATING] Error getting Instagram page details', { error });
            return null;
        }
    }

    private async _getFacebookCredentials(): Promise<FacebookCredentials[]> {
        const lastSeenWorkingCredentials = await this._getLastSeenWorkingCredentials();
        if (!lastSeenWorkingCredentials.length) {
            throw new MalouError(MalouErrorCode.UNAUTHORIZED, {
                message: '[MALOUPE] No Facebook credentials found',
            });
        }
        return sampleSize(lastSeenWorkingCredentials, this.SAMPLE_SIZE);
    }

    private async _getLastSeenWorkingCredentials(): Promise<FacebookCredentials[]> {
        if (this.CREDENTIALS.length) {
            return this.CREDENTIALS;
        }
        const lastSeenWorkingCredentials = await this._facebookCredentialsRepository.getLastSeenWorkingCredentials(2 * this.SAMPLE_SIZE);
        this.CREDENTIALS = lastSeenWorkingCredentials
            .map((credential) => {
                const igPageId = credential.pageAccess[0]?.igPageId;
                const userAccessToken = credential.userAccessToken;
                if (isNil(igPageId) || isNil(userAccessToken)) {
                    return null;
                }
                return {
                    userAccessToken,
                    igPageId,
                };
            })
            .filter((credential) => isNotNil(credential));
        return this.CREDENTIALS;
    }
}
