import { NextFunction, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import { GiftDrawsController } from './gift-draws.controller';

@singleton()
export default class GiftDrawsRouter {
    constructor(private readonly _giftDrawsController: GiftDrawsController) {}

    init(router: Router): void {
        router.post('/gift-draws/insights-per-restaurant', (req: any, res: Response, next: NextFunction) =>
            this._giftDrawsController.handleGetGiftDrawInsightsPerRestaurant(req, res, next)
        );

        router.get('/gift-draws/restaurants/:restaurant_id/insights-per-gift', (req: any, res: Response, next: NextFunction) =>
            this._giftDrawsController.handleGetGiftDrawInsightsPerGift(req, res, next)
        );

        router.post(
            '/gift-draws/restaurants/:restaurant_id/wheels-of-fortune/:wheel_of_fortune_id',
            (req: any, res: Response, next: NextFunction) => this._giftDrawsController.handleCreateGiftDraw(req, res, next)
        );

        router.get('/gift-draws/:gift_draw_id/gift-retrieved', (req: any, res: Response, next: NextFunction) =>
            this._giftDrawsController.handleSetGiftDrawRetrieved(req, res, next)
        );

        router.get('/gift-draws/:gift_draw_id', (req: any, res: Response, next: NextFunction) =>
            this._giftDrawsController.handleGetGiftDrawById(req, res, next)
        );

        router.put('/gift-draws/:gift_draw_id/clients/:client_id', (req: any, res: Response, next: NextFunction) =>
            this._giftDrawsController.handleAssignClientToGiftDraw(req, res, next)
        );

        router.get('/gift-draws/:gift_draw_id/send-retrieval-email', (req: any, res: Response, next: NextFunction) =>
            this._giftDrawsController.handleSendRetrievalEmail(req, res, next)
        );

        router.get('/gift-draws/:gift_draw_id/cancel-draw', (req: any, res: Response, next: NextFunction) =>
            this._giftDrawsController.handleCancelDraw(req, res, next)
        );
    }
}
