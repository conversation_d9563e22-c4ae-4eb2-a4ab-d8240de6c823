import { singleton } from 'tsyringe';

import { GetStoreLocatorDraftPagesDto } from '@malou-io/package-dto';

import { GenerateStoreCentralizationPageService } from ':modules/store-locator/services/generate-store-centralization-page/generate-store-centralization-page.service';
import { GenerateStorePageService } from ':modules/store-locator/services/generate-store-page/generate-store-page.service';
import { GetStoreLocatorStorePagesForEditService } from ':modules/store-locator/services/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.service';

@singleton()
export class GetStoreLocatorStorePagesForEditUseCase {
    constructor(
        private readonly _getStoreLocatorStorePagesForEditService: GetStoreLocatorStorePagesForEditService,
        private readonly _generateStoreCentralizationService: GenerateStoreCentralizationPageService,
        private readonly _generateStorePageService: GenerateStorePageService
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorDraftPagesDto> {
        // todo store-locator move this with popin confirmation later on
        await Promise.all([
            this._generateStorePageService.generateMissingStorePages({ organizationId }),
            this._generateStoreCentralizationService.generateMissingCentralizationPages({ organizationId }),
        ]);
        return await this._getStoreLocatorStorePagesForEditService.execute(organizationId);
    }
}
