import { singleton } from 'tsyringe';

import { GetStoreLocatorCentralizationPageDto } from '@malou-io/package-dto';

import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GetStoreLocatorCentralizationPagesService } from ':modules/store-locator/services/get-store-locator-centralization-pages/get-store-locator-centralization-pages.service';

@singleton()
export class GetStoreLocatorCentralizationPageUseCase {
    constructor(private readonly _getStoreLocatorCentralizationPagesService: GetStoreLocatorCentralizationPagesService) {}

    async execute(
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration,
        options: {
            isForEdit?: boolean;
        } = {}
    ): Promise<GetStoreLocatorCentralizationPageDto[] | null> {
        return await this._getStoreLocatorCentralizationPagesService.execute(storeLocatorOrganizationConfig, options);
    }
}
