import { singleton } from 'tsyringe';

import { GetStoreLocatorCentralizationDraftDto } from '@malou-io/package-dto';

import { GetStoreLocatorCentralizationPagesService } from ':modules/store-locator/services/get-store-locator-centralization-pages/get-store-locator-centralization-pages.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class GetStoreLocatorCentralizationPagesForEditUseCase {
    constructor(
        private readonly _getStoreLocatorCentralizationPagesService: GetStoreLocatorCentralizationPagesService,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorCentralizationDraftDto> {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        const centralizationPages =
            (await this._getStoreLocatorCentralizationPagesService.execute(storeLocatorOrganizationConfig, {
                isForEdit: true,
            })) ?? [];

        return {
            centralizationPages,
        };
    }
}
