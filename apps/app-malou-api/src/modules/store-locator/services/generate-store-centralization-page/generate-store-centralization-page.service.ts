import { flatMap } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IStoreLocatorMapPage, toDbId } from '@malou-io/package-models';
import {
    BusinessCategory,
    DEFAULT_IMAGE_DESCRIPTION,
    DEFAULT_PIN_IMAGE_URL,
    DEFAULT_PLACEHOLDER_IMAGE_URL,
    MediaCategory,
    MediaType,
    StoreLocatorLanguage,
    StoreLocatorPageStatus,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GenerateStoreCentralizationPageContentService } from ':modules/store-locator/services/generate-store-centralization-page-content/generate-store-centralization-page-content.service';
import { GetStoreLocatorStorePagesForEditService } from ':modules/store-locator/services/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.service';
import { StoreLocatorCentralizationPageRepository } from ':modules/store-locator/store-locator-centralization-page.repository';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class GenerateStoreCentralizationPageService {
    constructor(
        private readonly _storeLocatorCentralizationPageRepository: StoreLocatorCentralizationPageRepository,
        private readonly _generateStoreCentralizationContentService: GenerateStoreCentralizationPageContentService,
        private readonly _mediasRepository: MediasRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _getStoreLocatorStorePagesForEditService: GetStoreLocatorStorePagesForEditService
    ) {}

    async generateMissingCentralizationPages({ organizationId }: { organizationId: string }): Promise<void> {
        try {
            const [storeLocatorOrganizationConfig, existingCentralizationPages] = await Promise.all([
                this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
                this._storeLocatorCentralizationPageRepository.find({
                    filter: {
                        organizationId,
                        status: StoreLocatorPageStatus.DRAFT,
                    },
                    projection: { _id: 1, lang: 1, restaurantId: 1 },
                    options: { lean: true },
                }),
            ]);
            assert(storeLocatorOrganizationConfig, '[GENERATE_MAP_PAGE] Store Locator Organization Config not found');

            const existingCentralizationPage = existingCentralizationPages[0];

            const missingCentralizationPages = flatMap(storeLocatorOrganizationConfig.languages, (lang) => {
                const hasPageForLang = existingCentralizationPages.some((page) => page.lang === lang);
                return hasPageForLang ? [] : lang;
            });

            if (missingCentralizationPages.length === 0) {
                logger.info('[STORE_LOCATOR] [GENERATE_MAP_PAGE] No missing centralization pages to generate', { organizationId });
                return;
            }

            await Promise.all(
                missingCentralizationPages.map(async (lang) => {
                    try {
                        await this.createNewCentralizationPageContent({
                            storeLocatorOrganizationConfig,
                            existingCentralizationPage,
                            lang,
                        });
                    } catch (error) {
                        logger.error('[STORE_LOCATOR] [GENERATE_MAP_PAGE] Error creating new centralization page content', {
                            organizationId,
                            lang,
                            error,
                        });
                    }
                })
            );

            // Get draft pages to fill in cache and improve performance on first page load
            await this._getStoreLocatorStorePagesForEditService.execute(organizationId);
        } catch (err) {
            logger.error('[STORE_LOCATOR] [GENERATE_MAP_PAGE] Error generating missing centralization pages', {
                organizationId,
                error: err,
            });
        }
    }

    async createNewCentralizationPageContent({
        storeLocatorOrganizationConfig,
        existingCentralizationPage,
        lang,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        existingCentralizationPage: IStoreLocatorMapPage | undefined;
        lang: StoreLocatorLanguage;
    }): Promise<void> {
        const [{ pageUrl, title, twitterDescription, description, keywords }, medias] = await Promise.all([
            this._generateStoreCentralizationContentService.generateWholeCentralizationPageContent({
                storeLocatorOrganizationConfig,
                lang,
            }),
            this._getImagesFromMedia({ existingCentralizationPage, organizationId: storeLocatorOrganizationConfig.organizationId }),
        ]);

        const relativePath = `${lang}${pageUrl}`;

        await this._storeLocatorCentralizationPageRepository.create({
            data: {
                organizationId: toDbId(storeLocatorOrganizationConfig.organizationId),
                lang,
                fullUrl: `${storeLocatorOrganizationConfig.baseUrl}/${relativePath}`,
                relativePath,
                status: StoreLocatorPageStatus.DRAFT,
                blocks: {
                    head: {
                        title,
                        description,
                        twitterDescription,
                        keywords,
                        facebookImageUrl: medias.head[0].url,
                        twitterImageUrl: medias.head[0].url,
                        snippetImageUrl: medias.head[0].url,
                    },
                    map: {
                        pins: {
                            activePin: {
                                url: DEFAULT_PIN_IMAGE_URL,
                                description: DEFAULT_IMAGE_DESCRIPTION,
                            },
                            inactivePin: {
                                url: DEFAULT_PIN_IMAGE_URL,
                                description: DEFAULT_IMAGE_DESCRIPTION,
                            },
                        },
                        popup: {
                            noStoreImage: {
                                url: DEFAULT_PLACEHOLDER_IMAGE_URL,
                                description: DEFAULT_IMAGE_DESCRIPTION,
                            },
                        },
                    },
                },
            },
        });
    }

    private async _getImagesFromMedia({
        existingCentralizationPage,
        organizationId,
    }: {
        existingCentralizationPage: IStoreLocatorMapPage | undefined;
        organizationId: string;
    }): Promise<{
        head: { url: string; description: string }[];
    }> {
        const backup = {
            head: [{ url: DEFAULT_PLACEHOLDER_IMAGE_URL, description: DEFAULT_IMAGE_DESCRIPTION }],
        };

        if (existingCentralizationPage) {
            return {
                head: [{ url: existingCentralizationPage.blocks.head.facebookImageUrl, description: DEFAULT_IMAGE_DESCRIPTION }],
            };
        }
        const firstEligibleRestaurant = await this._restaurantsRepository.findOne({
            filter: {
                organizationId,
                type: BusinessCategory.LOCAL_BUSINESS,
                shouldNotHaveStoreLocatorPage: false,
            },
            projection: { _id: 1 },
            options: { lean: true },
        });

        assert(firstEligibleRestaurant, '[STORE_LOCATOR] [GENERATE_MAP_PAGE] No eligible restaurant found to get media from');
        const restaurantId = firstEligibleRestaurant._id;

        try {
            const medias = await this._mediasRepository.find({
                filter: {
                    restaurantId: restaurantId,
                    category: MediaCategory.ADDITIONAL,
                    type: MediaType.PHOTO,
                },
                projection: { dimensions: 1, urls: 1 },
                options: { lean: true },
            });

            if (!medias || medias.length === 0) {
                logger.warn('[STORE_LOCATOR] [GENERATE_MAP_PAGE] No medias found for restaurant', { restaurantId });
                return backup;
            }

            // todo store-locator pick best images thanks to dimensions

            const mediasPerSection = {
                head: [{ url: medias[0].urls.original, description: DEFAULT_IMAGE_DESCRIPTION }],
            };

            return mediasPerSection;
        } catch (error) {
            logger.error('[STORE_LOCATOR] [GENERATE_MAP_PAGE] Error fetching media URLs', { restaurantId, error });
            return backup;
        }
    }
}
