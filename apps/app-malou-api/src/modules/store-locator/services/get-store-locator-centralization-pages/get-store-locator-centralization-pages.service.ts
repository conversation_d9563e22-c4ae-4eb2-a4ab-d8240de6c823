import { singleton } from 'tsyringe';

import { GetStoreLocatorCentralizationPageDto, getStoreLocatorCentralizationPageValidator } from '@malou-io/package-dto';
import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { FetchStoreLocatorMapHeadBlockService } from ':modules/store-locator/services/fetch-store-map-head/fetch-store-map-head.service';
import { GetStorePagesDataWithUpdatedInformationBlockService } from ':modules/store-locator/services/get-store-pages-data-with-updated-information-block/get-store-pages-data-with-updated-information-block';
import { StoreLocatorCentralizationPageRepository } from ':modules/store-locator/store-locator-centralization-page.repository';

@singleton()
export class GetStoreLocatorCentralizationPagesService {
    constructor(
        private readonly _fetchStoreLocatorMapHeadBlockService: FetchStoreLocatorMapHeadBlockService,
        private readonly _storeLocatorCentralizationPageRepository: StoreLocatorCentralizationPageRepository,
        private readonly _getStorePagesDataWithUpdatedInformationBlockService: GetStorePagesDataWithUpdatedInformationBlockService
    ) {}

    async execute(
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration,
        options: {
            isForEdit?: boolean;
        } = {}
    ): Promise<GetStoreLocatorCentralizationPageDto[] | null> {
        try {
            const organizationId = storeLocatorOrganizationConfig.organization.id;

            const storeLocatorMapPages = await this._storeLocatorCentralizationPageRepository.getStoreLocatorCentralizationPages(
                organizationId,
                {
                    isForEdit: options.isForEdit ?? false,
                }
            );

            if (storeLocatorMapPages.length === 0) {
                logger.warn('[STORE_LOCATOR] [CENTRALIZATION_PAGE] No map page found for organization', { organizationId });
                return null;
            }

            const maps = await Promise.all(
                storeLocatorMapPages.map(async (storeLocatorMapPage) => {
                    const [headBlock, storeList] = await Promise.all([
                        this._fetchStoreLocatorMapHeadBlockService.execute({
                            storeLocatorOrganizationConfig,
                            storeLocatorMapPage,
                            alternateStoreLocatorMapPages: storeLocatorMapPages,
                        }),
                        this._getStoreList(organizationId, storeLocatorMapPage.lang),
                    ]);

                    const mapComponents = {
                        pins: storeLocatorMapPage.blocks.map.pins,
                        popup: storeLocatorMapPage.blocks.map.popup,
                    };

                    const mapPage = {
                        organizationName: storeLocatorOrganizationConfig.organization.name,
                        lang: storeLocatorMapPage.lang,
                        relativePath: storeLocatorMapPage.relativePath,
                        shouldDisplayWhiteMark: storeLocatorOrganizationConfig.shouldDisplayWhiteMark,
                        headBlock,
                        stores: storeList,
                        mapComponents,
                        styles: {
                            ...storeLocatorOrganizationConfig.styles.pages.common,
                            ...storeLocatorOrganizationConfig.styles.pages.map,
                        },
                    };

                    const parsedMapPage = await getStoreLocatorCentralizationPageValidator.parseAsync(mapPage);

                    return parsedMapPage;
                })
            );
            return maps;
        } catch (err) {
            logger.error('[STORE_LOCATOR] [CENTRALIZATION_PAGE] Failed to get map', { err });
            throw err;
        }
    }

    private async _getStoreList(
        organizationId: string,
        lang: StoreLocatorLanguage
    ): Promise<GetStoreLocatorCentralizationPageDto['stores']> {
        const storesWithInformationBlock = await this._getStorePagesDataWithUpdatedInformationBlockService.execute(organizationId, {
            lang,
        });
        return storesWithInformationBlock.map((storeWithInformationBlock) => {
            const { storeLocatorRestaurantPage, informationBlock } = storeWithInformationBlock;

            return {
                id: storeLocatorRestaurantPage.restaurantId.toString(),
                coordinates: informationBlock.coordinates,
                fullAddress: informationBlock.fullAddress,
                phone: informationBlock.phone,
                isNotOpenedYet: informationBlock.isNotOpenedYet,
                restaurantName: informationBlock.restaurantName,
                relativePath: storeLocatorRestaurantPage.relativePath,
                ctas: informationBlock.ctas,
                itineraryUrl: informationBlock.itineraryUrl,
                image: {
                    description: informationBlock.imageDescription,
                    url: informationBlock.imageUrl,
                    mediaId: undefined,
                },
            };
        });
    }
}
