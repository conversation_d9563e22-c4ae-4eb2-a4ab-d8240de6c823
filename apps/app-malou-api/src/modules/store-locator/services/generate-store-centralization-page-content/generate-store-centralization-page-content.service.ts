import { uniq } from 'lodash';
import { singleton } from 'tsyringe';

import { AiInteractionRelatedEntityCollection, GenerateStoreLocatorContentType, StoreLocatorLanguage } from '@malou-io/package-utils';

import {
    AiStoreLocatorContentService,
    AiStoreLocatorContentType,
    GenerateStoreLocatorContentPayload,
} from ':microservices/ai-store-locator-content-generator.service';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';

interface WholeCentralizationPageContent {
    pageUrl: string;
    title: string;
    description: string;
    twitterDescription: string;
    keywords: string;
}

@singleton()
export class GenerateStoreCentralizationPageContentService {
    constructor(private readonly _aiStoreLocatorContentService: AiStoreLocatorContentService) {}

    async generateWholeCentralizationPageContent({
        storeLocatorOrganizationConfig,
        lang,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<WholeCentralizationPageContent> {
        const payload = await this._getLambdaPayload({
            storeLocatorOrganizationConfig,
            lang,
        });

        const [{ text: pageUrl }, { text: title }, { text: twitterDescription }, { text: description }, { keywords }] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.MAP_PAGE_URL_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.MAP_BLOCK_TITLE_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.MAP_TWITTER_DESCRIPTION_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.MAP_DESCRIPTION_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.MAP_KEYWORDS_GENERATION,
                payload,
            }),
        ]);

        return {
            pageUrl,
            title,
            twitterDescription,
            description,
            keywords: keywords.join(', '),
        };
    }

    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>({
        type,
        payload,
    }: {
        type: T;
        payload: GenerateStoreLocatorContentPayload<AiInteractionRelatedEntityCollection.STORE_LOCATOR_CENTRALIZATION_PAGE>['restaurantData'];
    }): Promise<AiStoreLocatorContentType<T>> {
        const response = await this._aiStoreLocatorContentService.generateStoreLocatorContent(type, payload);
        return response.aiResponse;
    }

    private async _getLambdaPayload({
        storeLocatorOrganizationConfig,
        lang,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<GenerateStoreLocatorContentPayload['restaurantData']> {
        const organizationKeywords = storeLocatorOrganizationConfig.aiSettings.keywords.map(({ text }) => text);
        const organizationBricks: string[] = storeLocatorOrganizationConfig.aiSettings.keywords.flatMap((keyword) =>
            keyword.bricks.map((brick) => brick[lang] || brick.text)
        );

        return await this.getLambdaPayloadFromOrganizationParams({
            organizationConfig: {
                organizationName: storeLocatorOrganizationConfig.organization.name,
                keywords: organizationKeywords,
                tone: storeLocatorOrganizationConfig.aiSettings.tone,
                languageStyle: storeLocatorOrganizationConfig.aiSettings.languageStyle,
                bricks: organizationBricks,
            },
            lang,
        });
    }

    async getLambdaPayloadFromOrganizationParams({
        organizationConfig,
        lang,
    }: {
        organizationConfig: {
            organizationName: string;
            keywords: string[];
            bricks: string[];
            tone: string[];
            languageStyle: string;
        };
        lang: StoreLocatorLanguage;
    }): Promise<GenerateStoreLocatorContentPayload['restaurantData']> {
        const {
            keywords: organizationKeywords,
            languageStyle,
            organizationName,
            tone: organizationTone,
            bricks: organizationBricks,
        } = organizationConfig;

        const keywords = uniq(organizationKeywords.map((keyword) => keyword.toLowerCase().trim()));
        const brandTone = uniq([...organizationTone, languageStyle].map((tone) => tone.toLowerCase().trim()));
        const bricks = uniq(organizationBricks.map((brick) => brick.toLowerCase().trim()));

        return {
            organizationName,
            address: {},
            keywords,
            brandTone,
            bricks,
            language: lang,
        };
    }
}
