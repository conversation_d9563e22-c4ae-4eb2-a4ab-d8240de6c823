import { flatMap } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import {
    BusinessCategory,
    DEFAULT_IMAGE_DESCRIPTION,
    DEFAULT_PLACEHOLDER_IMAGE_URL,
    isNotNil,
    mapStoreLocatorLanguageToMalouLocale,
    MediaCategory,
    MediaType,
    StoreLocatorLanguage,
    StoreLocatorPageStatus,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GenerateStorePageContentService } from ':modules/store-locator/services/generate-store-page-content/generate-store-page-content.service';
import { GetStoreCallToActionsSuggestionsService } from ':modules/store-locator/services/get-store-call-to-actions-suggestions/get-store-call-to-actions-suggestions.service';
import { GetStoreLocatorStorePagesForEditService } from ':modules/store-locator/services/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { Translation } from ':services/translation.service';

@singleton()
export class GenerateStorePageService {
    private readonly _MAX_CTAS_COUNT = 4;

    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _generateStorePageContentService: GenerateStorePageContentService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _getStoreCallToActionsSuggestionsService: GetStoreCallToActionsSuggestionsService,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _getStoreLocatorStorePagesForEditService: GetStoreLocatorStorePagesForEditService,
        private readonly _translate: Translation
    ) {}

    async generateMissingStorePages({ organizationId }: { organizationId: string }): Promise<void> {
        try {
            const [storeLocatorOrganizationConfig, restaurants, existingRestaurantPages] = await Promise.all([
                this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
                this._restaurantsRepository.find({
                    filter: {
                        organizationId,
                        type: BusinessCategory.LOCAL_BUSINESS,
                        shouldNotHaveStoreLocatorPage: false,
                    },
                    projection: { _id: 1 },
                    options: { lean: true },
                }),
                this._storeLocatorRestaurantPageRepository.find({
                    filter: {
                        organizationId,
                        status: StoreLocatorPageStatus.DRAFT,
                    },
                    projection: { _id: 1, lang: 1, restaurantId: 1 },
                    options: { lean: true },
                }),
            ]);
            assert(storeLocatorOrganizationConfig, 'Store Locator Organization Config not found');
            assert(restaurants.length > 0, 'No restaurants found for the given organization');

            const missingRestaurantPages = flatMap(
                restaurants.map((restaurant) =>
                    storeLocatorOrganizationConfig.desiredLanguages
                        .filter(
                            (lang) =>
                                !existingRestaurantPages.some(
                                    (page) => page.restaurantId.toString() === restaurant._id.toString() && page.lang === lang
                                )
                        )
                        .map((lang) => ({
                            restaurantId: restaurant._id.toString(),
                            lang,
                        }))
                )
            );

            await Promise.all(
                missingRestaurantPages.map(async ({ restaurantId, lang }) => {
                    try {
                        await this.createNewPageContent({
                            restaurantId,
                            storeLocatorOrganizationConfig,
                            lang,
                        });
                    } catch (error) {
                        logger.error('[STORE_LOCATOR] Error creating new store page content', {
                            restaurantId,
                            lang,
                            error,
                        });
                    }
                })
            );

            // Get draft pages to fill in cache and improve performance on first page load
            await this._getStoreLocatorStorePagesForEditService.execute(organizationId);
        } catch (err) {
            logger.error('[STORE_LOCATOR] Error generating missing store pages', {
                organizationId,
                error: err,
            });
        }
    }

    async generateRestaurantPage({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<void> {
        const storeLocatorRestaurantPage = await this._storeLocatorRestaurantPageRepository.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                lang,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: { lean: true },
        });

        if (storeLocatorRestaurantPage) {
            return await this.updateExistingPageContent({ storeLocatorRestaurantPage, restaurantId, storeLocatorOrganizationConfig });
        }

        return await this.createNewPageContent({ restaurantId, storeLocatorOrganizationConfig, lang });
    }

    async updateExistingPageContent({
        storeLocatorRestaurantPage,
        restaurantId,
        storeLocatorOrganizationConfig,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
    }): Promise<void> {
        const {
            title,
            metaDescription,
            twitterDescription,
            descriptions,
            ctaTitle,
            reviewsTitle,
            galleryTitle,
            gallerySubtitle,
            socialNetworksTitle,
            faqs,
        } = await this._generateStorePageContentService.generateWholePageContent({
            restaurantId,
            storeLocatorOrganizationConfig,
            lang: storeLocatorRestaurantPage.lang,
        });

        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text).join(', ');

        await this._storeLocatorRestaurantPageRepository.updateOne({
            filter: { _id: storeLocatorRestaurantPage._id },
            update: {
                hasBeenUpdated: true,
                blocks: {
                    head: {
                        ...storeLocatorRestaurantPage.blocks.head,
                        title,
                        description: metaDescription,
                        twitterDescription,
                        keywords,
                    },
                    information: {
                        ...storeLocatorRestaurantPage.blocks.information,
                        title,
                    },
                    gallery: {
                        ...storeLocatorRestaurantPage.blocks.gallery,
                        title: galleryTitle,
                        subtitle: gallerySubtitle,
                    },
                    reviews: {
                        ...storeLocatorRestaurantPage.blocks.reviews,
                        title: reviewsTitle,
                    },
                    callToActions: {
                        ...storeLocatorRestaurantPage.blocks.callToActions,
                        title: ctaTitle,
                    },
                    descriptions: {
                        ...storeLocatorRestaurantPage.blocks.descriptions,
                        items: descriptions.map((description, index) => ({
                            ...storeLocatorRestaurantPage.blocks.descriptions.items[index],
                            title: description.title,
                            blocks: description.sections.map(({ subtitle, text }) => ({
                                title: subtitle,
                                text,
                            })),
                        })),
                    },
                    socialNetworks: {
                        ...storeLocatorRestaurantPage.blocks.socialNetworks,
                        title: socialNetworksTitle,
                    },
                    faq: {
                        title: storeLocatorRestaurantPage.blocks.faq.title,
                        items: faqs,
                    },
                },
            },
        });
    }

    async createNewPageContent({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<void> {
        const [
            {
                pageUrl,
                title,
                metaDescription,
                twitterDescription,
                descriptions,
                ctaTitle,
                reviewsTitle,
                galleryTitle,
                gallerySubtitle,
                socialNetworksTitle,
                faqs,
            },
            { primaryCta, ctas },
            medias,
        ] = await Promise.all([
            this._generateStorePageContentService.generateWholePageContent({
                restaurantId,
                storeLocatorOrganizationConfig,
                lang,
            }),
            this._getCtas({ restaurantId, lang }),
            this._getImagesFromMedia(restaurantId),
        ]);

        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text).join(', ');
        const relativePath = `${lang}${pageUrl}`;

        await this._storeLocatorRestaurantPageRepository.create({
            data: {
                restaurantId: toDbId(restaurantId),
                organizationId: toDbId(storeLocatorOrganizationConfig.organizationId),
                lang,
                fullUrl: `${storeLocatorOrganizationConfig.baseUrl}/${relativePath}`,
                relativePath,
                status: StoreLocatorPageStatus.DRAFT,
                hasBeenUpdated: true,
                blocks: {
                    head: {
                        title,
                        description: metaDescription,
                        twitterDescription,
                        // todo store-locator remove ? will be generated on the fly
                        keywords, // todo store-locator translation check
                        // todo store-locator remove ? will be generated on the fly
                        facebookImageUrl: medias.head[0].url,
                        twitterImageUrl: medias.head[0].url,
                        snippetImageUrl: medias.head[0].url,
                    },
                    information: {
                        title,
                        image: medias.information[0],
                        ctas: [primaryCta],
                    },
                    gallery: {
                        title: galleryTitle,
                        subtitle: gallerySubtitle,
                        images: medias.gallery,
                    },
                    reviews: {
                        title: reviewsTitle,
                        cta: primaryCta,
                    },
                    callToActions: {
                        title: ctaTitle,
                        ctas,
                    },
                    descriptions: {
                        items: descriptions.map((description, index) => ({
                            title: description.title,
                            image: medias.descriptions[index],
                            blocks: description.sections.map(({ subtitle, text }) => ({
                                title: subtitle,
                                text,
                            })),
                        })),
                    },
                    socialNetworks: {
                        title: socialNetworksTitle,
                    },
                    faq: {
                        title: this._translate
                            .fromLang({ lang: mapStoreLocatorLanguageToMalouLocale(lang) })
                            .store_locator.faq_block.title(),
                        items: faqs,
                    },
                },
            },
        });
    }

    private async _getImagesFromMedia(restaurantId: string): Promise<{
        head: { url: string; description: string }[];
        information: { url: string; description: string }[];
        gallery: { url: string; description: string }[];
        descriptions: { url: string; description: string }[];
    }> {
        const backup = {
            head: [{ url: DEFAULT_PLACEHOLDER_IMAGE_URL, description: DEFAULT_IMAGE_DESCRIPTION }],
            information: [{ url: DEFAULT_PLACEHOLDER_IMAGE_URL, description: DEFAULT_IMAGE_DESCRIPTION }],
            gallery: Array.from({ length: 7 }, () => ({
                url: DEFAULT_PLACEHOLDER_IMAGE_URL,
                description: DEFAULT_IMAGE_DESCRIPTION,
            })),
            descriptions: Array.from({ length: 2 }, () => ({
                url: DEFAULT_PLACEHOLDER_IMAGE_URL,
                description: DEFAULT_IMAGE_DESCRIPTION,
            })),
        };

        try {
            const medias = await this._mediasRepository.find({
                filter: {
                    restaurantId: toDbId(restaurantId),
                    category: MediaCategory.ADDITIONAL,
                    type: MediaType.PHOTO,
                },
                projection: { dimensions: 1, urls: 1 },
                options: { lean: true },
            });

            if (!medias || medias.length === 0) {
                logger.warn('[STORE_LOCATOR] No medias found for restaurant', { restaurantId });
                return backup;
            }

            // todo store-locator pick best images thanks to dimensions

            // Dispatch medias into information, gallery, ... to have the most different images possible, if there are more than needed, and reusing them the least if images count is not enough
            // information has 1 image, gallery 7, descriptions 2 and head 2
            const usedMediasOccurrence: Record<string, number> = {};
            medias.forEach((media) => (usedMediasOccurrence[media._id.toString()] = usedMediasOccurrence[media._id.toString()] ?? 0));

            const getNextMedia = (): string => {
                const minimumOccurrence = Math.min(...Object.values(usedMediasOccurrence));
                const mediasUsedTheLeast = Object.keys(usedMediasOccurrence).filter(
                    (mediaId) => usedMediasOccurrence[mediaId] === minimumOccurrence
                );
                const nextMediaId = mediasUsedTheLeast[Math.floor(Math.random() * mediasUsedTheLeast.length)];
                usedMediasOccurrence[nextMediaId]++;

                return medias.find((media) => media._id.toString() === nextMediaId)?.urls?.original ?? DEFAULT_PLACEHOLDER_IMAGE_URL;
            };

            const mediasPerSection = {
                head: [{ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION }],
                information: [{ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION }],
                gallery: Array.from({ length: 7 }, () => ({ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION })),
                descriptions: Array.from({ length: 2 }, () => ({ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION })),
            };

            return mediasPerSection;
        } catch (error) {
            logger.error('[STORE_LOCATOR] Error fetching media URLs', { restaurantId, error });
            return backup;
        }
    }

    private async _getCtas({ restaurantId, lang }: { restaurantId: string; lang: StoreLocatorLanguage }): Promise<{
        primaryCta: { text: string; url: string };
        ctas: { text: string; url: string }[];
    }> {
        const language = mapStoreLocatorLanguageToMalouLocale(lang);
        const ctaTexts = {
            orderUrl: this._translate.fromLang({ lang: language }).store_locator.ctas.order(),
            reservationUrl: this._translate.fromLang({ lang: language }).store_locator.ctas.reservation(),
            menuUrl: this._translate.fromLang({ lang: language }).store_locator.ctas.menu(),
            website: this._translate.fromLang({ lang: language }).store_locator.ctas.website(),
            itinerary: this._translate.fromLang({ lang: language }).store_locator.ctas.itinerary(),
        };

        const ctas = await this._getStoreCallToActionsSuggestionsService.execute(restaurantId);

        const primaryCtaKey =
            ['orderUrl', 'reservationUrl', 'menuUrl', 'website', 'itinerary'].find((key) => isNotNil(ctas[key])) ?? Object.keys(ctas)[0];

        const primaryCta = primaryCtaKey
            ? {
                  text: ctaTexts[primaryCtaKey] ?? primaryCtaKey,
                  url: ctas[primaryCtaKey],
              }
            : {
                  text: 'Backup',
                  url: 'https://example.com', // Fallback URL if no primary CTA is found
              };

        return {
            primaryCta,
            ctas: Object.entries(ctas)
                .map(([key, url]) => ({
                    text: ctaTexts[key] ?? key,
                    url,
                }))
                .slice(0, this._MAX_CTAS_COUNT), // Limit to 4 CTAs
        };
    }
}
