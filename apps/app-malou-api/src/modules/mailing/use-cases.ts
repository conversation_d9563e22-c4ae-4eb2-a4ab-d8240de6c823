import { AWSError } from 'aws-sdk';
import { SendCustomVerificationEmailResponse } from 'aws-sdk/clients/ses';
import { PromiseResult } from 'aws-sdk/lib/request';
import { omit } from 'lodash';
import assert from 'node:assert/strict';
import SESTransport from 'nodemailer/lib/ses-transport';
import { autoInjectable } from 'tsyringe';

import { IUser, toDbId } from '@malou-io/package-models';
import { APP_DEFAULT_LANGUAGE, EmailCategory, EmailType, MalouErrorCode, ReportType, Role } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { AWSSESEmailTemplateName } from ':helpers/enums/AWS-SES-email-template.enum';
import { logger } from ':helpers/logger';
import { getAWSSESEmailTemplateName } from ':helpers/utils';
import { ApiKeysRepository } from ':modules/api-keys/api-keys.repository';
import CampaignsRepository from ':modules/campaigns/campaigns.repository';
import ClientsRepository from ':modules/clients/clients.repository';
import * as adminEmailUseCases from ':modules/mailing/admin-email/use-cases';
import { AiEmailUseCases } from ':modules/mailing/ai/use-cases';
import { EmailSenderService, MailOptions } from ':modules/mailing/email-sender.service';
import { FeedbackEmailUseCases } from ':modules/mailing/feedbacks/use-cases';
import ':modules/mailing/handlebars-registers';
import { EmailData, Receiver } from ':modules/mailing/interface';
import * as messagesEmailUseCases from ':modules/mailing/messages/use-cases';
import { MobileAppEmailUseCases } from ':modules/mailing/mobile-app/use-cases';
import * as postEmailUseCases from ':modules/mailing/posts/use-cases';
import * as reviewBoosterUseCases from ':modules/mailing/review-booster/use-cases';
import * as userEmailUseCases from ':modules/mailing/user-email/use-cases';
import { WheelsOfFortuneEmailUseCases } from ':modules/mailing/wheels-of-fortune/use-cases';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';

@autoInjectable()
export default class MailingUseCases {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _apiKeysRepository: ApiKeysRepository,
        private readonly _clientsRepository: ClientsRepository,
        private readonly _campaignsRepository: CampaignsRepository,
        private readonly _aiEmailUseCases: AiEmailUseCases,
        private readonly _emailSenderService: EmailSenderService,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        private readonly _mobileAppEmailUseCases: MobileAppEmailUseCases,
        private readonly _feedbackEmailUseCases: FeedbackEmailUseCases,
        private readonly _wheelsOfFortuneEmailUseCases: WheelsOfFortuneEmailUseCases
    ) {}

    sendEmail = async (
        emailCategory: string,
        emailType: EmailType | ReportType,
        emailData: EmailData
    ): Promise<PromiseResult<SendCustomVerificationEmailResponse, AWSError> | SESTransport.SentMessageInfo | null> => {
        if (!Config.settings.sendEmail) {
            return null;
        }
        logger.info('[MAILING] Start preparing email - ', { emailCategory, emailType, emailData });
        let data: MailOptions | null = null;

        try {
            switch (emailCategory) {
                case EmailCategory.ADMIN_NOTIF:
                    data = await this.prepareAdminNotificationEmail(emailType, emailData);
                    break;
                case EmailCategory.USER_NOTIF:
                    data = await this.prepareUserNotificationEmail(emailType, emailData);
                    break;
                case EmailCategory.REVIEW_BOOSTER:
                    data = await this.prepareReviewBoosterEmail(emailType, emailData);
                    break;
                case EmailCategory.REVIEW_REPLY:
                    data = await this.prepareReviewReplyEmail(emailData);
                    break;
                case EmailCategory.FEEDBACK_NOTIFICATION:
                    data = await this.prepareFeedbackNotificationEmail(emailType, emailData);
                    break;
                case EmailCategory.MESSAGES_NOTIFICATION:
                    data = await this.prepareMessagesNotificationEmail(emailType, emailData);
                    break;
                case EmailCategory.POST_NOTIFICATION:
                    data = await this.preparePostNotificationEmail(emailType, emailData);
                    break;
                case EmailCategory.AI_NOTIFICATION:
                    data = await this.prepareAiNotificationEmail(emailType, emailData);
                    break;
                case EmailCategory.MOBILE_APP_NOTIFICATION:
                    data = await this.prepareMobileAppNotificationEmail(emailType, emailData);
                    break;
                case EmailCategory.WHEEL_OF_FORTUNE_NOTIFICATION:
                    data = await this.prepareWheelOfFortuneNotificationEmail(emailType, emailData);
                    break;
                default:
                    logger.error('[MAILING] - Email category not found', emailCategory);
                    throw new MalouError(MalouErrorCode.EMAIL_INVALID_CATEGORY, {
                        metadata: {
                            emailCategory,
                            emailType,
                            data: emailData,
                        },
                    });
            }
        } catch (error) {
            logger.error('[MAILING] - Error creating email', { error, emailCategory, emailType });
            throw new MalouError(MalouErrorCode.EMAIL_NOT_SENT, {
                metadata: {
                    error,
                    emailCategory,
                    emailType,
                    data: emailData,
                },
            });
        }

        if (!data) {
            logger.error('[MAILING] - Error creating email data', { emailCategory, emailType });
            throw new MalouError(MalouErrorCode.EMAIL_NOT_SENT, {
                metadata: {
                    emailCategory,
                    emailType,
                    data: emailData,
                },
            });
        }

        try {
            logger.info('[MAILING] Email prepared - ', { ...omit(data, 'html', 'text') });
            let emailSent: PromiseResult<SendCustomVerificationEmailResponse, AWSError> | SESTransport.SentMessageInfo;
            if (emailType === EmailType.EMAIL_VERIFICATION) {
                if (typeof data.to !== 'string') {
                    throw new MalouError(MalouErrorCode.INVALID_DATA, {
                        metadata: {
                            emailCategory,
                            data,
                        },
                    });
                }
                const lang = emailData.user?.defaultLanguage ?? APP_DEFAULT_LANGUAGE;
                if (!emailData.user?.defaultLanguage) {
                    logger.warn('[MAILING] - No language found for user', { emailData });
                }
                logger.info('[MAILING] Start sending verification email - ', { lang });
                const response: PromiseResult<SendCustomVerificationEmailResponse, AWSError> =
                    await this._emailSenderService.sendSESIdentityVerificationEmail({
                        EmailAddress: data.to,
                        TemplateName: getAWSSESEmailTemplateName(`${AWSSESEmailTemplateName.VERIFICATION_EMAIL}_${lang}`),
                    });
                emailSent = response;
            } else {
                logger.info('[MAILING] Start sending email - ', { emailCategory, emailType, emailData, data: omit(data, 'html', 'text') });
                const response: SESTransport.SentMessageInfo = await this._emailSenderService.sendEmail(data);
                const mailId = response?.response;
                if (mailId && emailCategory === EmailCategory.REVIEW_BOOSTER && emailType === EmailType.REVIEW_BOOSTER) {
                    await this._campaignsRepository.findOneAndUpdate({
                        filter: { _id: emailData.campaignId, 'contactInteractions.clientId': emailData.clientId },
                        update: { 'contactInteractions.$.awsMailId': mailId },
                    });
                }
                emailSent = response;
            }

            logger.info('[MAILING] Email sent');
            return emailSent;
        } catch (error) {
            logger.error('[MAILING_AWS_SES_SEND]', { error, emailCategory, emailType });
            throw new MalouError(MalouErrorCode.EMAIL_NOT_SENT, {
                metadata: {
                    error,
                    emailCategory,
                    emailType,
                    data: emailData,
                },
            });
        }
    };

    prepareAdminNotificationEmail = async (emailType: string, { restaurantId }: EmailData): Promise<MailOptions | null> => {
        assert(restaurantId, '[prepareAdminNotificationEmail] Missing restaurantId');
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            projection: { _id: 1, name: 1, access: 1 },
            options: { lean: true },
        });

        switch (emailType) {
            case EmailType.UPDATE:
                return adminEmailUseCases.getUpdateEmail(restaurant, [Config.settings.adminUpdatesNotificationEmail]);
            default:
                return null;
        }
    };

    prepareUserNotificationEmail = async (
        emailType: string,
        { restaurantId, userId, url, platformKey }: EmailData
    ): Promise<MailOptions | null> => {
        assert(userId, '[prepareUserNotificationEmail] Missing userId');
        const user = await this._usersRepository.findOneOrFail({
            filter: { _id: toDbId(userId) },
            projection: { _id: 1, email: 1, name: 1, defaultLanguage: 1 },
            options: { lean: true },
        });
        switch (emailType) {
            case EmailType.WRONG_PLATFORM_ACCESS:
                assert(restaurantId, '[prepareUserNotificationEmail] Missing restaurantId');
                assert(platformKey, '[prepareUserNotificationEmail] Missing platformKey');
                const restaurantBasicUsers = await this.getRestaurantBasicUsers(restaurantId);
                if (!restaurantBasicUsers?.length) {
                    logger.error('[MAILING] - Error found 0 users');
                    throw new MalouError(MalouErrorCode.MAILING_NO_USERS, {
                        metadata: {
                            restaurantId,
                            emailType,
                            userId,
                            platformKey,
                        },
                    });
                }
                const receiversForWrongAccess = restaurantBasicUsers.map((u) => u.email).join(', ');
                return userEmailUseCases.getWrongPlatformAccessEmail(restaurantId, receiversForWrongAccess, platformKey, user);
            case EmailType.RESET_PASSWORD:
                assert(url, '[prepareUserNotificationEmail] Missing url');
                return userEmailUseCases.getResetPasswordEmail({ user, url });
            case EmailType.CONFIRM_CREATE_ACCOUNT:
                assert(url, '[prepareUserNotificationEmail] Missing url');
                return userEmailUseCases.getConfirmCreateAccountEmail({ user: { ...user }, url });
            default:
                return null;
        }
    };

    prepareReviewBoosterEmail = async (
        emailType: string,
        { restaurantId, campaignId, clientId, emailData, userId }: EmailData
    ): Promise<MailOptions> => {
        const apiKey = await this._apiKeysRepository.findOneOrFail({ filter: { name: 'email' } });

        if (emailType === EmailType.EMAIL_VERIFICATION) {
            return { to: emailData.to };
        }

        assert(restaurantId, '[prepareReviewBoosterEmail] Missing restaurantId');
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            projection: { name: 1, logo: 1 },
            options: { lean: true, populate: [{ path: 'logo' }] },
        });

        if (emailType === EmailType.REVIEW_BOOSTER_TEST) {
            assert(userId, '[prepareReviewBoosterEmail] Missing userId');
            const user = await this._usersRepository.findOneOrFail({
                filter: { _id: toDbId(userId) },
                projection: { name: 1, lastname: 1, defaultLanguage: 1 },
                options: { lean: true },
            });
            const fakeCampaign: any = {
                _id: 'test',
                platformKey: emailData.platformKey,
                content: {
                    from: emailData.from,
                    object: emailData.object,
                    messageHTML: emailData.messageHTML,
                },
            };
            const fakeClient: any = {
                _id: 'test',
                firstName: user.name,
                lastName: user.lastname,
                email: emailData.to,
                language: user.defaultLanguage,
            };
            return reviewBoosterUseCases.getCampaignEmail(apiKey?.apiKey, fakeCampaign, restaurant, fakeClient);
        }

        assert(campaignId, '[prepareReviewBoosterEmail] Missing campaignId');
        assert(clientId, '[prepareReviewBoosterEmail] Missing clientId');
        const [campaign, client] = await Promise.all([
            this._campaignsRepository.findOneOrFail({
                filter: { _id: toDbId(campaignId) },
                projection: { content: 1, platformKey: 1 },
                options: { lean: true },
            }),
            this._clientsRepository.findOneOrFail({
                filter: { _id: toDbId(clientId) },
                projection: { firstName: 1, lastName: 1, email: 1, language: 1 },
                options: { lean: true },
            }),
        ]);
        return reviewBoosterUseCases.getCampaignEmail(apiKey?.apiKey, campaign, restaurant, client);
    };

    prepareReviewReplyEmail = async ({ clientId, emailData }: EmailData): Promise<MailOptions> => {
        assert(clientId, '[prepareReviewReplyEmail] Missing clientId');
        const client = await this._clientsRepository.findOneOrFail({
            filter: { _id: toDbId(clientId) },
            projection: { email: 1 },
            options: { lean: true },
        });
        const base64Name = Buffer.from(emailData.content.from.name).toString('base64');
        const from = `=?UTF-8?B?${base64Name}?= <${emailData.content.from.email}>`;
        return {
            from,
            to: client.email,
            subject: emailData.content.object,
            html: emailData.content.messageHTML,
        };
    };

    prepareFeedbackNotificationEmail = async (
        emailType: string,
        { userId, feedbackId, feedbackMessage, emailData }: EmailData<{ to: Receiver }>
    ): Promise<MailOptions | null> => {
        assert(userId, '[prepareFeedbackNotificationEmail] Missing userId');
        const user = await this._usersRepository.findOneOrFail({
            filter: { _id: toDbId(userId) },
            options: { lean: true },
        });
        const post = await this._postsRepository.findOneOrFail({
            filter: { feedbackId },
            options: { lean: true },
        });
        // The post may have been deleted
        if (!post) {
            logger.error('[MAILING] - Error finding post', { feedbackId, emailType });
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                metadata: {
                    feedbackId,
                },
                message: 'prepareFeedbackNotificationEmail - Post not found',
            });
        }

        const receiver = emailData?.to;
        assert(receiver, '[prepareFeedbackNotificationEmail] Missing receiver');
        const userReceiver = await this._usersRepository.findOneOrFail({ filter: { email: receiver.email }, options: { lean: true } });
        switch (emailType) {
            case EmailType.NEW_FEEDBACK_MESSAGE:
                const messageText = feedbackMessage?.text?.replaceAll('{USER}', '@') ?? '';
                return this._feedbackEmailUseCases.getNewFeedbackMessageEmail(user, userReceiver, post, messageText);
            case EmailType.CLOSED_FEEDBACK:
                return this._feedbackEmailUseCases.getClosedFeedbackEmail(user, userReceiver, post);
            case EmailType.OPENED_FEEDBACK:
                return this._feedbackEmailUseCases.getOpenedFeedbackEmail(user, userReceiver, post);
            default:
                return null;
        }
    };

    prepareMessagesNotificationEmail = async (emailType: string, { restaurant, user, message }: EmailData): Promise<MailOptions | null> => {
        // Switch is unnecessary here but i did it for eventual future messages notification types
        switch (emailType) {
            case EmailType.NEW_MESSAGE_RECEIVED:
                assert(restaurant, '[prepareMessagesNotificationEmail] Missing restaurant');
                assert(message, '[prepareMessagesNotificationEmail] Missing message');
                assert(user, '[prepareMessagesNotificationEmail] Missing user');
                return messagesEmailUseCases.getNewMessageEmail(restaurant, message, user);
            default:
                return null;
        }
    };

    getRestaurantAdmins = async (restaurantId: string): Promise<IUser[]> => {
        try {
            const admins = await this._usersRepository.find({
                filter: { role: Role.ADMIN },
                options: { populate: [{ path: 'restaurants' }], lean: true },
            });
            const restaurantAdmins = admins.filter((admin) =>
                admin.restaurants?.find((rest) => String(rest.restaurantId) === String(restaurantId))
            );
            return restaurantAdmins;
        } catch (e) {
            logger.error('[MAILING] - Error finding admins', e);
            return [];
        }
    };

    getRestaurantBasicUsers = async (restaurantId: string): Promise<IUser[]> => {
        try {
            const users = await this._usersRepository.find({
                filter: { role: 'malou-basic' },
                options: { populate: [{ path: 'restaurants' }], lean: true },
            });

            const restaurantUsers = users.filter((user) =>
                user.restaurants?.find((rest) => String(rest.restaurantId) === String(restaurantId))
            );
            return restaurantUsers;
        } catch (err) {
            logger.error('[MAILING] - Error finding users', err);
            throw new MalouError(MalouErrorCode.MAILING_NO_USERS, {
                metadata: {
                    restaurantId,
                },
            });
        }
    };

    preparePostNotificationEmail = async (emailType: string, { userId, restaurantId, post }: EmailData): Promise<MailOptions | null> => {
        try {
            assert(userId, '[preparePostNotificationEmail] Missing userId');
            const user = await this._usersRepository.findOneOrFail({
                filter: { _id: toDbId(userId) },
                options: { lean: true },
            });

            assert(restaurantId, '[preparePostNotificationEmail] Missing restaurantId');
            const restaurant = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: toDbId(restaurantId) },
                options: { lean: true },
            });

            switch (emailType) {
                case EmailType.POST_LOCATION_EXPIRED:
                    assert(post, '[preparePostNotificationEmail] Missing post');
                    return postEmailUseCases.getExpiredLocationPostEmail(user, restaurant, post);
                default:
                    return null;
            }
        } catch (error) {
            logger.error('[MAILING] - Error finding posts', error);
            throw error;
        }
    };

    prepareAiNotificationEmail = async (emailType: string, { restaurantId }: EmailData): Promise<MailOptions | null> => {
        assert(restaurantId, '[prepareAiNotificationEmail] Missing restaurantId');
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            projection: { name: 1 },
            options: { lean: true },
        });

        const users = await this._userRestaurantsRepository.getRestaurantUsers(restaurant._id.toString());

        switch (emailType) {
            case EmailType.AI_API_SOFT_LIMIT_REACHED:
                return this._aiEmailUseCases.getAiSoftLimitMailOptions(users, restaurant);
            case EmailType.AI_API_HARD_LIMIT_REACHED:
                return this._aiEmailUseCases.getAiHardLimitMailOptions(users, restaurant);
            default:
                return null;
        }
    };

    prepareMobileAppNotificationEmail(emailType: EmailType | ReportType, { user }: EmailData): Promise<MailOptions> | null {
        switch (emailType) {
            case EmailType.DOWNLOAD_MOBILE_APP:
                return this._mobileAppEmailUseCases.getDownloadMobileAppMailOptions(user as IUser);
            default:
                return null;
        }
    }

    prepareWheelOfFortuneNotificationEmail(
        emailType: EmailType | ReportType,
        { draw, wheelOfFortuneGoingLiveData, emptyStockData, user, restaurants }: EmailData
    ): Promise<MailOptions | null> | null {
        switch (emailType) {
            case EmailType.GIFT_EXPIRES_SOON:
                assert(draw, '[prepareWheelOfFortuneNotificationEmail] Missing draw');
                return this._wheelsOfFortuneEmailUseCases.getGiftExpiresSoonEmail({ draw });
            case EmailType.RETRIEVE_GIFT:
                assert(draw, '[prepareWheelOfFortuneNotificationEmail] Missing draw');
                return this._wheelsOfFortuneEmailUseCases.getRetrieveGiftEmail({ draw });
            case EmailType.WOF_LIVE_TOMORROW:
                assert(wheelOfFortuneGoingLiveData, '[prepareWheelOfFortuneNotificationEmail] Missing wheelOfFortuneGoingLiveData');
                return this._wheelsOfFortuneEmailUseCases.getWofGoingLiveTomorrowEmail(wheelOfFortuneGoingLiveData);
            case EmailType.EMPTY_STOCK:
                assert(emptyStockData, '[prepareWheelOfFortuneNotificationEmail] Missing emptyStockData');
                return this._wheelsOfFortuneEmailUseCases.getEmptyStockEmail(emptyStockData);
            case EmailType.BOOSTER_PACK_SUBSCRIPTION_REQUEST:
                assert(user, '[prepareWheelOfFortuneNotificationEmail] Missing user');
                return this._wheelsOfFortuneEmailUseCases.getBoosterPackSubscriptionRequestEmail(user, restaurants ?? []);
            default:
                return null;
        }
    }
}
