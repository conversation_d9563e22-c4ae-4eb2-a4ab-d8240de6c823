import { singleton } from 'tsyringe';

import { DateFilter, isBetween, PostSource, RecordOfNumbersFromKeys, RoiSavedTimeDuplication, TemplateType } from '@malou-io/package-utils';

import ClientsRepository from ':modules/clients/clients.repository';
import { MediasRepository } from ':modules/media/medias.repository';
import PostsRepository from ':modules/posts/posts.repository';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import TemplatesRepository from ':modules/templates/templates.repository';

@singleton()
export class GetDuplicatedObjectsActions {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _templatesRepository: TemplatesRepository,
        private readonly _clientsRepository: ClientsRepository
    ) {}

    async execute(restaurantId: string, dateFilter: DateFilter): Promise<RecordOfNumbersFromKeys<RoiSavedTimeDuplication>> {
        const duplicateFilter = { restaurantId, updatedAt: dateFilter, duplicatedFromRestaurantId: { $ne: null } };

        // Descriptions
        const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);
        const duplicatedDescriptions = restaurant?.descriptions.find(
            (description) => !!description.duplicatedFromRestaurantId && isBetween(description.updatedAt, dateFilter.$gte, dateFilter.$lte)
        )
            ? 1
            : 0;

        // Attributes
        const duplicatedRestaurantAttributesInDB = await this._restaurantAttributesRepository.find({
            filter: duplicateFilter,
        });
        const duplicatedAttributes = duplicatedRestaurantAttributesInDB?.length;

        // Keywords
        const duplicatedKeywordsInDB = await this._restaurantKeywordsRepository.find({
            filter: duplicateFilter,
            options: { lean: true },
        });
        const duplicatedKeywords = duplicatedKeywordsInDB?.length;
        // Posts
        const duplicatedPostsInDB = await this._postsRepository.find({
            filter: { restaurantId, createdAt: dateFilter, duplicatedFromRestaurantId: { $ne: null } },
        });
        const duplicatedSeoPosts = duplicatedPostsInDB.filter((post) => post.source === PostSource.SEO)?.length;
        const duplicatedSocialPosts = duplicatedPostsInDB.filter((post) => post.source === PostSource.SOCIAL && !post.isStory)?.length;
        const duplicatedStories = duplicatedPostsInDB.filter((post) => post.isStory && post.source === PostSource.SOCIAL)?.length;

        // Media
        const duplicatedMediaInDB = await this._mediasRepository.find({
            filter: duplicateFilter,
        });
        const duplicatedMedia = duplicatedMediaInDB?.length;

        // Message template & message template
        const duplicatedTemplatesInDB = await this._templatesRepository.find({
            filter: duplicateFilter,
        });
        const duplicatedReviewTemplates = duplicatedTemplatesInDB.filter((template) => template.type === TemplateType.REVIEW).length;
        const duplicatedMessageTemplates = duplicatedTemplatesInDB.filter((template) => template.type === TemplateType.MESSAGE).length;

        // Clients
        const duplicatedClientsInDB = await this._clientsRepository.find({
            filter: duplicateFilter,
        });
        const duplicatedClients = duplicatedClientsInDB?.length;

        return {
            duplicatedDescriptions,
            duplicatedAttributes,
            duplicatedKeywords,
            duplicatedSeoPosts,
            duplicatedSocialPosts,
            duplicatedStories,
            duplicatedMedia,
            duplicatedMessageTemplates,
            duplicatedReviewTemplates,
            duplicatedClients,
        };
    }
}
