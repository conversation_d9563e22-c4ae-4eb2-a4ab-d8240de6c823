import { inject, singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { FetchTheForkClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/providers/fetch-thefork-client.use-case';

export interface IFetchProviderClientUseCase {
    execute: (providerClientId: string, restaurantId: string) => Promise<ProviderClient>;
}

@singleton()
export class FetchProviderClientUseCase {
    constructor(@inject(FetchTheForkClientUseCase) private readonly _fetchTheForkClientUseCase: IFetchProviderClientUseCase) {}

    async execute(providerClientId: string, restaurantId: string, platformKey: PlatformKey): Promise<ProviderClient> {
        let fetchProviderClientUseCase: IFetchProviderClientUseCase;
        switch (platformKey) {
            case PlatformKey.LAFOURCHETTE:
                fetchProviderClientUseCase = this._fetchTheForkClientUseCase;
                break;
            default:
                throw new MalouError(MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY, {
                    metadata: {
                        platformKey,
                    },
                });
        }
        return fetchProviderClientUseCase.execute(providerClientId, restaurantId);
    }
}
