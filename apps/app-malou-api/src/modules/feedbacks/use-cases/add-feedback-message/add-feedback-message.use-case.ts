import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { FeedbacksDto } from '@malou-io/package-dto';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { LightUser } from ':helpers/utils.types';
import { FeedbackAuthor } from ':modules/feedbacks/entities/feedback-author.entity';
import { FeedbackMessage, FeedbackMessageProps } from ':modules/feedbacks/entities/feedback-message.entity';
import { FeedbackParticipant, FeedbackParticipantProps } from ':modules/feedbacks/entities/feedback-participant.entity';
import FeedbacksRepository from ':modules/feedbacks/feedback.repository';
import { FeedbackNotificationService } from ':modules/feedbacks/services/feedback-notification.service';
import { MediasRepository } from ':modules/media/medias.repository';
import { UsersRepository } from ':modules/users/users.repository';

@singleton()
export class AddFeedbackMessageUseCase {
    constructor(
        private readonly _feedbacksRepository: FeedbacksRepository,
        private readonly _feedbackNotificationService: FeedbackNotificationService,
        private readonly _usersRepository: UsersRepository,
        private readonly _mediasRepository: MediasRepository
    ) {}

    async execute(
        feedbackId: string,
        feedbackMessageWithoutId: Omit<FeedbackMessageProps, 'id' | 'createdAt' | 'updatedAt' | 'author'>,
        participants: FeedbackParticipantProps[],
        lightUser: LightUser
    ): Promise<FeedbacksDto> {
        const feedback = await this._feedbacksRepository.findById(feedbackId);
        if (!feedback) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Feedback not found',
                metadata: {
                    feedbackId,
                },
            });
        }

        const feedbackMessage = await this._getFeedbackMessage(feedbackMessageWithoutId, lightUser);
        const feedbackParticipants = participants.map((participant) => new FeedbackParticipant(participant));

        feedback.addMessage(feedbackMessage, feedbackParticipants);

        const updatedFeedback = await this._feedbacksRepository.updateFeedback(feedback);
        assert(updatedFeedback);
        const addedFeedbackMessage = updatedFeedback.feedbackMessages[updatedFeedback.feedbackMessages.length - 1];

        // Email notification needs to be sent after updating feedback
        // It is async and should not block the response
        this._feedbackNotificationService
            .handleEmailNotificationRequest({
                feedbackMessageType: feedbackMessage.type,
                feedbackId: updatedFeedback.id,
                feedbackMessageId: addedFeedbackMessage.id,
            })
            .catch((e) => logger.error('Error sending notification', e));

        return updatedFeedback.toDto();
    }

    private async _getFeedbackMessage(
        feedbackMessageWithoutId: Omit<FeedbackMessageProps, 'id' | 'createdAt' | 'updatedAt' | 'author'>,
        lightUser: LightUser
    ): Promise<FeedbackMessage> {
        const user = await this._usersRepository.findById(lightUser._id.toString());
        if (!user) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'User not found',
                metadata: {
                    userId: lightUser._id.toString(),
                },
            });
        }

        let profilePictureUrl: string | undefined = undefined;

        if (user.profilePicture) {
            const media = await this._mediasRepository.findById(user.profilePicture);
            if (media) {
                profilePictureUrl = media.storedObjects?.thumbnail256Outside.publicUrl;
            }
        }

        const author = new FeedbackAuthor({
            id: user.id,
            name: user.name ?? '',
            lastname: user.lastname ?? '',
            role: user.role,
            email: user.email,
            userId: user.id,
            profilePictureUrl,
        });

        const now = new Date();

        return new FeedbackMessage({
            ...feedbackMessageWithoutId,
            id: '',
            createdAt: now,
            updatedAt: now,
            publishedAt: now,
            lastUpdatedAt: now,
            author,
        });
    }
}
