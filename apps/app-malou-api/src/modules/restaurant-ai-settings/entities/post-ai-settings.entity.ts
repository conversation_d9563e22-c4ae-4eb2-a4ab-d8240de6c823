import { z } from 'zod';

import {
    AiPostGenerationEmojiStatus,
    AiPostSettingsLength,
    AiPostSettingsTone,
    EntityConstructor,
    FrenchTutoiementVouvoiement,
} from '@malou-io/package-utils';

enum FrenchTutoiementVouvoiementFromLambda {
    tutoiement = 'tutoiement',
    vouvoiement = 'vouvoiement',
    default = 'default',
}

export class PostAiSettings {
    denomination: FrenchTutoiementVouvoiement;
    tone: AiPostSettingsTone[];
    length: AiPostSettingsLength;
    emoji: AiPostGenerationEmojiStatus;
    prompt?: string | null;

    constructor(data: EntityConstructor<PostAiSettings>) {
        this.denomination = data.denomination;
        this.tone = data.tone;
        this.length = data.length;
        this.emoji = data.emoji;
        this.prompt = data.prompt;
    }

    toLambdaDto() {
        const length = {
            [AiPostSettingsLength.very_short]: '100',
            [AiPostSettingsLength.short]: '200',
            [AiPostSettingsLength.medium]: '350',
            [AiPostSettingsLength.long]: '600',
        }[this.length];
        const denomination = {
            [FrenchTutoiementVouvoiement.DOES_NOT_MATTER]: 'default',
            [FrenchTutoiementVouvoiement.FORMAL]: 'vouvoiement',
            [FrenchTutoiementVouvoiement.INFORMAL]: 'tutoiement',
        }[this.denomination];
        return {
            denomination,
            tone: this.tone,
            length,
            containsEmojis: this.emoji,
            openPrompt: this.prompt,
        };
    }

    toDocument() {
        return {
            denomination: this.denomination,
            tone: this.tone,
            length: this.length,
            emoji: this.emoji,
            prompt: this.prompt,
        };
    }

    static fromLambdaGenerationResponse(response: unknown) {
        const validator = z
            .object({
                denomination: z.nativeEnum(FrenchTutoiementVouvoiementFromLambda),
                tone: z.array(z.nativeEnum(AiPostSettingsTone)),
                length: z.nativeEnum(AiPostSettingsLength),
                containsEmojis: z.nativeEnum(AiPostGenerationEmojiStatus),
            })
            .transform((value) => {
                return {
                    denomination: {
                        [FrenchTutoiementVouvoiementFromLambda.tutoiement]: FrenchTutoiementVouvoiement.INFORMAL,
                        [FrenchTutoiementVouvoiementFromLambda.vouvoiement]: FrenchTutoiementVouvoiement.FORMAL,
                        [FrenchTutoiementVouvoiementFromLambda.default]: FrenchTutoiementVouvoiement.DOES_NOT_MATTER,
                    }[value.denomination],
                    tone: value.tone,
                    length: value.length,
                    containsEmojis: value.containsEmojis,
                };
            });
        const validated = validator.parse(response);
        return new PostAiSettings({
            denomination: validated.denomination,
            tone: validated.tone,
            length: validated.length,
            emoji: validated.containsEmojis,
            prompt: '',
        });
    }
}
