import { Router } from 'express';
import multer from 'multer';
import { singleton } from 'tsyringe';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { casl } from ':helpers/casl/middlewares';
import { authorize } from ':plugins/passport';

import { MediasController } from './medias.controller';

@singleton()
export default class MediaRouter extends AbstractRouter {
    constructor(private _mediasController: MediasController) {
        super();
    }

    init(): Router {
        // Uploads and creates medias, used on cropping, uploading platform logos...
        // Used by the mobile app.
        this.router.post('/', authorize(), casl(), (req, res, next) => this._mediasController.handleUploadAndCreateMedia(req, res, next));

        // Create media after a direct upload to AWS S3, mainly used by the web app.
        // This endpoint is not used by the mobile app. This endpoint is also deprecated:
        // we are replacing it with POST /api/v1/media/upload-v2.
        this.router.post('/create', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleCreateMediaAfterUpload(req, res, next)
        );

        this.router.post('/cloud-storage-upload-params', authorize(), (req, res, next) =>
            this._mediasController.getCloudStorageUploadParams(req, res, next)
        );

        this.router.put('/move', authorize(), (req, res, next) => this._mediasController.handleMoveMediaTowardsFolder(req, res, next));

        this.router.post('/get-video-information', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleGetVideoInformation(req, res, next)
        );
        this.router.post('/transcode-heif-to-jpg', authorize(), multer().single('image'), (req, res, next) =>
            this._mediasController.handleTranscodeHeifToJpg(req, res, next)
        );

        // Only return aws url for media (especially used for conversation messages)
        this.router.post('/upload_only', authorize(), casl(), (req, res, next) => this._mediasController.handleUploadOnly(req, res, next));

        this.router.get('/', authorize(), casl(), (req, res, next) => this._mediasController.handleGetMediasByIds(req, res, next));

        this.router.get('/fetch-description', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleFetchMediaDescription(req, res, next)
        );

        this.router.get('/:medium_id', authorize(), (req, res, next) => this._mediasController.handleGetMediaById(req, res, next));

        this.router.put('/:medium_id', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleUpdateMediaById(req, res, next)
        );

        this.router.post('/:medium_id/replace', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleReplaceMediaUrlsById(req, res, next)
        );

        this.router.get('/restaurants/:restaurant_id', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleGetRestaurantMedias(req, res, next)
        );

        this.router.delete('/restaurants/:restaurant_id', authorize(), (req, res, next) =>
            this._mediasController.handleDeleteManyMedias(req, res, next)
        );

        this.router.post('/restaurants/:restaurant_id/duplicate', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleDuplicateRestaurantMedias(req, res, next)
        );

        this.router.post('/:mediaId/duplicate-for-publication', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleDuplicateMediaForPublication(req, res, next)
        );

        this.router.post('/thumbnails', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleCreateVideoThumbnail(req, res, next)
        );

        this.router.post('/thumbnails/list', authorize(), casl(), (req, res, next) =>
            this._mediasController.handleGetMediaThumbnailsList(req, res, next)
        );

        this.router.post('/upload-v2', authorize(), casl(), (req, res, next) => this._mediasController.uploadMediaV2(req, res, next));

        this.router.post('/get-media-for-edition/:mediaId', authorize(), casl(), (req, res, next) =>
            this._mediasController.getMediaForEdition(req, res, next)
        );

        this.router.post('/:mediaId/update-transform-data', authorize(), (req, res, next) =>
            this._mediasController.handleUpdateTransformData(req, res, next)
        );

        return this.router;
    }
}
