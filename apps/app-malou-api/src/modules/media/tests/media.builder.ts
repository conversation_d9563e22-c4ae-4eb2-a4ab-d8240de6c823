import { Builder } from 'builder-pattern';

import { IMedia, newDbId } from '@malou-io/package-models';
import { FileFormat, MediaCategory, MediaType } from '@malou-io/package-utils';

type MediaPayload = IMedia;

const _buildMedia = (media: MediaPayload) => Builder<MediaPayload>(media);

export const getDefaultMedia = () =>
    _buildMedia({
        _id: newDbId(),
        name: 'test_media.png',
        title: 'Test Media title',
        category: MediaCategory.ADDITIONAL,
        restaurantId: newDbId(),
        format: FileFormat.PNG,
        type: MediaType.PHOTO,
        urls: {
            original: 'www.urls.com/original.png',
            igFit: 'www.urls.com/igFile.png',
            small: 'www.urls.com/small.png',
        },
        sizes: {
            original: 100000,
        },
        dimensions: {
            original: {
                width: 1080,
                height: 1920,
            },
        },
        storedObjects: {
            original: { key: 'keyOriginal', publicUrl: '-', provider: 'S3' },
            normalized: { key: 'keyNormalized', publicUrl: '-', provider: 'S3' },
            thumbnail1024Outside: { key: 'keyThumbnail1024Outside', publicUrl: '-', provider: 'S3' },
            thumbnail256Outside: { key: 'keyThumbnail256Outside', publicUrl: '-', provider: 'S3' },
        },
        socialId: '3248982493187',
        convertedStatus: null,
        folderId: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
    });
