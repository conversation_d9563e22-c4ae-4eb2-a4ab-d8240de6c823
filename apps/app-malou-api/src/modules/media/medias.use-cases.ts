import 'reflect-metadata';

import fs from 'fs';
import { UpdateQuery, UpdateWriteOpResult } from 'mongoose';
import fetch from 'node-fetch';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { ID, IMedia, toDbId } from '@malou-io/package-models';
import { MalouErrorCode, PictureSizeRecord } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import SharpProvider from ':helpers/providers/sharp.provider';
import PostsRepository from ':modules/posts/posts.repository';
import { uploadMedia } from ':plugins/cloud-storage/s3';

import { MediasRepository } from './medias.repository';
import { MediaUploaderService } from './services/media-uploader/media-uploader.service';

@singleton()
export class MediasUseCases {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _sharpProvider: SharpProvider,
        private readonly _mediaUploaderService: MediaUploaderService
    ) {}

    async updateMediaThumbnail(thumbnailUrl: string) {
        const restaurantId = thumbnailUrl.split('/')[1];
        const pipeline = [
            { $match: { restaurantId: toDbId(restaurantId) } },
            { $match: { 'urls.original': { $regex: thumbnailUrl.split('.')[0] } } },
            { $project: { _id: 1 } },
        ];
        const mediaIdToUpdate = await this._mediasRepository.aggregate<{ _id: string }>(pipeline);

        if (!mediaIdToUpdate.length) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                metadata: {
                    restaurantId,
                    thumbnailUrl,
                },
            });
        }

        const result = await this._mediasRepository.findOneAndUpdate({
            filter: { _id: mediaIdToUpdate[0]._id },
            update: { thumbnail: `https://${Config.services.s3.bucketName}.s3.eu-west-3.amazonaws.com/${thumbnailUrl}` },
        });
        return result;
    }

    async createResizesFromAwsOriginalUrl(awsOriginalUrl: string, sizes: ('small' | 'igFit')[] = ['small', 'igFit']) {
        const res = await fetch(awsOriginalUrl);
        const buffer = await res.buffer();

        const match = awsOriginalUrl.match(/restaurants\/([\w\-]+)\/media\/([\w\-]+)\/(.+)$/);
        assert(match);
        const [, restaurantId, mediaUuid, file] = match;
        const pathName = `./downloadedMedias/${mediaUuid}-${file}`;
        const image = await this._sharpProvider.sharp(buffer).toFile(pathName);
        const [, extension] = file.split('.');

        const args = this._mediaUploaderService.getResizeDimensionsForFile(pathName);
        const urls: PictureSizeRecord<string> = {
            original: awsOriginalUrl,
        };
        const mediaSizes: PictureSizeRecord<number> = {
            original: image.size,
        };

        for (const size of sizes) {
            const { mediaUrl, mediaSize } = await this._getMediaUrlAndSize({
                file,
                buffer,
                args: args[size],
                restaurantId,
                mediaUuid,
                extension,
                mediaName: size,
            });
            urls[size] = mediaUrl;
            mediaSizes[size] = mediaSize;
        }

        fs.promises
            .unlink(pathName)
            .then(() => {
                logger.info('[UNLINK] File deleted', { path: pathName });
            })
            .catch((e) => {
                logger.error('[UNLINK] Error when unlinking file', { path: pathName, e });
            });

        return {
            urls,
            sizes: mediaSizes,
        };
    }

    async updateMediaPostIds(postId: ID, update: UpdateQuery<IMedia> | Partial<IMedia>): Promise<UpdateWriteOpResult> {
        logger.info('[UPDATE_MEDIA_POST_IDS]', { postId });
        const post = await this._postsRepository.findOne({
            filter: { _id: postId },
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });
        const postMediaIds = post?.attachments?.map((at) => at._id) ?? [];
        const parentMedias = await Promise.all(postMediaIds.map((mediaId) => this._getAllParentMedias(mediaId)));
        const parentMediaIds = parentMedias.reduce((acc, curr) => acc.concat(curr), []).map((m) => m._id);
        const mediasIds = postMediaIds.concat(parentMediaIds).filter((m) => !!m);
        return this._mediasRepository.updateMany({ filter: { _id: mediasIds }, update });
    }

    private async _getAllParentMedias(mediaId: ID): Promise<IMedia[]> {
        const MAX_LOOP_COUNT = 10;
        let media = await this._mediasRepository.findOne({
            filter: { _id: mediaId },
            projection: { originalMediaId: 1 },
            options: { lean: true },
        });
        if (!media || mediaId.toString() === media?.originalMediaId?.toString()) {
            return [];
        }
        const parentMedias: IMedia[] = [];
        let count = 0;
        do {
            const parentMedia = await this._mediasRepository.findOne({
                filter: {
                    _id: media.originalMediaId,
                },
                options: { lean: true },
            });
            if (parentMedia) {
                parentMedias.push(parentMedia);
                media = parentMedia;
            } else {
                return parentMedias;
            }
            count++;
        } while (media && count < MAX_LOOP_COUNT);

        return parentMedias;
    }

    private async _getMediaUrlAndSize({
        file,
        buffer,
        args,
        restaurantId,
        mediaUuid,
        extension,
        mediaName,
    }): Promise<{ mediaUrl: string; mediaSize: number }> {
        const pathName = `./downloadedMedias/${mediaUuid}-${mediaName}-${file}`;
        const image = await this._resizeImageToPath(buffer, pathName, args);
        const awsUrl = await uploadMedia({
            localImage: pathName,
            s3Path: `restaurants/${restaurantId}/media/${mediaUuid}`,
            mediaName,
            extension,
        });
        return { mediaUrl: awsUrl, mediaSize: image.size };
    }

    private async _resizeImageToPath(buffer: Buffer, pathName: string, args: number[]) {
        return this._sharpProvider
            .sharp(buffer)
            .resize(...args)
            .withMetadata()
            .toFile(pathName)
            .catch((e: any) => {
                logger.error('[RESIZE_ERROR] Error when resizing image with sharp', { path: pathName, args, e });
                return this._sharpProvider.sharp(buffer).toFile(pathName);
            });
    }
}
