import { inject, singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { CloudStorage } from ':plugins/cloud-storage/cloud-storage.interface';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class DeleteMediasUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        @inject(AwsS3) private readonly _cloudStorage: CloudStorage
    ) {}

    async execute(mediaIds: string[], restaurantId: string): Promise<void> {
        const medias = await this._mediasRepository.findMediasByIds(mediaIds);

        if (!this._checkIfMediasBelongToSameRestaurant(medias, restaurantId)) {
            throw new MalouError(MalouErrorCode.CANNOT_DELETE_MEDIAS_FROM_OTHER_RESTAURANTS, {
                message: 'Cannot delete medias from other restaurants',
            });
        }

        await Promise.all(medias.map((media) => this.deleteMedium(media)));
    }

    async deleteMedium(media: Media): Promise<void> {
        await this._mediasRepository.markMediumAsDeleted(media.id);
    }

    private _checkIfMediasBelongToSameRestaurant(medias: Media[], restaurantId: string): boolean {
        return medias.every((media) => media.restaurantId === restaurantId);
    }
}
