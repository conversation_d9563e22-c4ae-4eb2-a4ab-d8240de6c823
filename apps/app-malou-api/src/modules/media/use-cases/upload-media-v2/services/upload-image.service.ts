import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { PassThrough, Readable } from 'node:stream';
import { pipeline } from 'node:stream/promises';
import sharp, { OutputInfo } from 'sharp';
import { inject, singleton } from 'tsyringe';

import { IMedia, IMediaDimension, IMediaStoredObject, newDbId, toDbId } from '@malou-io/package-models';
import { AspectRatio, FileFormat, MediaCategory, MediaType, MimeType } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { StoredOjectProvider } from ':modules/media/enums/stored-object-provider.enum';
import { MediasRepository } from ':modules/media/medias.repository';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';
import { metricsService } from ':services/metrics.service';

export const imageCounter = metricsService.getMeter().createCounter<{
    status: 'success' | 'invalid_file';
}>('media.upload.image.count', {
    description: 'Number of images uploaded',
});

const MIME_TYPE_TO_EXTENSION: Record<MimeType.IMAGE_JPEG | MimeType.IMAGE_PNG | MimeType.IMAGE_HEIC, string> = {
    [MimeType.IMAGE_PNG]: 'png',
    [MimeType.IMAGE_JPEG]: 'jpeg',
    [MimeType.IMAGE_HEIC]: 'heic',
};

/**
 * Important: All streams duplicated by this function must be read together
 */
const duplicateReadable = (source: Readable): Readable => {
    return source.pipe(new PassThrough());
};

@singleton()
export class UploadImageService {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        @inject(AwsS3DistantStorageService) private readonly _distantStorageService: DistantStorageService
    ) {}

    async execute(params: {
        source: Readable;
        imageMimeType: MimeType.IMAGE_JPEG | MimeType.IMAGE_PNG | MimeType.IMAGE_HEIC;
        uuid: string;
        restaurantId: string;
        folderId?: string;
        userFileName: string;

        /**
         * Sets the field originalMediaId on the created media. This is useful if the media
         * is derived from another one and should not appear in the gallery (for instance if
         * the media is a video thumbnail).
         */
        originalMediaId?: string;
    }): Promise<Result<{ mediaId: string }, void>> {
        const { source, imageMimeType, uuid, restaurantId, userFileName } = params;
        logger.info('[UploadImageService] Start', { imageMimeType, uuid, restaurantId, userFileName });
        const storedObjectsRes = await this._processImage(source, imageMimeType, uuid);
        if (storedObjectsRes.isErr()) {
            imageCounter.add(1, { status: 'invalid_file' });
            return err(undefined);
        }

        const storedObjects = storedObjectsRes.value;
        logger.info('[UploadImageService] storedObjects', { storedObjects });

        const id = newDbId();
        const aspectRatio = storedObjects.normalized.dimension.width / storedObjects.normalized.dimension.height;
        // Default values, as they will be overridden later when added to the post (and will depend of the publication type)
        const transformData = {
            aspectRatio: AspectRatio.ORIGINAL,
            rotationInDegrees: 0,
            left: 0,
            top: 0,
            width: 1,
            height: 1,
        };
        const mediaInput: IMedia = {
            type: MediaType.PHOTO,
            format: FileFormat.JPEG,
            _id: id,
            category: MediaCategory.ADDITIONAL,
            socialId: id.toString(),
            createdAt: new Date(),
            updatedAt: new Date(),
            storedObjects: {
                original: storedObjects.original,
                normalized: storedObjects.normalized.storedObject,
                thumbnail1024Outside: storedObjects.thumbnail1024Outside.storedObject,
                thumbnail256Outside: storedObjects.thumbnail256Outside.storedObject,
            },
            dimensions: {
                original: storedObjects.normalized.dimension, // For retro-compatibility
                normalized: storedObjects.normalized.dimension,
                thumbnail1024Outside: storedObjects.thumbnail1024Outside.dimension,
                thumbnail256Outside: storedObjects.thumbnail256Outside.dimension,
            },
            restaurantId: toDbId(restaurantId),
            name: userFileName,
            aspectRatio,
            transformData,
            isV2: true,
            urls: {
                original: storedObjects.normalized.storedObject.publicUrl, // For retro-compatibility
            },
            sizes: {}, // For retro-compatibility
            originalMediaId: params.originalMediaId ? toDbId(params.originalMediaId) : undefined,
            folderId: params.folderId === undefined ? undefined : toDbId(params.folderId),
        };
        const media = await this._mediasRepository.create({ data: mediaInput, options: { lean: true } });
        imageCounter.add(1, { status: 'success' });

        return ok({ mediaId: media._id.toString() });
    }

    private async _processImage(
        source: Readable,
        originalMimeType: MimeType,
        uuid: string
    ): Promise<
        Result<
            {
                original: IMediaStoredObject;
                normalized: { storedObject: IMediaStoredObject; dimension: IMediaDimension };
                thumbnail256Outside: { storedObject: IMediaStoredObject; dimension: IMediaDimension };
                thumbnail1024Outside: { storedObject: IMediaStoredObject; dimension: IMediaDimension };
            },
            void
        >
    > {
        const thumbnailInfo = [
            { size: 4032, fileName: 'normalized.jpeg' }, // 4032 because it's the default 12 MegaPixels format 4/3
            { size: 1024, fileName: 'thumbnail1024Outside.jpeg' },
            { size: 256, fileName: 'thumbnail256Outside.jpeg' },
        ];
        const originalFileExtension = MIME_TYPE_TO_EXTENSION[originalMimeType];
        const originalFileName = originalFileExtension ? `original.${originalFileExtension}` : 'original';

        try {
            const promises = [
                this._uploadAndGetPublicAccessibleUrl({
                    readable: duplicateReadable(source),
                    uuid,
                    fileName: originalFileName,
                    mimeType: originalMimeType,
                }),
                ...thumbnailInfo.map(async (info) => {
                    const object = await this._generateAndUploadImageThumbnail(info.size, duplicateReadable(source), uuid, info.fileName);
                    return object;
                }),
            ] as const;

            // we have to consume the source stream otherwise everything is blocked (we duplicated it in the loop above)
            source.resume();

            const [original, normalized, thumbnail1024Outside, thumbnail256Outside] = await Promise.all(promises);
            const storedObjects = { original, normalized, thumbnail1024Outside, thumbnail256Outside };
            return ok(storedObjects);
        } catch (error) {
            // Not great but Sharp’s error reporting is quite questionnable.
            // We use the “info” level because that’s what happen when the user uploads
            // an invalid file.
            logger.info('[UploadImageService] sharp failed (invalid file?)', error);
            return err(undefined);
        }
    }

    private async _generateAndUploadImageThumbnail(
        size: number,
        source: Readable,
        uuid: string,
        fileName: string
    ): Promise<{ storedObject: IMediaStoredObject; dimension: IMediaDimension }> {
        let width: number | undefined, height: number | undefined;
        const normalizeTransformer = sharp()
            .rotate() // rotate the image depending on the Exif data
            .withExif({}) // removes the exif data
            .resize({ width: size, height: size, fit: 'outside', withoutEnlargement: true }) //  https://sharp.pixelplumbing.com/api-resize
            .on('info', (info: OutputInfo) => {
                width = info.width;
                height = info.height;
            })
            .jpeg({ progressive: true });

        const [storedObject] = await Promise.all([
            this._uploadAndGetPublicAccessibleUrl({
                readable: normalizeTransformer,
                uuid,
                fileName,
                mimeType: MimeType.IMAGE_JPEG,
            }),
            pipeline(source, normalizeTransformer),
        ]);
        assert(width);
        assert(height);
        return { storedObject, dimension: { width, height } };
    }

    private async _uploadAndGetPublicAccessibleUrl(params: {
        readable: Readable;
        uuid: string;
        fileName: string;
        mimeType: MimeType;
    }): Promise<IMediaStoredObject> {
        const key = `media-upload/${params.uuid}/${params.fileName}`;
        const contentType = params.mimeType;
        await this._distantStorageService.saveFromReadable(key, params.readable, { contentType });
        const publicUrl = await this._distantStorageService.getPublicAccessibleUrl(key);
        return { publicUrl, key, provider: StoredOjectProvider.S3 };
    }
}
