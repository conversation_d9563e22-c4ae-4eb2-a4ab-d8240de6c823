import { singleton } from 'tsyringe';
import { DeepPartial } from 'utility-types';

import { MediaDto } from '@malou-io/package-dto';

import { MediaProps } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';

interface UpdateMediaUseCaseParams {
    mediaId: string;
    data: DeepPartial<MediaProps>;
}

@singleton()
export class UpdateMediaUseCase {
    constructor(private readonly _mediasRepository: MediasRepository) {}
    /** Returns `null` if no media has the ID `params.mediaId` */
    async execute(params: UpdateMediaUseCaseParams): Promise<MediaDto | null> {
        const media = await this._mediasRepository.updateMedia(params.mediaId, params.data);
        if (media === null) {
            return null;
        }
        return media.toDto();
    }
}
