import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { MediaConvertedStatus, PostPublicationStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import PostsRepository from ':modules/posts/posts.repository';
import PostsUseCases from ':modules/posts/posts.use-cases';

@singleton()
export class ConvertMediaUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _postsRepository: PostsRepository
    ) {}

    async execute(mediaId: string, detail: any, metadata: any): Promise<void> {
        await this._mediasRepository.findOneAndUpdate({
            filter: { _id: mediaId },
            update: { convertedStatus: detail.status.toLowerCase() },
        });
        if (metadata?.postIdToRetry && metadata?.platformKey) {
            if (detail.status.toLowerCase() === MediaConvertedStatus.COMPLETE) {
                await this._retryPublishSingleMediaPost(mediaId, metadata);
            } else {
                await this._setPostOnError(metadata.postIdToRetry, detail.errorMessage);
            }
        }
    }

    private async _retryPublishSingleMediaPost(mediaId: string, metadata: any): Promise<void> {
        logger.info('Retry publish single media post', { mediaId, metadata });
        const { postIdToRetry, platformKey } = metadata;
        const media = await this._mediasRepository.findOne({ filter: { _id: mediaId }, options: { lean: true } });
        if (!media) {
            logger.warn('Media not found');
            return;
        }
        const postUseCase = this._postsUseCases.getPlatformPostUseCases(platformKey);
        const post = await this._postsRepository.findOne({
            filter: { _id: postIdToRetry },
            options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }], lean: true },
        });
        assert(post);
        assert.equal(post.attachments.length, 1, 'the post must have exactly one attached media');
        const previousMediaId = post.attachments[0]._id;
        if (!post) {
            logger.warn('Post not found');
            return;
        }
        post.attachments[0] = media;
        await this._postsRepository.updateOne({ filter: { _id: postIdToRetry }, update: { attachments: [toDbId(mediaId)] } });
        await postUseCase.publish({ post });
        if (previousMediaId) {
            await this._mediasRepository.markMediumAsDeleted(previousMediaId.toString());
        }
    }

    private async _setPostOnError(postId: string, errorData: string): Promise<void> {
        const post = await this._postsRepository.findOne({ filter: { _id: postId }, options: { lean: true } });
        if (!post) {
            logger.warn('Post not found');
            return;
        }
        await this._postsRepository.updateOne({
            filter: { _id: postId },
            update: {
                published: PostPublicationStatus.ERROR,
                errorData,
                errorStage: 'publish ig post - media convert',
                isPublishing: false,
            },
        });
    }
}
