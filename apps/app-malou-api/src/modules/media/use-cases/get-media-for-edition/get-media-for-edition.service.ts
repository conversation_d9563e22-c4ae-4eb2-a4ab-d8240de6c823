import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { MalouErrorCode, MediaType, PublicationType, TransformDataComputerService } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { MediasRepository } from ':modules/media/medias.repository';
import { GetMediaInfoService } from ':modules/media/services/get-media-thumbnail/get-media-thumbnail.service';

@singleton()
export class GetMediaForEditionService {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _getMediaInfoService: GetMediaInfoService
    ) {}

    async getMedia(mediaId: string, publicationType: PublicationType): Promise<GetMediaForEditionResponseDto> {
        const media = await this._mediasRepository.findOneOrFail({ filter: { _id: toDbId(mediaId) }, options: { lean: true } });
        let transformData = media.transformData;
        let aspectRatio = media.aspectRatio;
        if (!transformData || !aspectRatio) {
            const width = media.dimensions?.original?.width;
            const height = media.dimensions?.original?.height;
            if (!width || !height) {
                throw new MalouError(MalouErrorCode.CANNOT_COMPUTE_TRANSFORM_DATA_FROM_OLD_MEDIA, {
                    message: 'width or height are falsy',
                    metadata: { medId: media._id.toString(), width, height },
                });
            }
            if (!aspectRatio) {
                aspectRatio = width / height;
            }
            if (!transformData) {
                const transformArea = TransformDataComputerService.computeDefaultAreaFor(publicationType, aspectRatio);
                transformData = {
                    aspectRatio: TransformDataComputerService.computePreferredAspectRatioFor(publicationType, aspectRatio),
                    rotationInDegrees: 0,
                    ...transformArea,
                };
            }
        }

        const thumbnail1024Outside = await this._getMediaInfoService.getThumbnail1024OutsideUrlAndDimensions(media);
        const thumbnail256Outside = await this._getMediaInfoService.getThumbnail256OutsideUrlAndDimensions(media);

        if (!thumbnail1024Outside || !thumbnail256Outside) {
            throw new MalouError(MalouErrorCode.CANNOT_GET_THUMBNAIL_FOR_EDITION_MEDIA, {
                metadata: { mediaId: media._id.toString() },
            });
        }
        assert(media.type !== MediaType.FILE);
        const base = {
            id: mediaId,
            thumbnail1024OutsideUrl: thumbnail1024Outside.url,
            thumbnail1024OutsideDimensions: thumbnail1024Outside.dimensions,
            thumbnail256OutsideUrl: thumbnail256Outside.url,
            thumbnail256OutsideDimensions: thumbnail256Outside.dimensions,
            aspectRatio,
            transformData,
            aiDescription: media.aiDescription,
            name: media.name,
        };
        if (media.type === MediaType.PHOTO) {
            return {
                ...base,
                type: MediaType.PHOTO,
            };
        }
        assert(media.type === MediaType.VIDEO, 'media types other than PHOTO or VIDEO are not supported');
        const urlAndDimensions = this._getMediaInfoService.getUrlAndDimensions(media);
        assert(urlAndDimensions);
        return {
            ...base,
            type: MediaType.VIDEO,
            videoUrl: urlAndDimensions.url,
            videoDimensions: urlAndDimensions.dimensions,
            duration: media.duration ?? undefined,
            timelinePreviewFrames256hUrls: media.timelinePreviewFrames256h?.map((frame) => frame.publicUrl) ?? undefined,
        };
    }
}
