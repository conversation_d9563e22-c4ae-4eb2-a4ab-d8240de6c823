import { singleton } from 'tsyringe';

import { MediaFilters } from ':helpers/filters/media-filters';
import { Pagination } from ':helpers/pagination';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';

type GetRestaurantMediasPaginatedUseCaseCommand = {
    pagination: Pagination;
    filters: MediaFilters;
};

type GetRestaurantMediasPaginatedUseCaseResult = {
    medias: Media[];
    pagination: Pagination;
};

@singleton()
export class GetRestaurantMediasPaginatedUseCase {
    constructor(private _mediasRepository: MediasRepository) {}

    async execute({ pagination, filters }: GetRestaurantMediasPaginatedUseCaseCommand): Promise<GetRestaurantMediasPaginatedUseCaseResult> {
        const result = await this._mediasRepository.getRestaurantMediasPaginated(pagination, filters);

        pagination.total = result.totalCount[0]?.total ?? 0;
        return { medias: result.data, pagination };
    }
}
