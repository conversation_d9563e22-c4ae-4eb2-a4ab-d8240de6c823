import { singleton } from 'tsyringe';

import { CreateMediaAfterUploadBodyDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { MediaCategory } from '@malou-io/package-utils';

import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { MediaTitleGeneratorService } from ':modules/media/services/media-title-generator/media-title-generator.service';

@singleton()
export class CreateMediaAfterUploadUseCase {
    constructor(
        private readonly _mediaTitleGeneratorService: MediaTitleGeneratorService,
        private readonly _mediasRepository: MediasRepository
    ) {}

    async execute(medias: CreateMediaAfterUploadBodyDto['medias'], restaurantId: string) {
        const promises = medias.map(async (uploadedMedia) => {
            const title = await this._mediaTitleGeneratorService.generateTitleForMediaWithRestaurantKeywords(
                restaurantId,
                uploadedMedia.name
            );
            const id = newDbId().toString();
            const media = new Media({
                ...uploadedMedia,
                id,
                socialId: id,
                duration: uploadedMedia.duration,
                sizes: {
                    ...uploadedMedia.sizes,
                    id: newDbId().toString(),
                },
                createdAt: new Date(),
                updatedAt: new Date(),
                urls: {
                    ...uploadedMedia.urls,
                    id: newDbId().toString(),
                },
                restaurantId,
                title,
                category: MediaCategory.ADDITIONAL,
            });

            return this._mediasRepository.createMedia(media);
        });
        return Promise.all(promises);
    }
}
