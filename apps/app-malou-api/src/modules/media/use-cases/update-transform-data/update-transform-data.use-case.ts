import { singleton } from 'tsyringe';

import { UpdateTransformDataBodyDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';

import { MediasRepository } from ':modules/media/medias.repository';

@singleton()
export class UpdateTransformDataUseCase {
    constructor(private readonly _mediasRepository: MediasRepository) {}
    async execute(mediaId: string, body: UpdateTransformDataBodyDto): Promise<void> {
        await this._mediasRepository.updateOne({
            filter: { _id: toDbId(mediaId) },
            update: {
                transformData: {
                    aspectRatio: body.aspectRatio,
                    rotationInDegrees: body.rotationInDegrees,
                    left: body.left,
                    top: body.top,
                    width: body.width,
                    height: body.height,
                },
            },
        });
    }
}
