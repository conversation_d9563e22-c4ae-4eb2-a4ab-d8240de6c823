import { Request } from 'express';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { MediasRepository } from ':modules/media/medias.repository';
import { FileParserService } from ':modules/media/services/file-parser/file-parser.service';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';
import { CloudStorage } from ':plugins/cloud-storage/cloud-storage.interface';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class ReplaceMediaUrlsUseCase {
    constructor(
        private _fileParserService: FileParserService,
        private _mediaUploaderService: MediaUploaderService,
        private _mediasRepository: MediasRepository,
        @inject(AwsS3) private readonly _cloudStorage: CloudStorage
    ) {}

    async execute(req: Request, mediumId: string) {
        const bucketName = process.env.AWS_BUCKET;

        const [parsedFile] = await this._fileParserService.parseFiles(req, 'media');

        const media = await this._mediasRepository.findById(mediumId);
        assert(media);

        const uploadedMedium = await this._mediaUploaderService.uploadFromFile(parsedFile, media);

        const updatedMedia = await this._mediasRepository.updateMedia(media.id, uploadedMedium);

        await Promise.allSettled(
            media
                .getUrlsArray()
                .map((url) =>
                    this._cloudStorage.deleteObject(url.replace(`https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/`, ''))
                )
        );
        return updatedMedia;
    }
}
