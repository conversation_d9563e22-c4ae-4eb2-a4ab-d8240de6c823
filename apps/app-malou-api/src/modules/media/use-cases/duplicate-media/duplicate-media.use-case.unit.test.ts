import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MediasRepository } from ':modules/media/medias.repository';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { DuplicateMediaUseCase } from ':modules/media/use-cases/duplicate-media/duplicate-media.use-case';
import { DuplicateMediasUseCase } from ':modules/media/use-cases/duplicate-medias/duplicate-medias.use-case';

describe('DuplicateMediaUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['MediasRepository']);
    });

    it('should duplicate v2 media', async () => {
        const testCase = new TestCaseBuilderV2<'medias'>({
            seeds: {
                medias: {
                    data() {
                        return [getDefaultMedia().isV2(true).build()];
                    },
                },
            },
            expectedResult(dependencies): { restaurantId: string; duplicatedMediaId: string }[] {
                return [
                    {
                        restaurantId: dependencies.medias[0].restaurantId!.toString(),
                        duplicatedMediaId: expect.any(String),
                    },
                ];
            },
        });
        await testCase.build();
        const expectedResult = testCase.getExpectedResult();
        const userId = newDbId().toString();
        const media = testCase.getSeededObjects().medias[0];
        const mediaId = media._id.toString();
        const duplicateMediaUseCase = container.resolve(DuplicateMediaUseCase);
        const result = await duplicateMediaUseCase.execute(media._id.toString(), userId, [(media.restaurantId as DbId).toString()]);

        expect(result).toEqual(expectedResult);

        const mediasRepository = container.resolve(MediasRepository);
        const dbMedias = await mediasRepository.find({ filter: {}, options: { lean: true } });
        expect(dbMedias).toBeArrayOfSize(2);
        const originalMedia = dbMedias.find((e) => e._id.toString() === mediaId);
        const duplicatedMedia = dbMedias.find((e) => e._id.toString() === result[0].duplicatedMediaId);
        assert(originalMedia);
        assert(duplicatedMedia);
        expect(originalMedia._id.toString()).toBe(mediaId);
        expect(duplicatedMedia._id.toString()).toBe(result[0].duplicatedMediaId);
        expect(duplicatedMedia.originalMediaId!.toString()).toBe(mediaId);
        expect(duplicatedMedia.userId!.toString()).toBe(userId);
    });

    it('should duplicate v1 media', async () => {
        const testCase = new TestCaseBuilderV2<'medias'>({
            seeds: {
                medias: {
                    data() {
                        return [getDefaultMedia().isV2(false).build()];
                    },
                },
            },
            expectedResult(dependencies): { restaurantId: string; duplicatedMediaId: string }[] {
                return [
                    {
                        restaurantId: dependencies.medias[0].restaurantId!.toString(),
                        duplicatedMediaId: expect.any(String),
                    },
                ];
            },
        });
        await testCase.build();
        const expectedResult = testCase.getExpectedResult();
        const userId = newDbId().toString();
        const media = testCase.getSeededObjects().medias[0];

        const restaurantId = (media.restaurantId as DbId).toString();

        const DuplicateMediasUseCaseMock = {
            execute: () => {
                return [{ id: 'duplicatedMediaId', restaurantId }];
            },
        } as unknown as DuplicateMediasUseCase;

        container.registerInstance(DuplicateMediasUseCase, DuplicateMediasUseCaseMock);

        const duplicateMediaUseCase = container.resolve(DuplicateMediaUseCase);
        const result = await duplicateMediaUseCase.execute(media._id.toString(), userId, [restaurantId]);

        expect(result).toEqual(expectedResult);
    });
});
