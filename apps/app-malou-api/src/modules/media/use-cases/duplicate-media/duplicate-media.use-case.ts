import { omit } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { DuplicateRestaurantMediaBodyDto } from '@malou-io/package-dto';
import { IMediaTransformData, newDbId } from '@malou-io/package-models';
import { AspectRatio, MediaType, PublicationType, TransformDataComputerService } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { DuplicateMediasUseCase } from ':modules/media/use-cases/duplicate-medias/duplicate-medias.use-case';

/**
 * For media v2
 */
@singleton()
export class DuplicateMediaUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _duplicateMediasUseCase: DuplicateMediasUseCase,
        private readonly _agendaSingleton: AgendaSingleton
    ) {}

    /**
     * Duplicates a media to many restaurants
     *
     * @param restaurantIds IDs of the restaurants the media will be copied to.
     */
    async execute(
        mediaId: string,
        reqUserId: string,
        restaurantIds: string[],
        overrideTransformData?: 'default' | PublicationType
    ): Promise<{ restaurantId: string; duplicatedMediaId: string }[]> {
        const media = await this._mediasRepository.findById(mediaId);
        assert(media);

        const result = media.isV2
            ? await this._duplicateV2Media(media, reqUserId, restaurantIds, overrideTransformData)
            : await this._duplicateV1Media(media, reqUserId, restaurantIds);
        return result;
    }

    private async _duplicateV2Media(
        media: Media,
        reqUserId: string,
        restaurantIds: string[],
        overrideTransformData?: 'default' | PublicationType
    ): Promise<{ restaurantId: string; duplicatedMediaId: string }[]> {
        let transformData: IMediaTransformData | undefined;
        if (overrideTransformData === 'default') {
            transformData = {
                aspectRatio: AspectRatio.ORIGINAL,
                rotationInDegrees: 0,
                left: 0,
                top: 0,
                width: 1,
                height: 1,
            };
        } else if (overrideTransformData !== undefined) {
            assert(media.aspectRatio);
            const transformArea = TransformDataComputerService.computeDefaultAreaFor(overrideTransformData, media.aspectRatio);
            transformData = {
                aspectRatio: TransformDataComputerService.computePreferredAspectRatioFor(overrideTransformData, media.aspectRatio),
                rotationInDegrees: 0,
                ...transformArea,
            };
        }
        const newMedias = restaurantIds.map((restaurantId): Media => {
            const id = newDbId().toString();
            return new Media({
                ...omit(media, ['id', 'socialId']),
                id,
                socialId: id,
                originalMediaId: media.originalMediaId ?? media.id,
                userId: reqUserId,
                duplicatedFromRestaurantId: media.restaurantId,
                restaurantId,
                transformData: transformData ?? media.transformData,
            });
        });
        const newMediaWithIds = await this._mediasRepository.createMedias(newMedias);
        for (const newMedia of newMediaWithIds) {
            if (newMedia.type === MediaType.VIDEO && !newMedia.isVideoNormalized) {
                await this._agendaSingleton.now(AgendaJobName.GENERATE_NORMALIZED_VIDEO, { mediaId: newMedia.id });
            }
        }
        return newMediaWithIds.map((newMedia): { restaurantId: string; duplicatedMediaId: string } => {
            assert(newMedia.restaurantId);
            return { restaurantId: newMedia.restaurantId, duplicatedMediaId: newMedia.id };
        });
    }

    private async _duplicateV1Media(
        media: Media,
        reqUserId: string,
        restaurantIds: string[]
    ): Promise<{ restaurantId: string; duplicatedMediaId: string }[]> {
        const originalMediaDto: DuplicateRestaurantMediaBodyDto['originalMedia'][number] = {
            id: media.id,
            originalMediaId: media.originalMediaId,
            name: media.name,
            format: media.format,
            type: media.type,
            duration: media.duration,
            urls: {
                original: media.urls.original,
                small: media.urls.small ?? undefined,
                cover: media.urls.cover ?? undefined,
                smallCover: media.urls.smallCover ?? undefined,
                igFit: media.urls.igFit ?? undefined,
            },
            sizes: media.sizes,
            dimensions: media.dimensions,
            folderId: media.folderId,
            category: media.category,
            thumbnail: media.thumbnail,
            aiDescription: media.aiDescription ?? undefined,
        };

        assert(media.restaurantId, 'duplicating a media that’s not associated with a restaurant is not supported');
        const duplicatedMediasDto = await this._duplicateMediasUseCase.execute(
            [originalMediaDto],
            restaurantIds,
            reqUserId,
            media.restaurantId
        );

        return duplicatedMediasDto.map((duplicatedMedia) => {
            assert(duplicatedMedia.restaurantId);
            return {
                restaurantId: duplicatedMedia.restaurantId,
                duplicatedMediaId: duplicatedMedia.id,
            };
        });
    }
}
