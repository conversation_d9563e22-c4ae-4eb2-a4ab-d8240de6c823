import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { MediaDto } from '@malou-io/package-dto';

import { MediasRepository } from ':modules/media/medias.repository';

@singleton()
export class GetMediaByIdUseCase {
    constructor(private readonly _mediasRepository: MediasRepository) {}
    async execute(mediaId: string): Promise<MediaDto> {
        const media = await this._mediasRepository.findById(mediaId);
        assert(media);
        return media?.toDto();
    }
}
