import { container } from 'tsyringe';

import { DbId, newDbId, toDbId } from '@malou-io/package-models';
import { ApplicationLanguage, MediaTagCategory } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AiMediaTag } from ':microservices/ai-media-analysis.service';
import { GenerateMediaAnalysisService } from ':modules/ai/services/generate-media-analysis.service';
import { MediasRepository } from ':modules/media/medias.repository';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { FetchMediaDescriptionUseCase } from ':modules/media/use-cases/fetch-media-description/fetch-media-description.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

const defaultMediaAnalysisGeneration = {
    description: 'ai description',
    isInformative: true,
    tags: [{ tag: 'tag', language: ApplicationLanguage.EN, category: MediaTagCategory.FOOD }],
};

let fetchMediaDescriptionUseCase: FetchMediaDescriptionUseCase;
let mediasRepository: MediasRepository;

describe('FetchMediaDescriptionUseCase', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories(['RestaurantsRepository', 'MediasRepository']);

        class GenerateMediaAnalysisServiceMock {
            execute(): Promise<{ description: string; isInformative: boolean; tags: AiMediaTag[] }> {
                return Promise.resolve(defaultMediaAnalysisGeneration);
            }
        }
        container.register(GenerateMediaAnalysisService, { useValue: new GenerateMediaAnalysisServiceMock() as any });

        fetchMediaDescriptionUseCase = container.resolve(FetchMediaDescriptionUseCase);
        mediasRepository = container.resolve(MediasRepository);
    });

    describe('execute', () => {
        it('should update media description and tags', async () => {
            const userId = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'medias'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    medias: {
                        data(dependencies) {
                            return [
                                getDefaultMedia().socialId('media 1').restaurantId(dependencies.restaurants()[0]._id).build(),
                                getDefaultMedia().socialId('media 2').restaurantId(dependencies.restaurants()[0]._id).build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const mediaIds = seededObjects.medias.map((media) => (media._id as DbId).toString());

            await fetchMediaDescriptionUseCase.execute({
                mediaIds,
                userId,
            });

            const medias = await mediasRepository.find({ filter: { _id: { $in: mediaIds.map(toDbId) } }, options: { lean: true } });
            const firstMedia = medias[0];
            const secondMedia = medias[1];
            const defaultDescription = defaultMediaAnalysisGeneration.description;
            const defaultTags = defaultMediaAnalysisGeneration.tags;

            expect(medias).toHaveLength(2);
            expect(firstMedia.aiDescription).toEqual(defaultDescription);
            expect(secondMedia.aiDescription).toEqual(defaultDescription);

            expect(firstMedia.aiTags).toEqual(defaultTags);
            expect(secondMedia.aiTags).toEqual(defaultTags);
        });
    });
});
