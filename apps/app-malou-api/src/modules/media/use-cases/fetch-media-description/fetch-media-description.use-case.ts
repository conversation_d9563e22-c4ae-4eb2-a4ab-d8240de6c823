import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { MediaType } from '@malou-io/package-utils';

import { GenerateMediaAnalysisService } from ':modules/ai/services/generate-media-analysis.service';
import { MediasRepository } from ':modules/media/medias.repository';

@singleton()
export class FetchMediaDescriptionUseCase {
    constructor(
        private _mediasRepository: MediasRepository,
        private _generateMediaAnalysisService: GenerateMediaAnalysisService
    ) {}

    async execute({ mediaIds, userId }: { mediaIds: string[]; userId: string }): Promise<void> {
        const mediaWithoutDescriptionOrTags = await this._mediasRepository.find({
            filter: {
                _id: {
                    $in: mediaIds.map((id) => toDbId(id)),
                },
                $or: [{ aiDescription: null }, { aiTags: null }],
                type: MediaType.PHOTO,
            },
            projection: { _id: 1, urls: 1, restaurantId: 1, originalMediaId: 1 },
            options: { lean: true },
        });
        // We use "for loop" instead of "Promise.all" because we don't want to make all lambda calls at once & we need to update at least one description asap
        for (const media of mediaWithoutDescriptionOrTags) {
            if (!media.urls?.igFit && !media.urls?.original) {
                continue;
            }
            const mediaAnalysis = await this._generateMediaAnalysisService.execute({
                relatedEntityId: media._id?.toString(),
                mediaUrl: media.urls?.igFit || media.urls?.original,
                restaurantId: media.restaurantId?.toString(),
                userId,
            });
            await this._mediasRepository.findOneAndUpdate({
                filter: { _id: media._id },
                update: {
                    aiDescription: mediaAnalysis.description,
                    hasDisplayedText: mediaAnalysis.isInformative,
                    aiTags: mediaAnalysis.tags,
                },
            });
            // Update the original media if it exists and doesn't have a description
            let mediaOriginal = media.originalMediaId ? await this._mediasRepository.findById(media.originalMediaId.toString()) : null;
            while (mediaOriginal && !mediaOriginal.aiDescription) {
                await this._mediasRepository.updateOne({
                    filter: { _id: toDbId(mediaOriginal.id) },
                    update: {
                        aiDescription: mediaAnalysis.description,
                        hasDisplayedText: mediaAnalysis.isInformative,
                        aiTags: mediaAnalysis.tags,
                    },
                });
                mediaOriginal = mediaOriginal.originalMediaId
                    ? await this._mediasRepository.findById(mediaOriginal.originalMediaId.toString())
                    : null;
            }
        }
        return;
    }
}
