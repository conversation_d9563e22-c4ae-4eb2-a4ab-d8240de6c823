import { singleton } from 'tsyringe';

import { MediaDto } from '@malou-io/package-dto';

import { MediasRepository } from ':modules/media/medias.repository';

@singleton()
export class GetMediaByIdsUseCase {
    constructor(private readonly _mediasRepository: MediasRepository) {}

    async execute(ids: string[]): Promise<MediaDto[]> {
        const medias = await this._mediasRepository.findMediasByIds(ids);
        return medias.map((media) => media.toDto());
    }
}
