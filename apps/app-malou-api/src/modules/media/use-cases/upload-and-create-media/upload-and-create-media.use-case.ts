import { Request } from 'express';
import { singleton } from 'tsyringe';

import { MediaDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { getFileExtension, getFileFormatFromExtension, MalouErrorCode, MediaCategory, MediaType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Media, MediaProps } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { ParsedFile } from ':modules/media/services/file-parser/file-parser.interface';
import { FileParserService } from ':modules/media/services/file-parser/file-parser.service';
import { MediaTitleGeneratorService } from ':modules/media/services/media-title-generator/media-title-generator.service';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';

interface UploadAndCreateMediaUseCaseParams {
    req: Request;
    restaurantId?: string;
}

@singleton()
export class UploadAndCreateMediaUseCase {
    constructor(
        private readonly _fileParserService: FileParserService,
        private readonly _mediaTitleGeneratorService: MediaTitleGeneratorService,
        private readonly _mediaUploaderService: MediaUploaderService,
        private readonly _mediasRepository: MediasRepository
    ) {}

    async execute({ req, restaurantId }: UploadAndCreateMediaUseCaseParams): Promise<MediaDto[]> {
        const parsedFiles = await this._fileParserService.parseFiles(req, 'media');

        this._assertFilesType(parsedFiles);

        const promises = parsedFiles.map(async (file) => {
            const title = await this._mediaTitleGeneratorService.generateTitleForMediaWithRestaurantKeywords(restaurantId, file.name);
            const originalMediaId = file.originalMediaId ? await this._mediasRepository.findById(file.originalMediaId) : undefined;
            const id = newDbId().toString();
            const media: MediaProps = {
                id,
                socialId: id,
                restaurantId,
                userId: file.userId,
                title: title,
                category: file.category ?? MediaCategory.ADDITIONAL,
                postIds: [],
                urls: {
                    original: file.pathWhereFileIsStored,
                },
                dimensions: file.dimensions,
                sizes: {},
                format: getFileFormatFromExtension(getFileExtension(file.pathWhereFileIsStored)),
                originalMediaId: file.originalMediaId ?? undefined,
                folderId: null,
                type: file.type,
                resizeMetadata: file.resizeMetadata,
                aiDescription: originalMediaId?.aiDescription,
                aiTags: originalMediaId?.aiTags,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            const uploadedMedia = await this._mediaUploaderService.uploadFromFile(file, media, { keepOriginalAsIgFit: false });
            const newMedia = await this._mediasRepository.createMedia(
                new Media({
                    ...media,
                    ...uploadedMedia,
                })
            );
            return newMedia.toDto();
        });
        const newMedias = await Promise.all(promises);

        return newMedias;
    }

    private _assertFilesType(parsedFiles: ParsedFile[]) {
        const areFilesUnsupported = parsedFiles.some((file) => file.type === MediaType.FILE);
        if (areFilesUnsupported) {
            throw new MalouError(MalouErrorCode.WRONG_MEDIA_TYPE, {
                message: 'File type is not supported',
                metadata: { type: MediaType.FILE },
            });
        }
    }
}
