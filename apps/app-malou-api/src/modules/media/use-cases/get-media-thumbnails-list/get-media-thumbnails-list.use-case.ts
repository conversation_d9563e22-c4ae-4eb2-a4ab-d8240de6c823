import { inject, singleton } from 'tsyringe';

import { MalouErrorCode, MediaType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { MediasRepository } from ':modules/media/medias.repository';
import { CloudinaryVideoThumbnailGeneratorService } from ':modules/media/services/video-thumbnail-generator/adapters/cloudinary-video-thumbnail-generator.service';
import { VideoThumbnailGenerator } from ':modules/media/services/video-thumbnail-generator/video-thumbnail-generator.interface';

@singleton()
export class GetMediaThumbnailsListUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        @inject(CloudinaryVideoThumbnailGeneratorService)
        private readonly _videoThumbnailGeneratorService: VideoThumbnailGenerator
    ) {}

    async execute(mediaId: string, momentsInSeconds: number[]): Promise<string[]> {
        const media = await this._mediasRepository.findOne({ filter: { _id: mediaId }, options: { lean: true } });

        if (!media) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Media not found',
            });
        }

        if (media.type !== MediaType.VIDEO) {
            throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                message: 'Media is not a video',
            });
        }

        const thumbnails = await this._videoThumbnailGeneratorService.generateThumbnailList(media.urls.original, momentsInSeconds);

        return thumbnails;
    }
}
