import { singleton } from 'tsyringe';

import { MediasRepository } from ':modules/media/medias.repository';

@singleton()
export class MoveMediaToFolderUseCase {
    constructor(private readonly _mediasRepository: MediasRepository) {}

    /**
     * @param folderId - If null, the media will be moved to the root folder.
     */
    async execute(mediaIds: string[], folderId: string | null): Promise<void> {
        return this._mediasRepository.updateMediasFolder(mediaIds, folderId);
    }
}
