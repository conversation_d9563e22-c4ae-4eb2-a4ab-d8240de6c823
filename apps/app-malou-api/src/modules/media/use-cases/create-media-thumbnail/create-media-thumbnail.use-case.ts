import { inject, singleton } from 'tsyringe';

import { MediaDto } from '@malou-io/package-dto';
import { IMedia } from '@malou-io/package-models';
import { MalouErrorCode, MediaType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { CloudinaryVideoThumbnailGeneratorService } from ':modules/media/services/video-thumbnail-generator/adapters/cloudinary-video-thumbnail-generator.service';
import { VideoThumbnailGenerator } from ':modules/media/services/video-thumbnail-generator/video-thumbnail-generator.interface';

@singleton()
export class CreateMediaThumbnailUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        @inject(CloudinaryVideoThumbnailGeneratorService)
        private readonly _videoThumbnailGeneratorService: VideoThumbnailGenerator
    ) {}

    async execute(mediaId: string, momentInSeconds: number): Promise<MediaDto> {
        const media = await this._mediasRepository.findOne({ filter: { _id: mediaId }, options: { lean: true } });

        if (!media) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'Media not found',
            });
        }

        if (media.thumbnail) {
            return this._mapToDto(media);
        }

        if (media.type !== MediaType.VIDEO) {
            throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                message: 'Media is not a video',
            });
        }

        const thumbnail = await this._videoThumbnailGeneratorService.generateThumbnail(media.urls.original, momentInSeconds);

        const newMedia = await this._mediasRepository.findOneAndUpdateOrFail({
            filter: { _id: mediaId },
            update: { thumbnail },
            options: { lean: true, new: true },
        });

        return this._mapToDto(newMedia);
    }

    private _mapToDto(media: IMedia): MediaDto {
        return new Media({
            ...media,
            id: media._id.toString(),
            restaurantId: media.restaurantId?.toString(),
            userId: media.userId?.toString(),
            postIds: media.postIds?.map((postId) => postId.toString()),
            originalMediaId: media.originalMediaId?.toString(),
            folderId: media.folderId?.toString(),
            duplicatedFromRestaurantId: media.duplicatedFromRestaurantId?.toString(),
        }).toDto();
    }
}
