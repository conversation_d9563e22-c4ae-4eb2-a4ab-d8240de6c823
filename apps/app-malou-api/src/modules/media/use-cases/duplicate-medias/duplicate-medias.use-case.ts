import { omit } from 'lodash';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { DuplicateRestaurantMediaBodyDto, MediaDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { MalouErrorCode, MediaCategory, MediaType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { MediaTitleGeneratorService } from ':modules/media/services/media-title-generator/media-title-generator.service';
import { CloudStorage } from ':plugins/cloud-storage/cloud-storage.interface';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class DuplicateMediasUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _mediaTitleGeneratorService: MediaTitleGeneratorService,
        @inject(AwsS3) private readonly _cloudStorage: CloudStorage
    ) {}

    async execute(
        originalMedia: DuplicateRestaurantMediaBodyDto['originalMedia'],
        destinationRestaurantIds: string[],
        userId: string,
        sourceRestaurantId: string
    ): Promise<MediaDto[]> {
        const promises: Promise<MediaDto>[] = [];
        destinationRestaurantIds.forEach((restaurantId) => {
            originalMedia.forEach((media) => {
                if (media.type === MediaType.FILE) {
                    throw new MalouError(MalouErrorCode.WRONG_MEDIA_TYPE, {
                        metadata: {
                            restaurantId,
                            userId,
                            media,
                        },
                    });
                }
                const id = newDbId().toString();
                assert(media.dimensions); // this will not work on a few old medias
                const newMedia = new Media({
                    ...omit(media, 'id', 'socialId'),
                    id,
                    socialId: id,
                    restaurantId,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    folderId: sourceRestaurantId === restaurantId ? media.folderId || null : null,
                    userId: userId.toString(),
                    category: media.category ?? MediaCategory.ADDITIONAL,
                    type: media.type,
                    format: media.format,
                    originalMediaId: sourceRestaurantId === restaurantId ? (media.originalMediaId ?? media.id) : undefined, // Do not set originalMediaId if the media is not from the same restaurant
                    duplicatedFromRestaurantId: sourceRestaurantId === restaurantId ? undefined : sourceRestaurantId,
                    urls: {
                        original: media.urls.original,
                        small: media.urls.small,
                        cover: media.urls.cover,
                        smallCover: media.urls.smallCover,
                        igFit: media.urls.igFit,
                    },
                    sizes: {
                        original: media.sizes.original,
                        small: media.sizes.small,
                        cover: media.sizes.cover,
                        smallCover: media.sizes.smallCover,
                        igFit: media.sizes.igFit,
                    },
                    dimensions: {
                        original: media.dimensions.original
                            ? {
                                  width: media.dimensions.original.width,
                                  height: media.dimensions.original.height,
                              }
                            : undefined,
                        small: media.dimensions.small
                            ? {
                                  width: media.dimensions.small.width,
                                  height: media.dimensions.small.height,
                              }
                            : undefined,
                        cover: media.dimensions.cover
                            ? {
                                  width: media.dimensions.cover.width,
                                  height: media.dimensions.cover.height,
                              }
                            : undefined,
                        smallCover: media.dimensions.smallCover
                            ? {
                                  width: media.dimensions.smallCover.width,
                                  height: media.dimensions.smallCover.height,
                              }
                            : undefined,
                        igFit: media.dimensions.igFit
                            ? {
                                  width: media.dimensions.igFit.width,
                                  height: media.dimensions.igFit.height,
                              }
                            : undefined,
                    },
                });
                promises.push(this.duplicateRestaurantMedia(newMedia, restaurantId, userId.toString(), sourceRestaurantId));
            });
        });

        return Promise.all(promises);
    }

    async duplicateRestaurantMedia(
        media: Media,
        destinationRestaurantId: string,
        userId: string,
        sourceRestaurantId: string,
        folderId?: string
    ): Promise<MediaDto> {
        const title = await this._mediaTitleGeneratorService.generateTitleForMediaWithRestaurantKeywords(destinationRestaurantId);

        const id = newDbId().toString();
        const newMedia: Media = new Media({
            id,
            socialId: id,
            userId,
            title,
            name: media.name,
            duplicatedFromRestaurantId: sourceRestaurantId === destinationRestaurantId ? undefined : sourceRestaurantId,
            restaurantId: destinationRestaurantId,
            category: MediaCategory.ADDITIONAL,
            format: media.format,
            type: media.type,
            duration: media.duration,
            urls: media.urls,
            sizes: media.sizes,
            dimensions: media.dimensions,
            postIds: [],
            folderId: folderId ?? null,
            createdAt: new Date(),
            updatedAt: new Date(),
            thumbnail: media.thumbnail,
            originalMediaId: sourceRestaurantId === destinationRestaurantId ? (media.originalMediaId ?? media.id) : undefined, // Do not set originalMediaId if the media is not from the same restaurant
            aiDescription: media.aiDescription,
        });
        const { entityId, entityRelated } = media.getEntityRelated();
        assert(entityId, 'Missing entityId');
        const urls = await this._cloudStorage.duplicateObject(newMedia.format, newMedia.urls, entityRelated, entityId);
        newMedia.urls = urls;
        return this._mediasRepository.createMedia(newMedia).then((m) => m.toDto());
    }
}
