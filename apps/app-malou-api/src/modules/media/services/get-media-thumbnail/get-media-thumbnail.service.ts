import axios from 'axios';
import assert from 'node:assert/strict';
import sharp from 'sharp';
import { singleton } from 'tsyringe';

import { IMedia, toDbId } from '@malou-io/package-models';
import { MediaDimension, MediaType } from '@malou-io/package-utils';

import { MediasRepository } from ':modules/media/medias.repository';

@singleton()
export class GetMediaInfoService {
    constructor(private readonly _mediasRepository: MediasRepository) {}

    getUrlAndDimensions(media: IMedia): { url: string; dimensions: MediaDimension } | null {
        if (media.isV2) {
            assert(media.storedObjects, 'V2 medias must have storedObjects');
            assert(media.dimensions, 'V2 medias must have dimensions');
            if (media.storedObjects.normalized.publicUrl && media.dimensions.normalized) {
                return {
                    url: media.storedObjects.normalized.publicUrl,
                    dimensions: media.dimensions.normalized,
                };
            }
        } else {
            if (media.urls.original && media.dimensions?.original) {
                return {
                    url: media.urls.original,
                    dimensions: media.dimensions.original,
                };
            }
        }
        return null;
    }

    async getThumbnail1024OutsideUrlAndDimensions(media: IMedia): Promise<{ url: string; dimensions: MediaDimension } | null> {
        if (media.isV2) {
            assert(media.storedObjects, 'v2 medias must have stored objects');
            if (media.storedObjects.thumbnail1024Outside.publicUrl && media.dimensions?.thumbnail1024Outside) {
                return {
                    url: media.storedObjects.thumbnail1024Outside.publicUrl,
                    dimensions: media.dimensions.thumbnail1024Outside,
                };
            }
        } else {
            if (media.type === MediaType.PHOTO) {
                if (media.urls.igFit && media.dimensions?.igFit) {
                    return {
                        url: media.urls.igFit,
                        dimensions: media.dimensions.igFit,
                    };
                }
                if (media.urls.original && media.dimensions?.original) {
                    return {
                        url: media.urls.original,
                        dimensions: media.dimensions.original,
                    };
                }
            }
            if (media.type === MediaType.VIDEO) {
                const v1VideoThumbnailUrlAndDimensions = await this._getV1VideoThumbnailUrlAndDimensions(media);
                if (v1VideoThumbnailUrlAndDimensions) {
                    return v1VideoThumbnailUrlAndDimensions;
                }
            }
        }
        return null;
    }

    async getThumbnail256OutsideUrlAndDimensions(media: IMedia): Promise<{ url: string; dimensions: MediaDimension } | null> {
        if (media.isV2) {
            assert(media.storedObjects, 'V2 medias must have storedObjects');
            assert(media.dimensions, 'V2 medias must have dimensions');
            if (media.storedObjects.thumbnail256Outside.publicUrl && media.dimensions?.thumbnail256Outside) {
                return {
                    url: media.storedObjects.thumbnail256Outside.publicUrl,
                    dimensions: media.dimensions.thumbnail256Outside,
                };
            }
        } else {
            if (media.type === MediaType.PHOTO) {
                if (media.urls.small && media.dimensions?.small) {
                    return {
                        url: media.urls.small,
                        dimensions: media.dimensions.small,
                    };
                }
                if (media.urls.igFit && media.dimensions?.igFit) {
                    return {
                        url: media.urls.igFit,
                        dimensions: media.dimensions.igFit,
                    };
                }
                if (media.urls.original && media.dimensions?.original) {
                    return {
                        url: media.urls.original,
                        dimensions: media.dimensions.original,
                    };
                }
            }
            if (media.type === MediaType.VIDEO) {
                const v1VideoThumbnailUrlAndDimensions = await this._getV1VideoThumbnailUrlAndDimensions(media);
                if (v1VideoThumbnailUrlAndDimensions) {
                    return v1VideoThumbnailUrlAndDimensions;
                }
            }
        }
        return null;
    }

    private async _getV1VideoThumbnailUrlAndDimensions(media: IMedia): Promise<{ url: string; dimensions: MediaDimension } | null> {
        if (media.thumbnail) {
            let thumbnailDimensions: MediaDimension;
            if (media.dimensions?.thumbnail) {
                thumbnailDimensions = media.dimensions.thumbnail;
            } else {
                thumbnailDimensions = await this._getImageDimensions(media.thumbnail);
                await this._saveThumbnailDimensions(media._id.toString(), thumbnailDimensions);
            }
            return {
                url: media.thumbnail,
                dimensions: thumbnailDimensions,
            };
        }
        return null;
    }

    private async _getImageDimensions(imageUrl: string): Promise<MediaDimension> {
        const axiosRes = await axios.get(imageUrl, { responseType: 'arraybuffer' });
        const metadata = await sharp(axiosRes.data).metadata();
        assert(metadata.width);
        assert(metadata.height);
        return {
            width: metadata.width,
            height: metadata.height,
        };
    }

    private async _saveThumbnailDimensions(mediaId: string, dimensions: MediaDimension): Promise<void> {
        await this._mediasRepository.updateOne({
            filter: { _id: toDbId(mediaId) },
            update: { 'dimensions.thumbnail': dimensions },
            options: { new: true, lean: true },
        });
    }
}
