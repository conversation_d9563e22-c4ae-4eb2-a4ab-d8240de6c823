import lodash from 'lodash';
import { UpdateQuery } from 'mongoose';
import { singleton } from 'tsyringe';

import { DbId, IMedia, toDbIds } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import PostsRepository from ':modules/posts/posts.repository';

@singleton()
export class UpdateMediaPostIdsService {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _postsRepository: PostsRepository
    ) {}

    async updateMediaPostIds(postId: string, update: UpdateQuery<IMedia> | Partial<IMedia>): Promise<void> {
        logger.info('[UPDATE_MEDIA_POST_IDS]', { postId });
        const post = await this._postsRepository.findOne({
            filter: { _id: postId },
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });
        const postMediaIds = post?.attachments?.map((at) => at._id.toString()) ?? [];
        const parentMedias = await Promise.all(postMediaIds.map((mediaId) => this._getAllParentMedias(mediaId)));
        const parentMediaIds = parentMedias.flat().map((m) => m?._id.toString());
        const mediasIds = lodash.uniq([...postMediaIds, ...parentMediaIds].filter(isNotNil));
        await this._mediasRepository.updateMany({ filter: { _id: { $in: toDbIds(mediasIds) } }, update });
    }

    private async _getAllParentMedias(mediaId: string) {
        const MAX_LOOP_COUNT = 50;
        let media = await this._mediasRepository.findOne({
            filter: { _id: mediaId },
            projection: { originalMediaId: 1 },
            options: { lean: true },
        });

        if (!media || mediaId.toString() === media?.originalMediaId?.toString()) {
            return [];
        }

        const parentMedias: { _id: DbId }[] = [];
        let count = 0;

        // Get all parents recursively
        do {
            const parentMedia = await this._mediasRepository.findOne({
                filter: {
                    _id: media.originalMediaId,
                },
                projection: { _id: 1 },
                options: { lean: true },
            });

            if (!parentMedia) {
                return parentMedias;
            }

            parentMedias.push(parentMedia);
            media = parentMedia;
            count++;
        } while (media && count < MAX_LOOP_COUNT);
    }
}
