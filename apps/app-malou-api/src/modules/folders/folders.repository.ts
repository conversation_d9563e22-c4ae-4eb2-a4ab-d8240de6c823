import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { EntityRepository, FolderModel, IFolder, toDbId } from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { FoldersFilters } from ':helpers/filters/folders-filters';
import { MediaFilters } from ':helpers/filters/media-filters';
import { MediasRepository } from ':modules/media/medias.repository';

import { Folder, FolderProps } from './entities/folder.entity';

@singleton()
export class FoldersRepository extends EntityRepository<IFolder> {
    constructor(private _mediasRepository: MediasRepository) {
        super(FolderModel);
    }

    async findFolders({
        restaurantId,
        parentFolderId,
        nameFilter,
    }: {
        restaurantId: string;
        parentFolderId: string | null;
        nameFilter?: string;
    }): Promise<Folder[]> {
        const filters = new FoldersFilters({ restaurantId, parentFolderId, name: nameFilter });
        const query = filters.buildQuery();
        const documents = await this.find({
            filter: query,
            options: { lean: true },
        });
        return documents.map(this._toEntity);
    }

    async createFolder(data: FolderProps): Promise<Folder> {
        const document = await this.create({ data: this._toDocument(data), options: { lean: true } });
        return this._toEntity(document);
    }

    async findById(id: string): Promise<Folder> {
        const document = await this.findOneOrFail({
            filter: { _id: toDbId(id) },
            options: {
                lean: true,
            },
        });
        return this._toEntity(document);
    }

    async findSubFolders(folderId: string, restaurantId: string): Promise<Folder[]> {
        const documents = await this.find({
            filter: { parentFolderId: folderId, restaurantId },
            options: { lean: true },
        });
        return documents.map(this._toEntity);
    }

    async updateFolder(id: string, data: Partial<FolderProps>): Promise<Folder> {
        if (data.name) {
            const folderWithSameName = await this.findOne({
                filter: { restaurantId: data.restaurantId, name: data.name, parentFolderId: data.parentFolderId },
                options: { lean: true },
            });

            if (folderWithSameName) {
                throw new MalouError(MalouErrorCode.FOLDER_WITH_SAME_NAME_ALREADY_EXISTS, {
                    message: 'The restaurant already has a folder with the same name',
                    metadata: { restaurantId: data.restaurantId, name: data.name, parentFolderId: data.parentFolderId },
                });
            }
        }

        const folder = await this.findById(id);
        const document = await this.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: this._toDocument({
                ...folder,
                ...data,
            }),
        });

        assert(document);
        return this._toEntity(document);
    }

    async getFolderMediaCount(folderId: string, countOnlyNeverUsedMedia?: boolean): Promise<number> {
        // medias that are not original medias are not shown in the gallery or media picker
        const filters = new MediaFilters({
            isNeverUsed: countOnlyNeverUsedMedia,
            folderIds: [folderId],
        });

        const mediaCount = await this._mediasRepository.countDocuments({
            filter: filters.buildQuery(),
        });

        const nestedFolders = await this.find({
            filter: { parentFolderId: folderId },
            projection: { _id: true },
            options: { lean: true },
        });

        const nestedFoldersMediaCountPromises = nestedFolders.map((folder) =>
            this.getFolderMediaCount(folder._id.toString(), countOnlyNeverUsedMedia)
        );
        const nestedFoldersMediaCounts = await Promise.all(nestedFoldersMediaCountPromises);

        const nestedFoldersMediaCount = nestedFoldersMediaCounts.reduce((totalCount, folderMediaCount) => {
            return totalCount + folderMediaCount;
        }, 0);

        return mediaCount + nestedFoldersMediaCount;
    }

    private _toEntity(folderDocument: IFolder): Folder {
        return new Folder({
            id: folderDocument._id.toString(),
            name: folderDocument.name,
            restaurantId: folderDocument.restaurantId.toString(),
            createdAt: folderDocument.createdAt,
            updatedAt: folderDocument.updatedAt,
            parentFolderId: folderDocument.parentFolderId?.toString() ?? null,
            duplicatedFromRestaurantId: folderDocument.duplicatedFromRestaurantId?.toString(),
        });
    }

    private _toDocument(folder: FolderProps): IFolder {
        return {
            _id: toDbId(folder.id),
            name: folder.name,
            restaurantId: toDbId(folder.restaurantId),
            createdAt: folder.createdAt ?? new Date(),
            updatedAt: folder.updatedAt ?? new Date(),
            parentFolderId: folder.parentFolderId ? toDbId(folder.parentFolderId) : undefined,
            duplicatedFromRestaurantId: folder.duplicatedFromRestaurantId ? toDbId(folder.duplicatedFromRestaurantId) : undefined,
        };
    }
}
