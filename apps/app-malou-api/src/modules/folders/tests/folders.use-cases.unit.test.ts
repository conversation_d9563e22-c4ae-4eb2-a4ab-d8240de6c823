import { omit } from 'lodash';
import { container } from 'tsyringe';

import { DbId, newDbId, toDbId } from '@malou-io/package-models';
import { isNotNil, MalouErrorCode } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MediasRepository } from ':modules/media/medias.repository';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { CloudStorage } from ':plugins/cloud-storage/cloud-storage.interface';
import { AwsS3 } from ':plugins/cloud-storage/s3';
import { ExperimentationService } from ':services/experimentations-service/experimentation.service';

import { FoldersRepository } from '../folders.repository';
import { FoldersUseCases } from '../folders.use-cases';
import { getDefaultFolder } from './folder.builder';

describe('foldersUseCases', () => {
    beforeAll(() => {
        registerRepositories(['FoldersRepository', 'MediasRepository', 'RestaurantsRepository']);
        container.registerInstance(AwsS3, {
            duplicateObject: jest.fn().mockResolvedValue({
                original: 'www.aws.com/files/original.png',
            }),
        } as unknown as CloudStorage);
        class ExperimentationServiceMock {
            isFeatureAvailable = jest.fn().mockResolvedValue(false);
        }
        container.register(ExperimentationService, { useValue: new ExperimentationServiceMock() as unknown as ExperimentationService });
    });

    describe('createFolder', () => {
        let testCase: TestCaseBuilderV2<'folders' | 'restaurants'>;

        beforeEach(async () => {
            testCase = new TestCaseBuilderV2<'folders' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234406').build(),
                            ];
                        },
                    },
                    folders: {
                        data(dependencies) {
                            return [
                                getDefaultFolder().name('First folder').restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultFolder().name('Second folder').restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultFolder().name('Third folder').restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [
                        {
                            name: 'Another folder',
                            parentFolderId: null,
                            mediaCount: 0,
                        },
                        {
                            name: 'Another folder in another restaurant',
                            parentFolderId: null,
                            mediaCount: 0,
                        },
                    ];
                },
            });

            await testCase.build();
        });

        it('should create the given folders', async () => {
            // arrange phase
            const foldersUseCases = container.resolve(FoldersUseCases);
            const expectedResult = testCase.getExpectedResult();
            const seededObjects = testCase.getSeededObjects();

            const foldersToCreate = [
                { name: 'Another folder', parentFolderId: null },
                { name: 'Another folder in another restaurant', parentFolderId: null },
            ];

            // act phase
            const firstRestaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const firstCreatedFolder = await foldersUseCases.createFolder(firstRestaurantId, foldersToCreate[0]);

            const secondRestaurantId = (seededObjects.restaurants[1]._id as DbId).toString();
            const secondCreatedFolder = await foldersUseCases.createFolder(secondRestaurantId, foldersToCreate[1]);

            // assert phase
            expect(omit(firstCreatedFolder, 'id')).toStrictEqual(expectedResult[0]);
            expect(omit(secondCreatedFolder, 'id')).toStrictEqual(expectedResult[1]);
        });

        it('should throw an error when folder to create is not valid', async () => {
            // arrange phase
            const foldersUseCases = container.resolve(FoldersUseCases);
            const seededObjects = testCase.getSeededObjects();

            const folderToCreate = { name: 'Second folder', parentFolderId: null };
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();

            // act and assert phases
            await expect(foldersUseCases.createFolder(restaurantId, folderToCreate)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.FOLDER_WITH_SAME_NAME_ALREADY_EXISTS,
                    message: 'The restaurant already has a folder with the same name',
                })
            );
        });
    });

    describe('getFolderMediaCount', () => {
        it('should return the expected media count for each folder ID', async () => {
            const foldersRepository = container.resolve(FoldersRepository);

            const testCase = new TestCaseBuilderV2<'medias' | 'folders'>({
                seeds: {
                    folders: {
                        data() {
                            return [getDefaultFolder().name('First folder').build(), getDefaultFolder().name('Second folder').build()];
                        },
                    },
                    medias: {
                        data(dependencies) {
                            return [
                                getDefaultMedia().folderId(dependencies.folders()[1]._id).socialId('12093809481078').build(),
                                getDefaultMedia().folderId(dependencies.folders()[1]._id).socialId('45342850980992').build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        [(dependencies.folders[0]._id as DbId).toString()]: 0,
                        [(dependencies.folders[1]._id as DbId).toString()]: 2,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();
            const folderIds = Object.keys(expectedResult);
            const expectedMediaCount = Object.values(expectedResult);

            const foldersMediaCountPromises = folderIds.map((folderId) => {
                return foldersRepository.getFolderMediaCount(folderId);
            });
            const foldersMediaCount = await Promise.all(foldersMediaCountPromises);

            expect(foldersMediaCount).toEqual(expectedMediaCount);
        });

        it('should return the expected media count for each folder ID with countOnlyNeverUsedMedia filter', async () => {
            const foldersRepository = container.resolve(FoldersRepository);
            const postId = newDbId();

            const testCase = new TestCaseBuilderV2<'medias' | 'folders'>({
                seeds: {
                    folders: {
                        data() {
                            return [getDefaultFolder().name('First folder').build(), getDefaultFolder().name('Second folder').build()];
                        },
                    },
                    medias: {
                        data(dependencies) {
                            return [
                                getDefaultMedia().folderId(dependencies.folders()[1]._id).socialId('12093809481078').postIds([]).build(),
                                getDefaultMedia()
                                    .folderId(dependencies.folders()[1]._id)
                                    .socialId('45342850980992')
                                    .postIds([postId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        [dependencies.folders[0]._id.toString()]: 0,
                        [dependencies.folders[1]._id.toString()]: 1,
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();
            const folderIds = Object.keys(expectedResult);
            const expectedMediaCount = Object.values(expectedResult);

            const foldersMediaCountPromises = folderIds.map((folderId) => {
                return foldersRepository.getFolderMediaCount(folderId, true);
            });
            const foldersMediaCount = await Promise.all(foldersMediaCountPromises);

            expect(foldersMediaCount).toEqual(expectedMediaCount);
        });
    });

    describe('duplicateFolders', () => {
        it('should duplicate the given folders and their medias to the same restaurant', async () => {
            // arrange phase
            const foldersUseCases = container.resolve(FoldersUseCases);
            const mediasRepository = container.resolve(MediasRepository);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'medias' | 'folders'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    folders: {
                        data(dependencies) {
                            return [
                                getDefaultFolder().name('First folder').restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultFolder().name('Second folder').restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                            ];
                        },
                    },
                    medias: {
                        data(dependencies) {
                            return [
                                getDefaultMedia()
                                    .name('First media')
                                    .folderId(dependencies.folders()[1]._id)
                                    .socialId('12093809481078')
                                    .build(),
                                getDefaultMedia()
                                    .name('Second media')
                                    .folderId(dependencies.folders()[1]._id)
                                    .socialId('45342850980992')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        fnResult: [
                            {
                                name: 'Copie de First folder',
                                parentFolderId: null,
                                mediaCount: 0,
                            },
                            {
                                name: 'Copie de Second folder',
                                parentFolderId: null,
                                mediaCount: 2,
                            },
                        ],
                        duplicatedMediaNames: [[], [dependencies.medias[0].name, dependencies.medias[1].name]],
                    };
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const seeds = testCase.getSeededObjects();

            const foldersToDuplicate = seeds.folders.map((folder) => ({
                id: (folder._id as DbId).toString(),
                name: folder.name,
                parentFolderId: (folder.parentFolderId as DbId | null)?.toString() ?? null,
            }));

            const user = { _id: '60917d6595edfb0213c8d1ea', defaultLanguage: 'fr' };
            const restaurantId = (seeds.restaurants[0]._id as DbId).toString();

            // act phase
            const duplicatedFolders = await foldersUseCases.duplicateFolders(foldersToDuplicate, [restaurantId], restaurantId, user);
            const duplicatedFoldersWithoutCreatedIds = duplicatedFolders.map((folder) => omit(folder, 'id'));

            const duplicatedMediaNames: string[][] = [];

            for (const folder of duplicatedFolders) {
                const result = await mediasRepository.find({ filter: { folderId: folder.id } });
                duplicatedMediaNames.push(
                    result
                        .map((media) => media.name)
                        .filter(isNotNil)
                        .sort()
                );
            }

            // assert phase
            expect(duplicatedMediaNames).toIncludeSameMembers(expectedResult.duplicatedMediaNames);
            expect(duplicatedFoldersWithoutCreatedIds).toIncludeSameMembers(expectedResult.fnResult);
        });

        it('should duplicate the given folders and their medias to the given restaurant', async () => {
            // arrange phase
            const foldersUseCases = container.resolve(FoldersUseCases);
            const mediasRepository = container.resolve(MediasRepository);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'medias' | 'folders'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('12345').build(), getDefaultRestaurant().uniqueKey('67890').build()];
                        },
                    },
                    folders: {
                        data(dependencies) {
                            return [
                                getDefaultFolder().name('First folder').restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultFolder().name('Second folder').restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                            ];
                        },
                    },
                    medias: {
                        data(dependencies) {
                            return [
                                getDefaultMedia()
                                    .name('First media')
                                    .folderId(dependencies.folders()[1]._id)
                                    .socialId('12093809481078')
                                    .build(),
                                getDefaultMedia()
                                    .name('Second media')
                                    .folderId(dependencies.folders()[1]._id)
                                    .socialId('45342850980992')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        fnResult: [
                            {
                                id: expect.any(String),
                                name: 'Copie de First folder',
                                parentFolderId: null,
                                mediaCount: 0,
                            },
                            {
                                id: expect.any(String),
                                name: 'Copie de Second folder',
                                parentFolderId: null,
                                mediaCount: 2,
                            },
                        ],
                        duplicatedMediaNames: [[], [dependencies.medias[0].name, dependencies.medias[1].name]],
                    };
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const seeds = testCase.getSeededObjects();

            const foldersToDuplicate = seeds.folders.map((folder) => ({
                id: (folder._id as DbId).toString(),
                name: folder.name,
                parentFolderId: (folder.parentFolderId as DbId | null)?.toString() ?? null,
            }));

            const user = { _id: '60917d6595edfb0213c8d1ea', defaultLanguage: 'fr' };
            const restaurantId = (seeds.restaurants[0]._id as DbId).toString();

            // act phase
            const duplicatedFolders = await foldersUseCases.duplicateFolders(foldersToDuplicate, [restaurantId], restaurantId, user);
            const duplicatedMediaNames: string[][] = [];

            for (const folder of duplicatedFolders) {
                const result = await mediasRepository.find({ filter: { folderId: folder.id } });
                duplicatedMediaNames.push(
                    result
                        .map((media) => media.name)
                        .filter(isNotNil)
                        .sort()
                );
            }

            // assert phase
            expect(duplicatedMediaNames).toIncludeSameMembers(expectedResult.duplicatedMediaNames);
            expect(duplicatedFolders).toIncludeSameMembers(expectedResult.fnResult);
        });
    });
});
