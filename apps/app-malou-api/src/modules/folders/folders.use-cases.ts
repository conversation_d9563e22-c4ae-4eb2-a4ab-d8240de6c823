import { singleton } from 'tsyringe';

import { CreateFolderBodyDto, FolderWithMediaCountDto, MediaDto, UpdateFolderBodyDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { Locale, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { MediaFilters } from ':helpers/filters/media-filters';
import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { DeleteMediasUseCase } from ':modules/media/use-cases/delete-medias/delete-medias.use-case';
import { DuplicateMediasUseCase } from ':modules/media/use-cases/duplicate-medias/duplicate-medias.use-case';
import PostsUseCases from ':modules/posts/posts.use-cases';
import { Translation } from ':services/translation.service';

import { Folder } from './entities/folder.entity';
import { FoldersDtoMapper } from './folders.mapper.dto';
import { FoldersRepository } from './folders.repository';

@singleton()
export class FoldersUseCases {
    constructor(
        private readonly _foldersRepository: FoldersRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _foldersDtoMapper: FoldersDtoMapper,
        private readonly _translationService: Translation,
        private readonly _deleteMediaUseCases: DeleteMediasUseCase,
        private readonly _duplicateMediasUseCase: DuplicateMediasUseCase
    ) {}

    async getFolderWithMediaCountById(folderId: string): Promise<FolderWithMediaCountDto | null> {
        const folder = await this._foldersRepository.findById(folderId);

        if (!folder) {
            return null;
        }

        const mediaCount = await this._foldersRepository.getFolderMediaCount(folder.id);
        return this._foldersDtoMapper.toFolderDto(folder, mediaCount);
    }

    async getFoldersWithMediaCount(
        filters: {
            restaurantId: string;
            parentFolderId: string | null;
            nameFilter?: string;
        },
        countOnlyNeverUsedMedia: boolean
    ): Promise<FolderWithMediaCountDto[]> {
        const folders = await this._foldersRepository.findFolders(filters);

        const folderDtoPromises = folders.map(async (folder) => {
            const mediaCount = await this._foldersRepository.getFolderMediaCount(folder.id, countOnlyNeverUsedMedia);
            return this._foldersDtoMapper.toFolderDto(folder, mediaCount);
        });

        return Promise.all(folderDtoPromises);
    }

    async getFolderMedia(folderId: string): Promise<MediaDto[]> {
        const medias = await this._mediasRepository.getFolderMedias(folderId);
        const mediaDtos = medias.map((media) => media.toDto());

        return mediaDtos;
    }

    async createFolder(restaurantId: string, folderData: CreateFolderBodyDto): Promise<FolderWithMediaCountDto> {
        const folderWithSameName = await this._foldersRepository.findOne({
            filter: { restaurantId, name: folderData.name, parentFolderId: folderData.parentFolderId },
            options: { lean: true },
        });

        if (folderWithSameName) {
            throw new MalouError(MalouErrorCode.FOLDER_WITH_SAME_NAME_ALREADY_EXISTS, {
                message: 'The restaurant already has a folder with the same name',
                metadata: { restaurantId, name: folderData.name, parentFolderId: folderData.parentFolderId },
            });
        }

        const folder = new Folder({
            id: newDbId().toString(),
            name: folderData.name,
            restaurantId,
            parentFolderId: folderData.parentFolderId ? folderData.parentFolderId : null,
        });
        const newFolder = await this._foldersRepository.createFolder(folder);
        return this._foldersDtoMapper.toFolderDto(newFolder, 0);
    }

    // TODO: use deleteMany with a hook
    async deleteFolders(folderIds: string[]): Promise<void> {
        const promises = folderIds.map((folderId) => this.deleteFolder(folderId));
        await Promise.allSettled(promises);
    }

    // TODO use hook
    async deleteFolder(folderId: string): Promise<void> {
        try {
            const medias = await this._mediasRepository.getFolderMedia(folderId);

            if (medias.length > 0) {
                const promises = medias.map((media) => this._deleteMediaUseCases.deleteMedium(media));
                await Promise.all(promises);
            }

            await this._foldersRepository.deleteOne({ filter: { _id: folderId } });
        } catch (error) {
            logger.warn('[DELETE_FOLDER_ERROR]', error);
            throw new Error(`An error occured while deleting folder with id ${folderId}`);
        }
    }

    async getPublishedMediaCountInFolders(restaurantId: string, folderIds: string[]): Promise<number> {
        const promises = folderIds.map(async (folderId) => {
            const filters = new MediaFilters({ folderIds });
            const query = filters.buildQuery();
            const mediasInFolder = await this._mediasRepository.find({ filter: query, projection: { _id: true }, options: { lean: true } });
            const mediaIds = mediasInFolder.map((media) => media._id);
            const postsWithMediaInFolder = await this._postsUseCases.getRestaurantPostsByMediaIds(restaurantId, mediaIds);

            const childrenFolders = await this._foldersRepository.findSubFolders(folderId, restaurantId);
            const childrenFolderIds = childrenFolders.map((folder) => folder.id);
            const publishedMediaCountInChildrenFolders = await this.getPublishedMediaCountInFolders(restaurantId, childrenFolderIds);

            return postsWithMediaInFolder.length + publishedMediaCountInChildrenFolders;
        });

        const publishedMediaCountInFoldersList = await Promise.all(promises);

        return publishedMediaCountInFoldersList.reduce((totalCount, currentCount) => totalCount + currentCount, 0);
    }

    async updateFolder(folderId: string, updateData: UpdateFolderBodyDto): Promise<FolderWithMediaCountDto> {
        const updatedFolder = await this._foldersRepository.updateFolder(folderId, updateData);
        const mediaCount = await this._foldersRepository.getFolderMediaCount(updatedFolder.id);
        return this._foldersDtoMapper.toFolderDto(updatedFolder, mediaCount);
    }

    async duplicateFolders(
        foldersToDuplicate: {
            parentFolderId: string | null;
            name: string;
            id: string;
        }[],
        restaurantIds: string[],
        fromRestId: string,
        user: { _id: string; defaultLanguage: string }
    ): Promise<FolderWithMediaCountDto[]> {
        const duplicatedFoldersInRestaurantsPromises = foldersToDuplicate.map((folder) =>
            this._duplicateFolderInRestaurants(folder, restaurantIds, fromRestId, user)
        );

        const duplicatedFoldersInRestaurants = await Promise.all(duplicatedFoldersInRestaurantsPromises);
        const duplicatedFolders = duplicatedFoldersInRestaurants.flat(1);

        const folders = duplicatedFolders.map(({ folder, mediaCount }) => this._foldersDtoMapper.toFolderDto(folder, mediaCount));
        return folders;
    }

    private async _duplicateFolderInRestaurants(
        folderToDuplicate: {
            parentFolderId: string | null;
            name: string;
            id: string;
        },
        restaurantIds: string[],
        fromRestId: string,
        user: { _id: string; defaultLanguage: string }
    ): Promise<{ folder: Folder; mediaCount: number }[]> {
        const duplicatedFolderInRestaurantsPromises = restaurantIds.map((restaurantId) =>
            this._duplicateFolder(folderToDuplicate, restaurantId, fromRestId, user)
        );
        const duplicatedFolderInRestaurants = await Promise.all(duplicatedFolderInRestaurantsPromises);

        return duplicatedFolderInRestaurants;
    }

    private async _duplicateFolder(
        folderToDuplicate: {
            parentFolderId: string | null;
            name: string;
            id: string;
        },
        restaurantId: string,
        fromRestId: string,
        user: { _id: string; defaultLanguage: string }
    ): Promise<{ folder: Folder; mediaCount: number }> {
        const restaurantFolders = await this._foldersRepository.find({
            filter: { restaurantId, parentFolderId: null },
            options: { lean: true },
        });
        const restaurantFolderNames = restaurantFolders.map((folder) => folder.name);
        const newName = this._getDuplicatedFolderName(folderToDuplicate.name, restaurantFolderNames, user.defaultLanguage as Locale);
        const folder = new Folder({
            id: newDbId().toString(),
            name: newName,
            restaurantId,
            parentFolderId: null,
            duplicatedFromRestaurantId: fromRestId,
        });
        const duplicatedFolder = await this._foldersRepository.createFolder(folder);
        const duplicatedMedias = await this._duplicateFolderMedias(duplicatedFolder.id, folderToDuplicate, restaurantId, fromRestId, user);
        await this._duplicateNestedFolder(duplicatedFolder.id, folderToDuplicate, restaurantId, fromRestId, user);

        return { folder: duplicatedFolder, mediaCount: duplicatedMedias.length };
    }

    private _getDuplicatedFolderName(folderToDuplicateName: string, restaurantFolderNames: string[], userDefaultLanguage: Locale): string {
        const lang = userDefaultLanguage;
        const duplicationPrefix = this._translationService.fromLang({ lang }).gallery.copy_of();

        const newNameBase = `${duplicationPrefix} ${folderToDuplicateName}`;
        let newName = newNameBase;
        let count = 0;

        while (restaurantFolderNames.includes(newName)) {
            count++;
            newName = `${newNameBase} (${count})`;
        }

        return newName;
    }

    private async _duplicateFolderMedias(
        duplicatedFolderId: string,
        folderToDuplicate: {
            parentFolderId: string | null;
            name: string;
            id: string;
        },
        restaurantId: string,
        fromRestId: string,
        user: { _id: string; defaultLanguage: string }
    ): Promise<MediaDto[]> {
        const mediasToDuplicate = await this._mediasRepository.getFolderMedias(folderToDuplicate.id);

        const duplicatedMediasPromises = mediasToDuplicate.map((media) =>
            this._duplicateMediasUseCase
                .duplicateRestaurantMedia(media, restaurantId, user._id, fromRestId, duplicatedFolderId)
                .then((duplicatedMedia) => duplicatedMedia)
        );

        return Promise.all(duplicatedMediasPromises);
    }

    private async _duplicateNestedFolder(
        duplicatedFolderId: string,
        folderToDuplicate: {
            parentFolderId: string | null;
            name: string;
            id: string;
        },
        restaurantId: string,
        fromRestId: string,
        user: { _id: string; defaultLanguage: string }
    ): Promise<void> {
        const nestedFolders = await this._foldersRepository.findSubFolders(folderToDuplicate.id, restaurantId);

        if (nestedFolders) {
            const nestFoldersToDuplicate = nestedFolders.map((nestedFolder) => ({
                parentFolderId: duplicatedFolderId,
                name: nestedFolder.name,
                id: nestedFolder.id,
            }));

            await this.duplicateFolders(nestFoldersToDuplicate, [restaurantId], fromRestId, user);
        }
    }
}
