import { singleton } from 'tsyringe';

import { PollingStoriesStatusResponseDto } from '@malou-io/package-dto';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class PollingStoriesStatusesUseCase {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(bindingIds: string[]): Promise<PollingStoriesStatusResponseDto[]> {
        const stories = await this._storiesRepository.find({
            filter: { bindingId: { $in: bindingIds }, isStory: true },
            projection: { _id: 1, published: 1, isPublishing: 1, bindingId: 1, attachments: 1 },
            options: { lean: true },
        });
        return stories.map((post) => ({
            postId: post._id.toString(),
            published: post.published,
            isPublishing: post.isPublishing ?? false,
            attachmentsLength: post.attachments?.length ?? 0,
            bindingId: post.bindingId,
        }));
    }
}
