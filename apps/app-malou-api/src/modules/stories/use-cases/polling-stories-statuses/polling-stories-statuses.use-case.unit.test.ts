import { container } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';

import { PostPublicationStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { PollingStoriesStatusesUseCase } from ':modules/stories/use-cases/polling-stories-statuses/polling-stories-statuses.use-case';

describe('PollingStoriesStatusesUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository']);
    });

    it('should return posts statuses', async () => {
        const bindingIds = [uuidv4(), uuidv4(), uuidv4()];

        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost()
                                .bindingId(bindingIds[0])
                                .isStory(true)
                                .published(PostPublicationStatus.PENDING)
                                .isPublishing(true)
                                .build(),
                            getDefaultPost()
                                .isStory(true)
                                .bindingId(bindingIds[1])
                                .published(PostPublicationStatus.PUBLISHED)
                                .isPublishing(false)
                                .build(),
                            getDefaultPost()
                                .isStory(true)
                                .bindingId(bindingIds[2])
                                .published(PostPublicationStatus.ERROR)
                                .isPublishing(undefined)
                                .build(),
                            getDefaultPost().build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                const posts = dependencies.posts;

                return posts
                    .filter((post) => post.bindingId && bindingIds.includes(post.bindingId))
                    .map((post) => ({
                        postId: post._id.toString(),
                        published: post.published,
                        isPublishing: post.isPublishing ?? false,
                        attachmentsLength: 0,
                        bindingId: post.bindingId,
                    }));
            },
        });

        await testCase.build();

        const useCase = container.resolve(PollingStoriesStatusesUseCase);
        const result = await useCase.execute(bindingIds);

        const expectedResult = testCase.getExpectedResult();
        expect(result).toIncludeSameMembers(expectedResult);
    });
});
