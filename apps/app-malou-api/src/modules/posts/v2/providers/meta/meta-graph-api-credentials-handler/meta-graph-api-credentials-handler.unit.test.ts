import { err, ok } from 'neverthrow';
import { container } from 'tsyringe';
import { z } from 'zod';

import { newDbId } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultFacebookCredential } from ':modules/credentials/tests/credential.builder';
import { MetaGraphApiCredentialsHandlerErrorCodes } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiCredentialsHandler } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.service';
import { MetaGraphApiProvider } from ':modules/posts/v2/providers/meta/meta-graph-api-provider/meta-graph-api.provider';

describe('MetaGraphApiCredentialsHandler', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['FacebookCredentialsRepository']);
    });

    describe('callApi', () => {
        it('should return CREDENTIAL_NOT_FOUND', async () => {
            const credentialId = newDbId();
            const fbOrIgPageId = '123456';

            const credentialsHandler = container.resolve(MetaGraphApiCredentialsHandler);
            const result = await credentialsHandler.callApi({
                responseValidator: z.any(),
                credentialOptions: { type: 'page', credentialId: credentialId.toString(), fbOrIgPageId },
                requestOptions: { method: 'GET' as const, endpoint: 'fake', queryParams: {} },
            });
            expect(result.isErr()).toBeTrue();
            expect(result.isErr() ? result.error.code : undefined).toBe(MetaGraphApiCredentialsHandlerErrorCodes.CREDENTIAL_NOT_FOUND);
        });

        it('should return CREDENTIAL_PAGE_ACCESS_TOKEN_NOT_FOUND', async () => {
            const credentialId = newDbId();
            const fbOrIgPageId = '123456';
            const testCase = new TestCaseBuilderV2<'facebookCredentials'>({
                seeds: {
                    facebookCredentials: {
                        data() {
                            return [getDefaultFacebookCredential()._id(credentialId).build()];
                        },
                    },
                },
            });
            await testCase.build();

            const credentialsHandler = container.resolve(MetaGraphApiCredentialsHandler);
            const result = await credentialsHandler.callApi({
                responseValidator: z.any(),
                credentialOptions: { type: 'page', credentialId: credentialId.toString(), fbOrIgPageId },
                requestOptions: { method: 'GET' as const, endpoint: 'fake', queryParams: {} },
            });
            expect(result.isErr()).toBeTrue();
            expect(result.isErr() ? result.error.code : undefined).toBe(
                MetaGraphApiCredentialsHandlerErrorCodes.CREDENTIAL_PAGE_ACCESS_TOKEN_NOT_FOUND
            );
        });

        it('should return Ok at try 1', async () => {
            const credentialId = newDbId();
            const fbOrIgPageId = '123456';
            const testCase = new TestCaseBuilderV2<'facebookCredentials'>({
                seeds: {
                    facebookCredentials: {
                        data() {
                            return [
                                getDefaultFacebookCredential()
                                    ._id(credentialId)
                                    .pageAccess([{ fbPageId: fbOrIgPageId, pageAccessToken: 'page access token' }])
                                    .build(),
                            ];
                        },
                    },
                },
            });
            await testCase.build();

            const resultData = 'some result data';
            const metaGraphApiProviderMock = {
                async callApi(..._args: any[]): Promise<any> {
                    return ok(resultData);
                },
            } as MetaGraphApiProvider;
            container.registerInstance(MetaGraphApiProvider, metaGraphApiProviderMock);

            const credentialsHandler = container.resolve(MetaGraphApiCredentialsHandler);
            const result = await credentialsHandler.callApi({
                responseValidator: z.string(),
                credentialOptions: { type: 'page', credentialId: credentialId.toString(), fbOrIgPageId },
                requestOptions: { method: 'GET' as const, endpoint: 'fake', queryParams: {} },
            });
            expect(result.isOk()).toBeTrue();
            expect(result.isOk() ? result.value : undefined).toBe(resultData);
        });

        it('should return Err at try 1 if not INVALID_TOKEN', async () => {
            const credentialId = newDbId();
            const fbOrIgPageId = '123456';
            const testCase = new TestCaseBuilderV2<'facebookCredentials'>({
                seeds: {
                    facebookCredentials: {
                        data() {
                            return [
                                getDefaultFacebookCredential()
                                    ._id(credentialId)
                                    .pageAccess([{ fbPageId: fbOrIgPageId, pageAccessToken: 'page access token' }])
                                    .build(),
                            ];
                        },
                    },
                },
            });
            await testCase.build();

            const metaGraphApiProviderMock = {
                async callApi(..._args: any[]): Promise<any> {
                    return err(MetaGraphApiCredentialsHandlerErrorCodes.MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION);
                },
            } as MetaGraphApiProvider;
            container.registerInstance(MetaGraphApiProvider, metaGraphApiProviderMock);

            const credentialsHandler = container.resolve(MetaGraphApiCredentialsHandler);
            const result = await credentialsHandler.callApi({
                responseValidator: z.string(),
                credentialOptions: { type: 'page', credentialId: credentialId.toString(), fbOrIgPageId },
                requestOptions: { method: 'GET' as const, endpoint: 'fake', queryParams: {} },
            });
            expect(result.isErr()).toBeTrue();
            expect(result.isErr() ? result.error : undefined).toBe(
                MetaGraphApiCredentialsHandlerErrorCodes.MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION
            );
        });
    });
});
