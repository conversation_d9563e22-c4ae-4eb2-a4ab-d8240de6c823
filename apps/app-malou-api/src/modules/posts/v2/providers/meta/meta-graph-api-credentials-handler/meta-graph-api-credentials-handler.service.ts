import { omit } from 'lodash';
import { err, Result } from 'neverthrow';
import { singleton } from 'tsyringe';
import { ZodType } from 'zod';

import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import {
    MetaGraphApiCredentialsHandlerError,
    MetaGraphApiCredentialsHandlerErrorCodes,
} from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiRequestOptions } from ':modules/posts/v2/providers/meta/meta-graph-api-provider/meta-graph-api.definitions';
import { MetaGraphApiProvider } from ':modules/posts/v2/providers/meta/meta-graph-api-provider/meta-graph-api.provider';

/**
 * Like MetaGraphApiProvider, but does retries among credentials.
 * This class should not be used directly except by MetaGraphApiHelper. You should probably use MetaGraphApiHelper instead.
 */
@singleton()
export class MetaGraphApiCredentialsHandler {
    constructor(
        private readonly _metaGraphApiProvider: MetaGraphApiProvider,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository
    ) {}

    async callApi<T>(params: {
        responseValidator: ZodType<T>;
        credentialOptions: { type: 'page'; credentialId: string; fbOrIgPageId: string } | { type: 'user'; credentialId: string };
        requestOptions: MetaGraphApiRequestOptions;
    }): Promise<Result<T, MetaGraphApiCredentialsHandlerError>> {
        logger.info('[MetaGraphApiCredentialsHandler.callApi] Start', { ...omit(params, ['responseValidator']) });

        const credential = await this._facebookCredentialsRepository.getCredentialByIdSafely(params.credentialOptions.credentialId);
        if (!credential) {
            logger.error('[MetaGraphApiCredentialsHandler.callApi] No credential found');
            return err({ code: MetaGraphApiCredentialsHandlerErrorCodes.CREDENTIAL_NOT_FOUND });
        }

        let accessToken: string;
        const credentialOptions = params.credentialOptions;
        if (credentialOptions.type === 'user') {
            accessToken = credential.userAccessToken;
        } else {
            const pageAccess = credential.pageAccess.find(
                (access) => access.fbPageId === credentialOptions.fbOrIgPageId || access.igPageId === credentialOptions.fbOrIgPageId
            );
            const pageAccessToken = pageAccess?.pageAccessToken;
            if (!pageAccess || !pageAccessToken) {
                logger.error('[MetaGraphApiCredentialsHandler._callApiWithFbOrInstaPageId] No page access token', {
                    pageAccesses: credential.pageAccess,
                });
                return err({ code: MetaGraphApiCredentialsHandlerErrorCodes.CREDENTIAL_PAGE_ACCESS_TOKEN_NOT_FOUND });
            }
            accessToken = pageAccessToken;
        }

        const res = await this._metaGraphApiProvider.callApi({
            responseValidator: params.responseValidator,
            token: accessToken,
            requestOptions: params.requestOptions,
        });
        if (res.isOk()) {
            if (params.credentialOptions.type === 'page') {
                await this._updateCredentialLastSeenWorking(params.credentialOptions.credentialId, accessToken);
            }
        }
        return res;
    }

    private async _updateCredentialLastSeenWorking(credentialId: string, pageAccessToken: string): Promise<void> {
        await this._facebookCredentialsRepository.findOneAndUpdate({
            filter: {
                _id: toDbId(credentialId),
                'pageAccess.pageAccessToken': pageAccessToken,
            },
            update: {
                'pageAccess.$.lastSeenWorking': new Date(),
            },
        });
    }
}
