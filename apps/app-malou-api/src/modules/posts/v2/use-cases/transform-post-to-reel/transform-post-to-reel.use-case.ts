import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { SocialPostDto } from '@malou-io/package-dto';
import {
    MalouErrorCode,
    MediaType,
    PostPublicationStatus,
    PostType,
    PublicationType,
    TransformDataComputerService,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { MediasRepository } from ':modules/media/medias.repository';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';

@singleton()
export class TransformPostToReelUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _mediasRepository: MediasRepository
    ) {}

    async execute({ postId, author }: { postId: string; author: PostAuthorProps }): Promise<SocialPostDto> {
        const post = await this._postsRepository.getSocialPostById(postId);

        if (!post) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { message: 'Post not found', metadata: { postId } });
        }

        post.postType = PostType.REEL;
        post.title = undefined;

        const media = post.attachments[0];
        assert(media?.type === MediaType.VIDEO, 'Media video is required for a reel post');
        assert(post.published !== PostPublicationStatus.PUBLISHED, 'Post must be unpublished to be transformed to a reel');

        const newTransformArea = TransformDataComputerService.computeDefaultAreaFor(PublicationType.REEL, media.aspectRatio);
        const newAspectRatio = TransformDataComputerService.computePreferredAspectRatioFor(PublicationType.REEL, media.aspectRatio);
        media.transformData = { ...newTransformArea, aspectRatio: newAspectRatio, rotationInDegrees: 0 };

        await this._mediasRepository.updateTransformData(media.id, media.transformData);
        const newPost = await this._postsRepository.updateSocialPost(post.id, post, new PostAuthor(author));
        if (!newPost) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { message: 'Post not found', metadata: { postId: post.id } });
        }
        return newPost.toDto();
    }
}
