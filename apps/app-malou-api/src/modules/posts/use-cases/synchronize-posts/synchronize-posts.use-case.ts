import { FilterQuery } from 'mongoose';
import { singleton } from 'tsyringe';

import { IPost } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CommentsRepository } from ':modules/comments/comments.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformPostGetter } from ':modules/posts/platforms/getter';
import { MalouPostData } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

type SynchronizePostsUseCaseOptions = {
    recentPostsOnly: boolean;
};

@singleton()
export class SynchronizePostsUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _commentsRepository: CommentsRepository,
        private readonly _platformPostGetter: PlatformPostGetter
    ) {}

    async execute(
        restaurantId: string,
        keys: PlatformKey[],
        options: SynchronizePostsUseCaseOptions = { recentPostsOnly: false }
    ): Promise<void> {
        try {
            logger.info('[SYNCHRONIZE_RESTAURANT_POSTS] Starting', { restaurantId });
            const platforms = await this._platformsRepository.getPlatformsByRestaurantId(restaurantId);

            const { recentPostsOnly } = options;
            const platformsToSync = platforms.filter((platform) => keys.includes(platform.key));

            const mappedPostsPerPlatform = await Promise.all(
                platformsToSync.map((platform) =>
                    this._platformPostGetter.getPlatformPostUseCases(platform.key).synchronize({ platform, recentPostsOnly })
                )
            );
            await this._clearPlatformsPosts(mappedPostsPerPlatform);

            logger.info('[SYNCHRONIZE_RESTAURANT_POSTS] upserting posts', { restaurantId });

            const upserts = mappedPostsPerPlatform
                .map((platformPosts) =>
                    platformPosts.map(
                        (post): Promise<IPost> =>
                            this._postsRepository.upsert({
                                filter: { socialId: post.socialId, platformId: post.platformId },
                                update: post,
                            })
                    )
                )
                .flat();

            await Promise.all(upserts);
            logger.info('[SYNCHRONIZE_RESTAURANT_POSTS] done', { restaurantId });
        } catch (error) {
            logger.error('[SynchronizeRecentPostsUseCase] Error', { restaurantId: restaurantId, error });
        } finally {
            await this._restaurantsRepository.findOneAndUpdate({
                filter: { _id: restaurantId },
                update: {
                    postsLastUpdate: new Date(),
                    ...(options.recentPostsOnly ? {} : { allPostsLastSync: new Date() }),
                },
            });
        }
    }

    /**
     * Deletes all posts in the database that are not fetched by synchronization
     */
    private async _clearPlatformsPosts(mappedPostsPerPlatform: MalouPostData[][]): Promise<void> {
        const nonEmptyPlatformPosts = mappedPostsPerPlatform.filter((platformPosts) => !!platformPosts?.length);
        for (const platformPosts of nonEmptyPlatformPosts) {
            const firstMappedPlatformPost = platformPosts[platformPosts.length - 1];
            const filter = {
                restaurantId: firstMappedPlatformPost.restaurantId,
                key: firstMappedPlatformPost.key,
                published: PostPublicationStatus.PUBLISHED,
                socialCreatedAt: {
                    $gte: firstMappedPlatformPost.socialCreatedAt,
                },
                isStory: { $ne: true }, // Stories are available on Instagram and Facebook only for a limited time so we don't delete them when synchronizing
                socialId: {
                    $nin: platformPosts.map((post) => post.socialId),
                },
            };
            await this._deleteManyPostsAndHandleSideEffects(filter);
        }
    }

    private async _deleteManyPostsAndHandleSideEffects(filter: FilterQuery<IPost>): Promise<any> {
        const posts = await this._postsRepository.find({
            filter,
            options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }, { path: 'feedback' }] },
        });
        logger.info('[DELETE_MANY_POSTS_AND_HANDLE_SIDE_EFFECTS] Start deleting posts', { postIds: posts.map((p) => p._id) });

        await this._postsRepository.deleteMany({ filter });

        const commentsFilter = { postSocialId: { $in: posts.map(({ socialId }) => socialId) } };
        return this._commentsRepository.deleteMany({ filter: commentsFilter });
    }
}
