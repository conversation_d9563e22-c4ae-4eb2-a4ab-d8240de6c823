import fs from 'fs';
import { container } from 'tsyringe';

import { IMedia } from '@malou-io/package-models';
import { FileFormat, MediaCategory, MediaType, PostSource } from '@malou-io/package-utils';

import SharpProvider from ':helpers/providers/sharp.provider';
import { Media } from ':modules/media/entities/media.entity';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';

import { seedWithDefaultIntegrationsSetup } from '../../tests/integrations/setups/seeds/default-integrations-seeds';
import { MediasRepository } from '../media/medias.repository';
import { MediasUseCases } from '../media/medias.use-cases';
import { FileMetadata } from '../media/services/image-resizers/image-resizer.port.interface';
import PostsRepository from './posts.repository';
import PostsUseCases from './posts.use-cases';

type TestCase = {
    medias: Media[];
    expectedResult: FileMetadata[];
};

const testCases: TestCase[] = [
    {
        medias: [
            new Media({
                id: '653647ea082f3c91ae973b1d',
                restaurantId: '6268039d8de2974d89ebd445',
                userId: '632c7d4d39292fe0065321be',
                title: 'bar climatisé - bar - café romantique - churrasqueira sainmar',
                name: undefined,
                category: MediaCategory.ADDITIONAL,
                format: FileFormat.JPEG,
                type: MediaType.PHOTO,
                urls: {
                    original:
                        'https://malou-dev.s3.eu-west-3.amazonaws.com/restaurants/6268039d8de2974d89ebd445/media/2032355d-2364-448f-9083-c4c4e1d4cab9/original.jpeg',
                    small: 'https://malou-dev.s3.eu-west-3.amazonaws.com/restaurants/6268039d8de2974d89ebd445/media/2032355d-2364-448f-9083-c4c4e1d4cab9/small.jpeg',
                    igFit: 'https://malou-dev.s3.eu-west-3.amazonaws.com/restaurants/6268039d8de2974d89ebd445/media/2032355d-2364-448f-9083-c4c4e1d4cab9/igfit.jpeg',
                },
                sizes: {
                    original: 67782,
                    small: 22663,
                    igFit: 71764,
                },
                postIds: ['651e8792e9c8db697023e9db', '651e8793e9c8db697023e9df'],
                socialId: '653647ea082f3c91ae973b1d',
                createdAt: new Date('2023-07-26T15:54:58.907Z'),
                updatedAt: new Date('2023-10-23T10:16:26.759Z'),
                dimensions: {
                    original: {
                        width: 638,
                        height: 638,
                    },
                    small: {
                        width: 300,
                        height: 0,
                    },
                    igFit: {
                        width: 638,
                        height: 0,
                    },
                },
                duration: undefined,
                resizeMetadata: {
                    aspectRatio: 1,
                    cropPosition: {
                        left: 0,
                        top: 0,
                    },
                    width: 637,
                    height: 637,
                },
            }),
            new Media({
                id: '653647ec082f3c91ae973b1f',
                restaurantId: '6268039d8de2974d89ebd445',
                userId: undefined,
                title: undefined,
                name: undefined,
                category: MediaCategory.ADDITIONAL,
                format: FileFormat.JPEG,
                type: MediaType.PHOTO,
                urls: {
                    original:
                        'https://malou-dev.s3.eu-west-3.amazonaws.com/restaurants/6268039d8de2974d89ebd445/media/4976b18d-38d7-487e-aa1a-53909e3b5021/original.jpeg',
                    small: 'https://malou-dev.s3.eu-west-3.amazonaws.com/restaurants/6268039d8de2974d89ebd445/media/4976b18d-38d7-487e-aa1a-53909e3b5021/small.jpeg',
                    igFit: 'https://malou-dev.s3.eu-west-3.amazonaws.com/restaurants/6268039d8de2974d89ebd445/media/4976b18d-38d7-487e-aa1a-53909e3b5021/igfit.jpeg',
                },
                sizes: {
                    original: 115233,
                    small: 18095,
                    igFit: 121738,
                },
                postIds: [],
                socialId: '653647ec082f3c91ae973b1f',
                createdAt: new Date('2023-10-06T10:08:01.540Z'),
                updatedAt: new Date('2023-10-23T10:16:26.759Z'),
                dimensions: {
                    original: {
                        width: 1080,
                        height: 1080,
                    },
                    small: {
                        width: 300,
                        height: 0,
                    },
                    igFit: {
                        width: 1080,
                        height: 0,
                    },
                },
                duration: undefined,
                resizeMetadata: {
                    aspectRatio: 1,
                    cropPosition: {
                        left: 0,
                        top: 0,
                    },
                    width: 0,
                    height: 0,
                },
            }),
        ],
        expectedResult: [
            {
                format: 'jpeg',
                height: 637,
                width: 637,
                size: 0,
                mimetype: 'image/jpeg',
                name: '653647ea082f3c91ae973b1d_resized_fitted',
                pathWhereFileIsStored: 'downloadedMedias/653647ea082f3c91ae973b1d_resized_fitted.jpeg',
                type: MediaType.PHOTO,
            },
            {
                format: 'jpeg',
                height: 1080,
                width: 1080,
                size: 0,
                mimetype: 'image/jpeg',
                name: '653647ec082f3c91ae973b1f_resized_fitted',
                pathWhereFileIsStored: 'downloadedMedias/653647ec082f3c91ae973b1f_resized_fitted.jpeg',
                type: MediaType.PHOTO,
            },
            // i commented this test case because at this time i don't know how to test the video with the webhook and the cache, and i don't have time to dive into it
            // {
            //     format: 'mov',
            //     height: 1080,
            //     width: 1080,
            //     size: 2531831,
            //     mimetype: 'video/quicktime',
            //     name: '653647fa082f3c91ae973b21',
            //     pathWhereFileIsStored: 'downloadedMedias/653647fa082f3c91ae973b21_resized.mov',
            //     type: MediaType.VIDEO,
            // },
        ],
    },
];

class MockMediasUseCases extends MediasUseCases {
    constructor(
        mediasRepository?: MediasRepository,
        postsRepository?: PostsRepository,
        mediaUploaderService?: MediaUploaderService,
        sharpProvider?: SharpProvider
    ) {
        super(mediasRepository!, postsRepository!, sharpProvider!, mediaUploaderService!);
    }

    resizeAttachmentsAndCreateNewPostMedias(resizedMediaMetadata) {
        return Promise.resolve(resizedMediaMetadata as unknown as IMedia);
    }
}

beforeAll(async () => {
    await seedWithDefaultIntegrationsSetup({
        mockPassportWithDefaultUser: true,
    });
});

describe.each(testCases)('PostsUseCases integration tests', ({ medias, expectedResult }) => {
    let postsUseCases: PostsUseCases;
    beforeAll(() => {
        container.registerInstance(MediasUseCases, new MockMediasUseCases() as unknown as MediasUseCases);
        postsUseCases = container.resolve(PostsUseCases);
    });

    describe('resizeAttachmentsAndCreateNewPostMedias', () => {
        it('should resize attachments and create new post medias', async () => {
            const result = (await postsUseCases.resizeAttachmentsAndCreateNewPostMedias(
                medias,
                PostSource.SOCIAL
            )) as unknown as FileMetadata[];
            // remove all medias from donwloadedMedias folder
            const files = fs.readdirSync('downloadedMedias');
            for (const file of files.filter((f) => f !== '.keep')) {
                fs.unlinkSync(`downloadedMedias/${file}`);
            }
            expect(
                result.map((r: FileMetadata) => ({
                    mimetype: r.mimetype,
                    name: r.name,
                    pathWhereFileIsStored: r.pathWhereFileIsStored,
                    type: r.type,
                    width: r.width,
                    size: r.size,
                    height: r.height,
                    format: r.format,
                }))
            ).toEqual(expectedResult);
        });
    });
});
