import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPost } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FacebookPostMapper } from ':modules/posts/platforms/facebook/facebook-post-mapper';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { IPlatformPostService } from ':modules/posts/platforms/platform-post.service.interface';
import { MalouPostData } from ':modules/posts/posts.interface';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import { MetaGraphApiHelperErrorObject } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class FacebookPostService implements IPlatformPostService {
    constructor(
        private readonly _facebookPostMapper: FacebookPostMapper,
        private readonly _metaGraphApiHelper: MetaGraphApiHelper,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async fetchPost({ post }: { post: IPost }): Promise<MalouPostData> {
        assert(post.restaurantId, 'Missing restaurantId on post');
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
            post.restaurantId.toString(),
            PlatformKey.FACEBOOK
        );
        if (!platform || !platform.id) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    postSocialId: post.socialId,
                    restaurantId: post.restaurantId.toString(),
                    platformKey: PlatformKey.FACEBOOK,
                },
            });
        }
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(platform.socialId, 'Missing socialId on platform');
        assert(post.socialId, 'Missing socialId on post');
        const fbPostResult =
            post.postType === PostType.REEL
                ? await this._metaGraphApiHelper.fetchFacebookReel(credentialId, post.socialId, platform.socialId)
                : await this._metaGraphApiHelper.fetchFacebookPost(credentialId, post.socialId, platform.socialId);
        if (fbPostResult.isErr()) {
            logger.error('FacebookPostService fetchPost', { error: fbPostResult.error });
            throw new MalouError(MalouErrorCode.FACEBOOK_API_EXCEPTION, {
                message: 'FacebookPostService fetchPost',
                metadata: { error: fbPostResult.error },
            });
        }
        return post.postType === PostType.REEL
            ? this._facebookPostMapper.mapPublishedFbReelToMalouPost(fbPostResult.value as FacebookApiTypes.Reels.GetReelResponse, {
                  restaurantId: post.restaurantId.toString(),
                  platformId: platform.id,
              })
            : this._facebookPostMapper.mapToMalouPost({ post: fbPostResult.value, platform });
    }

    async fetchPostFromSocialId(socialId: string, pageId: string, platforms: Platform[]): Promise<FbPostData | undefined> {
        if (platforms.length === 0) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    postSocialId: socialId,
                    platformKey: PlatformKey.FACEBOOK,
                },
            });
        }
        let fbPost: FbPostData | undefined;
        let error: MetaGraphApiHelperErrorObject | undefined;

        for (const platform of platforms) {
            const credentialId = platform.credentials?.[0];
            if (!credentialId || !platform.socialId) {
                continue;
            }
            const fbPostResult = await this._metaGraphApiHelper.getFbPagePostWithCommentsAndInsights(credentialId, pageId, socialId);
            if (fbPostResult.isErr()) {
                logger.error('FacebookPostService fetchPostFromSocialId', { error: fbPostResult.error });
                error = fbPostResult.error;
                continue;
            }
            fbPost = fbPostResult.value;
            break;
        }

        if (!fbPost) {
            logger.error('FacebookPostService fetchPostFromSocialId', { error });
        }

        return fbPost;
    }
}
