import { singleton } from 'tsyringe';

import {
    CreateScanBodyDto,
    NfcSnapshotDto,
    PatchScanBodyDto,
    ScanDto,
    ScanForAggregatedInsightsDto,
    ScanForRestaurantInsightsDto,
    ScanWithNfcDto,
    SearchScanQueryDto,
} from '@malou-io/package-dto';
import {
    IScan,
    IScanForAggregatedInsights,
    IScanForRestaurantInsightsWithReviewRating,
    IScanWithNfc,
    ITotem,
    toDbId,
} from '@malou-io/package-models';
import { FAKE_NFC_ID_FOR_WHEEL_OF_FORTUNE_SCANS, NfcType, WheelOfFortuneRedirectionPlatformKey } from '@malou-io/package-utils';

import { NfcsDtoMapper } from '../nfc/nfcs.mapper.dto';
import { CreateScan, PatchScan, ScanSearchFilters } from './scans.interface';

@singleton()
export class ScansDtoMapper {
    constructor(private readonly _nfcsDtoMapper: NfcsDtoMapper) {}

    toScanSearchFilters(dto: SearchScanQueryDto): ScanSearchFilters {
        return {
            nfcIds: dto.nfcIds,
            startScannedAt: dto.startScannedAt ? new Date(dto.startScannedAt) : undefined,
            endScannedAt: dto.endScannedAt ? new Date(dto.endScannedAt) : undefined,
            restaurantIds: dto.restaurantIds ? dto.restaurantIds.map((restaurantId) => toDbId(restaurantId)) : undefined,
            ledToPublishedReview: dto.ledToPublishedReview,
        };
    }

    toCreateScan(dto: CreateScanBodyDto): CreateScan {
        return {
            nfcId: toDbId(dto.nfcId),
            scannedAt: new Date(dto.scannedAt),
            nfcSnapshot: this._toNfcSnapshot(dto.nfcSnapshot),
            redirectedAt: dto.redirectedAt ? new Date(dto.redirectedAt) : undefined,
            starClicked: dto.starClicked,
        };
    }

    toPatchScan(dto: PatchScanBodyDto): PatchScan {
        return {
            nfcId: dto.nfcId ? toDbId(dto.nfcId) : undefined,
            scannedAt: dto.scannedAt ? new Date(dto.scannedAt) : undefined,
            nfcSnapshot: dto.nfcSnapshot ? this._toNfcSnapshot(dto.nfcSnapshot) : undefined,
            redirectedAt: dto.redirectedAt ? new Date(dto.redirectedAt) : undefined,
            starClicked: dto.starClicked,
        };
    }

    toScanDto(model: IScan): ScanDto {
        return {
            id: model._id.toString(),
            nfcId: model.nfcId.toString(),
            scannedAt: model.scannedAt.toISOString(),
            nfcSnapshot: model.nfcSnapshot ? this._toNfcSnapshotDto(model.nfcSnapshot) : undefined,
            redirectedAt: model.redirectedAt?.toISOString(),
            starClicked: model.starClicked,
            matchedReviewSocialId: model.matchedReviewSocialId,
            createdAt: model.createdAt.toISOString(),
            updatedAt: model.updatedAt.toISOString(),
        };
    }

    toScanWithNfcDto(scan: IScanWithNfc): ScanWithNfcDto {
        return {
            ...this.toScanDto(scan),
            nfc: scan.nfc ? this._nfcsDtoMapper.toNfcDto(scan.nfc) : undefined,
        };
    }

    toScanForAggregatedInsightsDto(
        scan: IScanForAggregatedInsights
    ): ScanForAggregatedInsightsDto['scans'][0] & { matchedReviewSocialId?: string | null } {
        return {
            id: scan._id.toString(),
            nfcId: scan.nfcId.toString(),
            matchedReviewSocialId: scan.matchedReviewSocialId ?? null,
            nfcSnapshot: {
                restaurantId: scan.nfcSnapshot.restaurantId.toString(),
                chipName: scan.nfcSnapshot.chipName,
                platformKey: scan.nfcSnapshot.platformKey,
                name: scan.nfcSnapshot.name,
                isRedirectingToWof: this._isWheelOfFortuneRelated({
                    nfcId: scan.nfcId.toString(),
                    redirectionLink: scan.nfcSnapshot.redirectionLink,
                    platformKey: scan.nfcSnapshot.platformKey,
                }),
            },
        };
    }

    toScanForRestaurantInsightsDto(scan: IScanForRestaurantInsightsWithReviewRating): ScanForRestaurantInsightsDto['scans'][0] {
        return {
            id: scan._id.toString(),
            nfcId: scan.nfcId.toString(),
            scannedAt: scan.scannedAt.toISOString(),
            matchedReview: scan.matchedReview
                ? {
                      id: scan.matchedReview._id.toString(),
                      rating: scan.matchedReview.rating ?? undefined,
                  }
                : null,
            starClicked: scan.starClicked,
            nfcSnapshot: {
                restaurantId: scan.nfcSnapshot.restaurantId.toString(),
                chipName: scan.nfcSnapshot.chipName,
                platformKey: scan.nfcSnapshot.platformKey,
                name: scan.nfcSnapshot.name,
                isRedirectingToWof: this._isWheelOfFortuneRelated({
                    nfcId: scan.nfcId.toString(),
                    redirectionLink: scan.nfcSnapshot.redirectionLink,
                    platformKey: scan.nfcSnapshot.platformKey,
                }),
            },
        };
    }

    _toNfcSnapshot(dto: CreateScanBodyDto['nfcSnapshot']): IScan['nfcSnapshot'] {
        return {
            _id: toDbId(dto.id),
            chipName: dto.chipName,
            restaurantId: toDbId(dto.restaurantId),
            active: dto.active,
            name: dto.name ?? undefined,
            platformKey: dto.platformKey,
            redirectionLink: dto.redirectionLink,
            notes: dto.notes,
            starsRedirected: dto.starsRedirected,
            type: dto.type,
            createdAt: new Date(dto.createdAt),
            updatedAt: new Date(dto.updatedAt),
        };
    }

    _toNfcSnapshotDto(nfc: IScan['nfcSnapshot']): NfcSnapshotDto {
        return {
            id: nfc._id.toString(),
            restaurantId: nfc.restaurantId.toString(),
            active: nfc.active,
            platformKey: nfc.platformKey,
            redirectionLink: nfc.redirectionLink,
            chipName: nfc.chipName ?? undefined,
            name: this._isTotem(nfc) ? nfc.name : undefined,
            notes: nfc.notes,
            starsRedirected: nfc.starsRedirected,
            type: nfc.type,
            createdAt: nfc.createdAt.toISOString(),
            updatedAt: nfc.updatedAt.toISOString(),
        };
    }

    private _isTotem(nfc: IScan['nfcSnapshot']): nfc is ITotem {
        return nfc.type === NfcType.TOTEM;
    }

    private _isWheelOfFortuneRelated({
        nfcId,
        redirectionLink,
        platformKey,
    }: {
        nfcId: string;
        redirectionLink: string | null;
        platformKey: IScan['nfcSnapshot']['platformKey'];
    }): boolean {
        return (
            platformKey === WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION ||
            nfcId === FAKE_NFC_ID_FOR_WHEEL_OF_FORTUNE_SCANS ||
            (redirectionLink ? !!redirectionLink.match(/wheel-of-fortune/) : false)
        );
    }
}
