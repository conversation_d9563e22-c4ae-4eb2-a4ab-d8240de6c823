export const lookupMatchedReviewStage = [
    {
        $lookup: {
            from: 'reviews',
            let: {
                socialId: '$matchedReviewSocialId',
                restaurantId: '$nfcSnapshot.restaurantId',
            },
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $and: [{ $eq: ['$socialId', '$$socialId'] }, { $eq: ['$restaurantId', '$$restaurantId'] }],
                        },
                    },
                },
            ],
            as: 'matchedReview',
        },
    },
    {
        $unwind: { path: '$matchedReview', preserveNullAndEmptyArrays: true },
    },
];
