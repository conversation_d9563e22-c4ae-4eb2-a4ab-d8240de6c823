import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { WheelOfFortuneRedirectionPlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { MOCKED_NOW_DATE_TIME } from ':modules/roi/tests/roi.constants';
import { getDefaultNfcSnapshot, getDefaultScan } from ':modules/scans/tests/scans.builder';
import { GetScanInsightsForRestaurantUseCase } from ':modules/scans/use-cases/get-scan-insights-for-restaurant/get-scan-insights-for-restaurant.use-case';

describe('GetScanInsightsForRestaurantUseCase', () => {
    let restaurantId1: DbId;
    let restaurantId2: DbId;
    let reviewId1: DbId;
    let reviewId2: DbId;
    let nfcId1: DbId;
    let nfcId2: DbId;
    let nfcId3: DbId;
    let reviewSocialId1: string;
    let reviewSocialId2: string;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'NfcsRepository', 'ScansRepository', 'ReviewsRepository']);
        const mockDateNow = jest.fn(() => MOCKED_NOW_DATE_TIME);
        global.Date.now = mockDateNow;
    });

    beforeEach(() => {
        restaurantId1 = newDbId();
        restaurantId2 = newDbId();
        reviewId1 = newDbId();
        reviewId2 = newDbId();
        nfcId1 = newDbId();
        nfcId2 = newDbId();
        nfcId3 = newDbId();
        reviewSocialId1 = 'review-social-id';
        reviewSocialId2 = 'review-social-id-2';
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return correct data format', async () => {
        const getScanInsightsForRestaurantUseCase = container.resolve(GetScanInsightsForRestaurantUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'scans'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()._id(reviewId1).restaurantId(restaurantId1).socialId(reviewSocialId1).rating(5).build(),
                            getDefaultReview()._id(reviewId2).restaurantId(restaurantId1).socialId(reviewSocialId2).rating(4).build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .matchedReviewSocialId(reviewSocialId1)
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .matchedReviewSocialId(reviewSocialId1)
                                .scannedAt(DateTime.now().minus({ month: 1, days: 15 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsForRestaurantUseCase.execute(restaurantId1.toString(), {
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),
            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
        });

        expect(result).toMatchObject({
            scans: expect.arrayContaining([
                expect.objectContaining({
                    scannedAt: expect.any(String),
                    starClicked: expect.any(Number),
                    nfcSnapshot: expect.objectContaining({
                        restaurantId: expect.any(String),
                        chipName: expect.any(String),
                        platformKey: expect.any(String),
                        name: expect.any(String),
                        isRedirectingToWof: expect.any(Boolean),
                    }),
                    matchedReview: expect.objectContaining({
                        id: expect.any(String),
                        rating: expect.any(Number),
                    }),
                }),
            ]),
            previousScans: expect.arrayContaining([
                expect.objectContaining({
                    scannedAt: expect.any(String),
                    starClicked: expect.any(Number),
                    nfcSnapshot: expect.objectContaining({
                        restaurantId: expect.any(String),
                        chipName: expect.any(String),
                        platformKey: expect.any(String),
                        name: expect.any(String),
                        isRedirectingToWof: expect.any(Boolean),
                    }),
                    matchedReview: expect.objectContaining({
                        id: expect.any(String),
                        rating: expect.any(Number),
                    }),
                }),
            ]),
        });
    });

    it('should not contain wheel of fortune scans', async () => {
        const getScanInsightsForRestaurantUseCase = container.resolve(GetScanInsightsForRestaurantUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'scans'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()._id(reviewId1).socialId(reviewSocialId1).rating(5).build(),
                            getDefaultReview()._id(reviewId2).socialId(reviewSocialId2).rating(4).build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId1)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsForRestaurantUseCase.execute(restaurantId1.toString(), {
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),

            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
        });

        expect(result.scans.length).toBe(2);
        expect(result.previousScans.length).toBe(0);
    });

    it('should return previous period scans', async () => {
        const getScanInsightsForRestaurantUseCase = container.resolve(GetScanInsightsForRestaurantUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'scans'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()._id(reviewId1).restaurantId(restaurantId1).socialId(reviewSocialId1).rating(5).build(),
                            getDefaultReview()._id(reviewId2).restaurantId(restaurantId1).socialId(reviewSocialId2).rating(4).build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId1)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .matchedReviewSocialId('review-social-id-3')
                                .build(),
                            getDefaultScan()
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),

                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 15 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId1)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 5 }).toJSDate())
                                .matchedReviewSocialId('review-social-id-3')
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsForRestaurantUseCase.execute(restaurantId1.toString(), {
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),
            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
        });

        expect(result.scans.length).toBe(2);
        expect(result.previousScans.length).toBe(4);
        const nfc1scan = result.previousScans.find((scan) => scan.nfcId === nfcId1.toString());
        expect(nfc1scan?.matchedReview).toMatchObject({
            id: reviewId1.toString(),
            rating: 5,
        });
        expect(result.previousScans.filter((scan) => scan.matchedReview === null).length).toBe(1);
    });

    it('should return WoF previous period scans', async () => {
        const getScanInsightsForRestaurantUseCase = container.resolve(GetScanInsightsForRestaurantUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'scans'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()._id(reviewId1).socialId(reviewSocialId1).rating(5).build(),
                            getDefaultReview()._id(reviewId2).socialId(reviewSocialId2).rating(4).build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId1)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId1)
                                        .build()
                                )
                                .matchedReviewSocialId('review-social-id-3')
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3).redirectionLink('wheel-of-fortune').restaurantId(restaurantId2).build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId1)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),

                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 15 }).toJSDate())
                                .matchedReviewSocialId(reviewSocialId1)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 5 }).toJSDate())
                                .matchedReviewSocialId('review-social-id-3')
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3).redirectionLink('wheel-of-fortune').restaurantId(restaurantId1).build()
                                )
                                .scannedAt(DateTime.now().minus({ months: 1, days: 5 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsForRestaurantUseCase.execute(restaurantId1.toString(), {
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),

            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
        });

        expect(result.scans.length).toBe(3);
        expect(result.previousScans.length).toBe(5);
    });
});
