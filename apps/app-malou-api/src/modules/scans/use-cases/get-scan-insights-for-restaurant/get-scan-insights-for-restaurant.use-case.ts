import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { GetScanInsightsBodyDto, GetScanInsightsForRestaurantBodyDto, ScanForRestaurantInsightsDto } from '@malou-io/package-dto';

import ScansRepository from ':modules/scans/repository/scans.repository';
import { ScansDtoMapper } from ':modules/scans/scans.dto-mapper';

@singleton()
export class GetScanInsightsForRestaurantUseCase {
    constructor(
        private readonly _scansRepository: ScansRepository,
        private readonly _scansDtoMapper: ScansDtoMapper
    ) {}

    async execute(restaurantId: string, params: GetScanInsightsForRestaurantBodyDto): Promise<ScanForRestaurantInsightsDto> {
        const { previousPeriod, currentPeriod } = this._getInsightsPeriod({
            startScannedAt: params.startScannedAt,
            endScannedAt: params.endScannedAt,
        });

        const promises = [
            this._scansRepository.getScansForRestaurantInsights(restaurantId, {
                ...params,
                startScannedAt: currentPeriod.startScannedAt,
                endScannedAt: currentPeriod.endScannedAt,
            }),
            this._scansRepository.getScansForRestaurantInsights(restaurantId, {
                ...params,
                startScannedAt: previousPeriod.startScannedAt,
                endScannedAt: previousPeriod.endScannedAt,
            }),
        ];

        const result = await Promise.all(promises);

        const scans = result[0]?.map((s) => this._scansDtoMapper.toScanForRestaurantInsightsDto(s)) ?? [];
        const previousScans = result[1]?.map((s) => this._scansDtoMapper.toScanForRestaurantInsightsDto(s)) ?? [];

        return {
            previousScans,
            scans,
        };
    }

    private _getInsightsPeriod({ startScannedAt, endScannedAt }: Pick<GetScanInsightsBodyDto, 'startScannedAt' | 'endScannedAt'>): {
        previousPeriod: Pick<GetScanInsightsBodyDto, 'startScannedAt' | 'endScannedAt'>;
        currentPeriod: Pick<GetScanInsightsBodyDto, 'startScannedAt' | 'endScannedAt'>;
    } {
        const startDate = new Date(startScannedAt);
        const endDate = new Date(endScannedAt);
        const difference = DateTime.fromJSDate(endDate).diff(DateTime.fromJSDate(startDate)).toObject();
        const previousStartDate = DateTime.fromJSDate(startDate).minus(difference).toJSDate();
        return {
            previousPeriod: {
                startScannedAt: previousStartDate.toISOString(),
                endScannedAt: startDate.toISOString(),
            },
            currentPeriod: {
                startScannedAt,
                endScannedAt,
            },
        };
    }
}
