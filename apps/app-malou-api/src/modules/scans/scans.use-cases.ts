import { singleton } from 'tsyringe';

import { IScan, IScanWithReview } from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { lookupMatchedReviewStage } from ':modules/scans/repository/scans.pipelines';

import ScansRepository from './repository/scans.repository';
import { ScansDtoMapper } from './scans.dto-mapper';
import { CreateScan, PatchScan, ScanSearchFilters } from './scans.interface';

@singleton()
export class ScansUseCases {
    constructor(
        private _scansRepository: ScansRepository,
        private _scansDtoMapper: ScansDtoMapper
    ) {}

    async searchScan(searchFilters: ScanSearchFilters): Promise<IScanWithReview[]> {
        const andFilters: any[] = [];
        if (searchFilters.nfcIds) {
            andFilters.push({ nfcId: { $in: searchFilters.nfcIds } });
        }
        if (searchFilters.startScannedAt) {
            andFilters.push({ scannedAt: { $gte: searchFilters.startScannedAt } });
        }
        if (searchFilters.endScannedAt) {
            andFilters.push({ scannedAt: { $lte: searchFilters.endScannedAt } });
        }
        if (searchFilters.restaurantIds) {
            andFilters.push({ 'nfcSnapshot.restaurantId': { $in: searchFilters.restaurantIds } });
        }
        if (searchFilters.ledToPublishedReview) {
            andFilters.push({ matchedReviewSocialId: { $ne: null } });
        }
        if (!andFilters.length) {
            throw new MalouError(MalouErrorCode.NO_FILTER_PROVIDED, { message: 'No filters provided for scans search' });
        }
        const matchStage = {
            $match: {
                $and: andFilters,
            },
        };
        const res = await this._scansRepository.aggregate([matchStage, ...lookupMatchedReviewStage]);

        return res;
    }

    async createScan(data: CreateScan): Promise<IScan> {
        return this._scansRepository.create({
            data,
        });
    }

    async patchScan(id: string, data: PatchScan): Promise<IScan | null> {
        return this._scansRepository.findOneAndUpdate({ filter: { _id: id }, update: data, options: { lean: true } });
    }

    async getScansThatLedToReviews({
        startScannedAt,
        endScannedAt,
        restaurantIds,
    }: {
        startScannedAt: string;
        endScannedAt: string;
        restaurantIds: string[];
    }) {
        const scanSearchFilters = this._scansDtoMapper.toScanSearchFilters({
            startScannedAt,
            endScannedAt,
            restaurantIds,
            ledToPublishedReview: true,
        });
        return this.searchScan(scanSearchFilters);
    }
}
