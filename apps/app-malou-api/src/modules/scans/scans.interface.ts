import { DbId, IScan } from '@malou-io/package-models';

export interface ScanSearchFilters {
    nfcIds?: string[];
    startScannedAt?: Date;
    endScannedAt?: Date;
    restaurantIds?: DbId[];
    ledToPublishedReview?: boolean;
}

export type CreateScan = Omit<IScan, '_id' | 'createdAt' | 'updatedAt'>;

export type PatchScan = Partial<CreateScan>;

export type ScanWithProjection = Pick<IScan, '_id' | 'redirectedAt' | 'nfcSnapshot' | 'starClicked' | 'matchedReviewSocialId'>;
