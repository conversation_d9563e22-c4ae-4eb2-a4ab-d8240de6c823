import 'reflect-metadata';

import ':env';

import parse from 'csv-parse/lib/sync';
import { createObjectCsvWriter } from 'csv-writer';
import fs from 'fs';
import { max, mean, min } from 'lodash';

import { UserWheelOfFortuneStatus } from '@malou-io/package-dto';
import {
    GiftDrawModel,
    IRestaurant,
    PlatformInsightModel,
    PlatformModel,
    RestaurantModel,
    ScanModel,
    UserModel,
    UserRestaurantModel,
    WheelOfFortuneModel,
} from '@malou-io/package-models';
import { PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { WheelOfFortune as WheelOfFortuneEntity } from ':modules/wheels-of-fortune/wheel-of-fortune.entity';
import ':plugins/db';

interface FileRow {
    userEmail: string;
    hasAtLeastOneActiveOrProgrammedWheel: boolean;
    latestEndDateForActiveWheels: string;
    hasCreatedAWheel: boolean;
    firstWheelCreatedAt: string;
    playersNb: number;
    retrievedGiftsNb: number;
    reviewsNb: number;
    gmbRating: number;
}

interface AirtableRow {
    Name: string;
    email: string;
    venues: string;
    main_abonnement: string;
    nb_of_venues: string;
}

async function getWheelOfFortuneStatusForUser(restaurants: IRestaurant[]): Promise<UserWheelOfFortuneStatus> {
    const userRestaurantIds = restaurants.map((restaurant) => restaurant._id);

    const restaurantsWheels = await WheelOfFortuneModel.find({ restaurantId: { $in: userRestaurantIds } });
    const aggregatedRestaurantWheels = await WheelOfFortuneModel.find({
        _id: { $in: restaurantsWheels.map((wheel: any) => wheel.aggregatedWheelOfFortuneId).filter(Boolean) },
    });

    const allRestaurantWheels = [...restaurantsWheels, ...aggregatedRestaurantWheels];

    const hasCreatedAWheel = !!allRestaurantWheels.length;
    const minWofCreatedAt: number | undefined = hasCreatedAWheel
        ? min(allRestaurantWheels.map((wheel) => new Date(wheel.createdAt).getTime()))
        : undefined;

    const activeOrProgrammedWheels = allRestaurantWheels.filter((wheel) => !!new WheelOfFortuneEntity(wheel).isActiveOrProgrammed());
    const hasAtLeastOneActiveOrProgrammedWheel = !!activeOrProgrammedWheels.length;
    const in100Years = new Date('12/12/2124');
    const maxActiveWofEndAt: number | undefined = activeOrProgrammedWheels.length
        ? max(activeOrProgrammedWheels.map((wheel) => (wheel.endDate ? new Date(wheel.endDate).getTime() : in100Years.getTime())))
        : undefined;

    return {
        hasAtLeastOneActiveOrProgrammedWheel,
        hasCreatedAWheel,
        firstWheelCreatedAt: minWofCreatedAt ? new Date(minWofCreatedAt) : null,
        latestEndDateForActiveWheels: maxActiveWofEndAt ? new Date(maxActiveWofEndAt) : null,
    };
}

async function handleWriteFile(data: FileRow[]): Promise<void> {
    try {
        const csvWriter = createObjectCsvWriter({
            path: `${__dirname}/users-with-wof-${process.env.NODE_ENV}.csv`,
            header: [
                { id: 'userEmail', title: 'email' },
                { id: 'hasCreatedAWheel', title: 'hasCreatedAWheel' },
                { id: 'firstWheelCreatedAt', title: 'firstWheelCreatedAt' },
                { id: 'hasAtLeastOneActiveOrProgrammedWheel', title: 'hasAtLeastOneActiveOrProgrammedWheel' },
                { id: 'latestEndDateForActiveWheels', title: 'latestEndDateForActiveWheels' },
                { id: 'playersNb', title: 'playersNb' },
                { id: 'retrievedGiftsNb', title: 'retrievedGiftsNb' },
                { id: 'reviewsNb', title: 'reviewsNb' },
                { id: 'gmbRating', title: 'gmbRating' },
                { id: 'mainSubscriptionPlan', title: 'mainSubscriptionPlan' },
                { id: 'venuesNb', title: 'venuesNumber' },
                { id: 'venues', title: 'venues' },
                { id: 'userName', title: 'userName' },
            ],
        });
        await csvWriter.writeRecords(data);
    } catch (e) {
        console.log(`Error writing in file: ${JSON.stringify(e)}`);
    }
}

async function readCsvFile(path: string): Promise<AirtableRow[]> {
    try {
        const binary = fs.readFileSync(path);
        return parse(binary, {
            columns: true,
            skipEmptyLines: true,
            relax: true,
            relax_column_count: true,
            skip_lines_with_empty_values: true,
            skip_empty_lines: true,
            delimiter: [',', ';'],
            skip_lines_with_error: true,
        });
    } catch (err) {
        console.error(`Error finding or reading file ${err}`);
        return [];
    }
}

async function main() {
    const airtableData: AirtableRow[] = await readCsvFile('src/tasks/wheels-of-fortune/airtable-data.csv');

    const users = await UserModel.find({ verified: true }, { _id: 1, email: 1 });
    const usersData: FileRow[] = [];

    for (const user of users) {
        const userRestaurants = await UserRestaurantModel.find({ userId: user._id }, { restaurantId: 1 });
        const userRestaurantIds = userRestaurants.map((userRestaurant) => userRestaurant.restaurantId);
        const restaurants = await RestaurantModel.find({ _id: { $in: userRestaurantIds }, active: true }, { active: 1 });

        if (!restaurants.length) {
            continue;
        }

        const {
            hasAtLeastOneActiveOrProgrammedWheel,
            hasCreatedAWheel,
            firstWheelCreatedAt,
            latestEndDateForActiveWheels,
        }: UserWheelOfFortuneStatus = await getWheelOfFortuneStatusForUser(restaurants);

        const giftDraws = await GiftDrawModel.find({ restaurantId: { $in: userRestaurantIds } }, { retrievedAt: 1 });
        const giftsRetrieved = giftDraws.filter((giftDraw) => !!giftDraw.retrievedAt);

        const wheelReviews = await ScanModel.find({
            'nfcSnapshot.restaurantId': { $in: userRestaurantIds },
            matchedReviewSocialId: { $ne: null },
            scannedAt: { $gte: firstWheelCreatedAt },
            'nfcSnapshot.redirectionLink': /wheel-of-fortune/,
        });

        const gmbPlatforms = await PlatformModel.find({ key: PlatformKey.GMB, restaurantId: { $in: userRestaurantIds } });
        let gmbRating;
        if (gmbPlatforms) {
            const gmbRatings = await Promise.all(
                gmbPlatforms.map((platform) =>
                    PlatformInsightModel.find(
                        {
                            socialId: platform.socialId,
                            metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                        },
                        { sort: { createdAt: -1 }, limit: 1, value: 1 }
                    )
                )
            );
            gmbRating = mean(gmbRatings.map((gmbInsights) => gmbInsights[0]?.value));
        }

        const userData = {
            userEmail: user.email,
            hasCreatedAWheel,
            firstWheelCreatedAt: firstWheelCreatedAt ? firstWheelCreatedAt.toISOString() : '',
            hasAtLeastOneActiveOrProgrammedWheel,
            latestEndDateForActiveWheels: latestEndDateForActiveWheels ? latestEndDateForActiveWheels.toISOString() : '',
            playersNb: giftDraws.length,
            retrievedGiftsNb: giftsRetrieved.length,
            reviewsNb: wheelReviews.length,
            gmbRating: gmbRating,
        };
        if (airtableData.length) {
            const airtableUser = airtableData.find(({ email }) => email === user.email);
            if (airtableUser) {
                Object.assign(userData, {
                    mainSubscriptionPlan: airtableUser.main_abonnement,
                    venues: airtableUser.venues,
                    venuesNb: airtableUser.nb_of_venues,
                    userName: airtableUser.Name,
                });
            }
        }
        usersData.push(userData);
    }

    await handleWriteFile(usersData);

    const userEmails = usersData.map(({ userEmail }) => userEmail);
    const airtableEmails = airtableData.map(({ email }) => email);
    const airtableUsersNotInDb = airtableEmails.filter((email) => !userEmails.includes(email));
    const dbUsersNotInAirtable = userEmails.filter((email) => !airtableEmails.includes(email));
    const commonEmails = userEmails.filter((email) => airtableEmails.includes(email));
    console.log(
        // eslint-disable-next-line max-len
        `commonUserEmails:  ${commonEmails.length} - airtableUsersNotInDb: ${airtableUsersNotInDb.length} - dbUsersNotInAirtable: ${dbUsersNotInAirtable.length}`
    );

    console.log(`processed ${users.length} users: file size is ${usersData.length} lines`);
}

main()
    .then(() => {
        console.log('Done !');
        process.exit(0);
    })
    .catch((error) => {
        console.warn('err >>', error);
        process.exit(1);
    });
