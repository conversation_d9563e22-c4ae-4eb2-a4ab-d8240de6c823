import 'reflect-metadata';

import ':env';

import { createObjectCsvWriter } from 'csv-writer';
import { max, min } from 'lodash';

import { UserWheelOfFortuneStatus } from '@malou-io/package-dto';
import {
    GiftDrawModel,
    IRestaurant,
    PlatformInsightModel,
    PlatformModel,
    RestaurantModel,
    ScanModel,
    UserModel,
    UserRestaurantModel,
    WheelOfFortuneModel,
} from '@malou-io/package-models';
import { PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { WheelOfFortune as WheelOfFortuneEntity } from ':modules/wheels-of-fortune/wheel-of-fortune.entity';
import ':plugins/db';

interface FileRow {
    restaurantId: string;
    restaurantName: string;
    hasAtLeastOneActiveOrProgrammedWheel: boolean;
    latestEndDateForActiveWheels: string;
    hasCreatedAWheel: boolean;
    firstWheelCreatedAt: string;
    playersNb: number;
    retrievedGiftsNb: number;
    reviewsNb: number;
    gmbRating: number;
    users: string;
}

async function getWheelOfFortuneStatusForUser(restaurants: IRestaurant[]): Promise<UserWheelOfFortuneStatus> {
    const userRestaurantIds = restaurants.map((restaurant) => restaurant._id);

    const restaurantsWheels = await WheelOfFortuneModel.find({ restaurantId: { $in: userRestaurantIds } });
    const aggregatedRestaurantWheels = await WheelOfFortuneModel.find({
        _id: { $in: restaurantsWheels.map((wheel: any) => wheel.aggregatedWheelOfFortuneId).filter(Boolean) },
    });

    const allRestaurantWheels = [...restaurantsWheels, ...aggregatedRestaurantWheels];

    const hasCreatedAWheel = !!allRestaurantWheels.length;
    const minWofCreatedAt: number | undefined = hasCreatedAWheel
        ? min(allRestaurantWheels.map((wheel) => new Date(wheel.createdAt).getTime()))
        : undefined;

    const activeOrProgrammedWheels = allRestaurantWheels.filter((wheel) => !!new WheelOfFortuneEntity(wheel).isActiveOrProgrammed());
    const hasAtLeastOneActiveOrProgrammedWheel = !!activeOrProgrammedWheels.length;
    const in100Years = new Date('12/12/2124');
    const maxActiveWofEndAt: number | undefined = activeOrProgrammedWheels.length
        ? max(activeOrProgrammedWheels.map((wheel) => (wheel.endDate ? new Date(wheel.endDate).getTime() : in100Years.getTime())))
        : undefined;

    return {
        hasAtLeastOneActiveOrProgrammedWheel,
        hasCreatedAWheel,
        firstWheelCreatedAt: minWofCreatedAt ? new Date(minWofCreatedAt) : null,
        latestEndDateForActiveWheels: maxActiveWofEndAt ? new Date(maxActiveWofEndAt) : null,
    };
}

async function handleWriteFile(data: FileRow[]): Promise<void> {
    try {
        const csvWriter = createObjectCsvWriter({
            path: `${__dirname}/restaurants-with-wof-${process.env.NODE_ENV}.csv`,
            header: [
                { id: 'restaurantId', title: 'restaurantId' },
                { id: 'restaurantName', title: 'restaurantName' },
                { id: 'hasCreatedAWheel', title: 'hasCreatedAWheel' },
                { id: 'firstWheelCreatedAt', title: 'firstWheelCreatedAt' },
                { id: 'hasAtLeastOneActiveOrProgrammedWheel', title: 'hasAtLeastOneActiveOrProgrammedWheel' },
                { id: 'latestEndDateForActiveWheels', title: 'latestEndDateForActiveWheels' },
                { id: 'playersNb', title: 'playersNb' },
                { id: 'retrievedGiftsNb', title: 'retrievedGiftsNb' },
                { id: 'reviewsNb', title: 'reviewsNb' },
                { id: 'gmbRating', title: 'gmbRating' },
                { id: 'users', title: 'users' },
            ],
        });
        await csvWriter.writeRecords(data);
    } catch (e) {
        console.log(`Error writing in file: ${JSON.stringify(e)}`);
    }
}

async function main() {
    const restaurants = await RestaurantModel.find({ active: true }, { _id: 1, active: 1, name: 1, internalName: 1 });
    const restaurantsData: FileRow[] = [];

    for (const restaurant of restaurants) {
        const userRestaurants = await UserRestaurantModel.find({ restaurantId: restaurant._id }, { userId: 1 });
        const userRestaurantIds = userRestaurants.map((userRestaurant) => userRestaurant.userId);
        const users = await UserModel.find({ _id: { $in: userRestaurantIds }, verified: true }, { _id: 1, email: 1 });

        if (!users.length) {
            continue;
        }

        const {
            hasAtLeastOneActiveOrProgrammedWheel,
            hasCreatedAWheel,
            firstWheelCreatedAt,
            latestEndDateForActiveWheels,
        }: UserWheelOfFortuneStatus = await getWheelOfFortuneStatusForUser([restaurant]);

        const giftDraws = await GiftDrawModel.find({ restaurantId: restaurant._id });
        const giftsRetrieved = giftDraws.filter((giftDraw) => !!giftDraw.retrievedAt);

        const wheelReviews = await ScanModel.find({
            'nfcSnapshot.restaurantId': restaurant._id,
            matchedReviewSocialId: { $ne: null },
            scannedAt: { $gte: firstWheelCreatedAt },
            'nfcSnapshot.redirectionLink': /wheel-of-fortune/,
        });

        const gmbPlatform = await PlatformModel.findOne({ key: PlatformKey.GMB, restaurantId: restaurant._id });
        let gmbRating;
        if (gmbPlatform) {
            const gmbRatings = await PlatformInsightModel.find(
                {
                    socialId: gmbPlatform.socialId,
                    metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                },
                { sort: { createdAt: -1 }, limit: 1, value: 1 }
            );
            gmbRating = gmbRatings?.[0];
        }

        const restaurantData = {
            restaurantId: restaurant._id.toString(),
            restaurantName: restaurant.name || restaurant.internalName || '',
            hasCreatedAWheel,
            firstWheelCreatedAt: firstWheelCreatedAt ? firstWheelCreatedAt.toISOString() : '',
            hasAtLeastOneActiveOrProgrammedWheel,
            latestEndDateForActiveWheels: latestEndDateForActiveWheels ? latestEndDateForActiveWheels.toISOString() : '',
            users: users.map((user) => user.email).join(', '),
            playersNb: giftDraws.length,
            retrievedGiftsNb: giftsRetrieved.length,
            reviewsNb: wheelReviews.length,
            gmbRating: gmbRating?.value,
        };
        restaurantsData.push(restaurantData);
    }

    await handleWriteFile(restaurantsData);

    console.log(`processed ${restaurants.length} users: file size is ${restaurantsData.length} lines`);
}

main()
    .then(() => {
        console.log('Done !');
        process.exit(0);
    })
    .catch((error) => {
        console.warn('err >>', error);
        process.exit(1);
    });
