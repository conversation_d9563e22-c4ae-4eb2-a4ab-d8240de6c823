import 'reflect-metadata';

import ':env';

import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { IPrivateReview, IScan, OverwriteOrAssign, PrivateReviewModel } from '@malou-io/package-models';

import ScansRepository from ':modules/scans/repository/scans.repository';
import ':plugins/db';

@singleton()
export class FixScanIdsThatAreNfcIdsTask {
    constructor(private readonly _scansRepository: ScansRepository) {}

    async execute(): Promise<void> {
        console.log('FixScanIdsThatAreNfcIdsTask started');

        const pipeline = [
            {
                $match: {
                    scanId: { $exists: true, $ne: null },
                },
            },
            // Get the private reviews that have a scanId that is not a valid scan id or null
            {
                $lookup: {
                    from: 'scans',
                    localField: 'scanId',
                    foreignField: '_id',
                    as: 'scan',
                },
            },
            {
                $match: {
                    scan: { $size: 0 },
                },
            },
        ];
        const cursor = PrivateReviewModel.aggregate(pipeline).cursor();

        let i = 0;
        let total = 0;
        await cursor.eachAsync(
            async (privateReviews: OverwriteOrAssign<IPrivateReview, { scan: IScan[] }>[]) => {
                console.log('Processing batch', i++, privateReviews.length);
                total += privateReviews.length;
                console.log('Total', total);
                const promises = privateReviews.map(async (privateReview) => {
                    // Get the scans from the nfc id within 15 minutes before the review creation date
                    const minScannedAt = DateTime.fromJSDate(privateReview.socialCreatedAt).minus({ minutes: 15 }).toJSDate();
                    const scans = await this._scansRepository.find({
                        filter: {
                            nfcId: privateReview.scanId,
                            scannedAt: { $gt: minScannedAt, $lt: privateReview.socialCreatedAt },
                            starClicked: privateReview.rating,
                        },
                        projection: { _id: 1, scannedAt: 1 },
                        options: { lean: true, sort: { scannedAt: -1 } },
                    });

                    const scanId = scans[0]?._id ?? null;

                    return {
                        updateOne: {
                            filter: { _id: privateReview._id },
                            update: { $set: { scanId } },
                        },
                    };
                });
                const bulkOperations = await Promise.all(promises);
                await PrivateReviewModel.bulkWrite(bulkOperations);
            },
            { batchSize: 1000 }
        );

        console.log('FixScanIdsThatAreNfcIdsTask finished');
    }
}
