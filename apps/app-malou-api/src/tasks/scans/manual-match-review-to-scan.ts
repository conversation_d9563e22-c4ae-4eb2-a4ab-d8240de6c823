import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { MatchReviewsToScansUseCase } from ':modules/reviews/use-cases/match-reviews-to-scans/match-reviews-to-scans.use-case';
import ':plugins/db';

@singleton()
export class ManualMatchReviewToScanTask {
    constructor(private readonly _matchReviewsToScansUseCase: MatchReviewsToScansUseCase) {}

    async execute() {
        const scanRedirectedAt: Date | undefined = new Date('2025-02-05T21:14:38.233+00:00'); // set the date you want to test
        // const minRedirectedAt: Date | undefined = scanRedirectedAt
        //     ? DateTime.fromJSDate(scanRedirectedAt).minus({ minutes: 1 }).toJSDate()
        //     : undefined;
        // const maxRedirectedAt: Date | undefined = scanRedirectedAt
        //     ? DateTime.fromJSDate(scanRedirectedAt).plus({ minutes: 1 }).toJSDate()
        //     : undefined;

        const minRedirectedAt = new Date('2024-09-01');
        const maxRedirectedAt = new Date('2024-10-01');
        const restaurantIds: string[] | undefined = undefined; // set the restaurant ids you want to test, undefined if you want to test all restaurants
        const onlyShowLogs = false; // set to true if you don't want to update the db, just to see the matched reviews

        await this._matchReviewsToScansUseCase.execute({ minRedirectedAt, maxRedirectedAt }, minRedirectedAt, {
            onlyShowLogs,
            restaurantIds,
        });
    }
}

const task = container.resolve(ManualMatchReviewToScanTask);

task.execute()
    .then(() => process.exit(0))
    .catch((error) => {
        console.log('error :>>', error);
        process.exit(1);
    });
