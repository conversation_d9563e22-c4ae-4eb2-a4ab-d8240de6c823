import { singleton } from 'tsyringe';

import { IScan, ScanModel } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class InitMatchedReviewSocialIdTask {
    constructor(private readonly _reviewsRepository: ReviewsRepository) {}

    async execute(): Promise<void> {
        console.log('InitMatchedReviewSocialIdTask started');

        const cursor = ScanModel.find({ matchedReviewId: { $ne: null }, matchedReviewSocialId: null }).cursor();

        let count = 0;
        await cursor.eachAsync(
            async (scans) => {
                console.log(`Starting matched review social id initialization for scans ${count} to ${count + scans.length}`);
                count += scans.length;

                const matchedReviewIds = (scans as unknown as (IScan & { matchedReviewId: string })[])
                    .map(({ matchedReviewId }) => matchedReviewId)
                    .filter(isNotNil);
                const reviews = await this._reviewsRepository.find({ filter: { _id: { $in: matchedReviewIds } } });

                const bulkOperations = (scans as unknown as (IScan & { matchedReviewId: string })[])
                    .map((scan) => {
                        const matchedReviewId = scan.matchedReviewId;
                        if (!matchedReviewId) {
                            return null; // will never happen
                        }

                        const review = reviews.find((review) => review._id.toString() === matchedReviewId.toString());
                        const update = {
                            $set: review
                                ? { matchedReviewSocialId: review.socialId } // if we found the review then we initialize matchedReviewSocialId
                                : { isCheckedForMatchingReview: false }, // else we unset the matching
                            $unset: { matchedReviewId: '' },
                        };

                        return {
                            updateOne: {
                                filter: { _id: scan._id },
                                update,
                            },
                        };
                    })
                    .filter(isNotNil);

                await ScanModel.bulkWrite(bulkOperations);
            },
            { batchSize: 1000 }
        );

        console.log('InitMatchedReviewSocialIdTask finished');
    }
}
