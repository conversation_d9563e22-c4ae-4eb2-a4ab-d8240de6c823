import { container } from 'tsyringe';

import { newDbId, ScanModel } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { getDefaultScan } from ':modules/scans/tests/scans.builder';
import { InitMatchedReviewSocialIdTask } from ':tasks/scans/init-matched-review-social-id/init-matched-review-social-id';

describe('InitMatchedReviewSocialIdTask', () => {
    beforeAll(() => {
        registerRepositories(['ReviewsRepository', 'ScansRepository']);
    });

    describe('should initialize matchedReviewSocialId from matchedReviewId', () => {
        it('should set matchedReviewSocialId when scan has matchedReviewId and review exists', async () => {
            const reviewId = newDbId();
            const socialId = 'review-social-id-123';

            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview()._id(reviewId).socialId(socialId).key(PlatformKey.GMB).build()];
                        },
                    },
                },
            });

            await testCase.build();

            // Create scan with matchedReviewId directly using ScanModel
            const scanData = getDefaultScan().build();
            await ScanModel.create({
                ...scanData,
                matchedReviewId: reviewId.toString(),
                matchedReviewSocialId: null,
            });

            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Verify scan has matchedReviewId but no matchedReviewSocialId before task
            const scansBeforeUpdate = await ScanModel.find({ matchedReviewId: { $ne: null } }).lean();
            expect(scansBeforeUpdate.length).toBe(1);
            expect((scansBeforeUpdate[0] as any).matchedReviewId?.toString()).toBe(reviewId.toString());
            expect(scansBeforeUpdate[0].matchedReviewSocialId).toBeNull();

            // Execute the task
            await initMatchedReviewSocialIdTask.execute();

            // Verify scan now has matchedReviewSocialId and no matchedReviewId
            const scansAfterUpdate = await ScanModel.find({ matchedReviewSocialId: socialId }).lean();
            expect(scansAfterUpdate.length).toBe(1);
            expect(scansAfterUpdate[0].matchedReviewSocialId).toBe(socialId);
            expect((scansAfterUpdate[0] as any).matchedReviewId).toBeUndefined();
        });

        it('should unset isCheckedForMatchingReview when scan has matchedReviewId but review does not exist', async () => {
            const nonExistentReviewId = newDbId();

            // Create scan with matchedReviewId directly using ScanModel (no corresponding review)
            const scanData = getDefaultScan().isCheckedForMatchingReview(true).build();
            await ScanModel.create({
                ...scanData,
                matchedReviewId: nonExistentReviewId.toString(),
                matchedReviewSocialId: null,
            });

            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Verify scan has matchedReviewId and isCheckedForMatchingReview before task
            const scansBeforeUpdate = await ScanModel.find({ matchedReviewId: { $ne: null } }).lean();
            expect(scansBeforeUpdate.length).toBe(1);
            expect((scansBeforeUpdate[0] as any).matchedReviewId?.toString()).toBe(nonExistentReviewId.toString());
            expect(scansBeforeUpdate[0].isCheckedForMatchingReview).toBe(true);

            // Execute the task
            await initMatchedReviewSocialIdTask.execute();

            // Verify scan has isCheckedForMatchingReview unset and no matchedReviewId
            const scansAfterUpdate = await ScanModel.find({ isCheckedForMatchingReview: false }).lean();
            expect(scansAfterUpdate.length).toBe(1);
            expect(scansAfterUpdate[0].isCheckedForMatchingReview).toBe(false);
            expect((scansAfterUpdate[0] as any).matchedReviewId).toBeUndefined();
            expect(scansAfterUpdate[0].matchedReviewSocialId).toBeNull();
        });

        it('should process multiple scans with different scenarios', async () => {
            const reviewId1 = newDbId();
            const reviewId2 = newDbId();
            const nonExistentReviewId = newDbId();
            const socialId1 = 'review-social-id-1';
            const socialId2 = 'review-social-id-2';

            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()._id(reviewId1).socialId(socialId1).key(PlatformKey.GMB).build(),
                                getDefaultReview()._id(reviewId2).socialId(socialId2).key(PlatformKey.FACEBOOK).build(),
                                // No review for nonExistentReviewId
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            // Create scans with matchedReviewId directly using ScanModel
            const scanData1 = getDefaultScan().build();
            const scanData2 = getDefaultScan().build();
            const scanData3 = getDefaultScan().isCheckedForMatchingReview(true).build();

            await ScanModel.create([
                { ...scanData1, matchedReviewId: reviewId1.toString(), matchedReviewSocialId: null },
                { ...scanData2, matchedReviewId: reviewId2.toString(), matchedReviewSocialId: null },
                { ...scanData3, matchedReviewId: nonExistentReviewId.toString(), matchedReviewSocialId: null },
            ]);

            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Execute the task
            await initMatchedReviewSocialIdTask.execute();

            // Verify results
            const allScansAfterUpdate = await ScanModel.find({}).sort({ _id: 1 }).lean();
            expect(allScansAfterUpdate.length).toBe(3);

            // First two scans should have matchedReviewSocialId set
            const scansWithSocialId = allScansAfterUpdate.filter((scan) => scan.matchedReviewSocialId);
            expect(scansWithSocialId.length).toBe(2);
            expect(scansWithSocialId.map((s) => s.matchedReviewSocialId).sort()).toEqual([socialId1, socialId2].sort());

            // Third scan should have isCheckedForMatchingReview unset
            const scanWithUnsetFlag = allScansAfterUpdate.find((scan) => scan.isCheckedForMatchingReview === false);
            expect(scanWithUnsetFlag).toBeDefined();
            expect(scanWithUnsetFlag?.matchedReviewSocialId).toBeNull();

            // All scans should have matchedReviewId removed
            expect(allScansAfterUpdate.every((scan) => (scan as any).matchedReviewId === undefined)).toBe(true);
        });
    });

    describe('should not process scans that do not meet criteria', () => {
        it('should not process scans that already have matchedReviewSocialId', async () => {
            const reviewId = newDbId();
            const existingSocialId = 'existing-social-id';

            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview()._id(reviewId).socialId('new-social-id').key(PlatformKey.GMB).build()];
                        },
                    },
                },
            });

            await testCase.build();

            // Create scan that already has matchedReviewSocialId
            const scanData = getDefaultScan().build();
            await ScanModel.create({
                ...scanData,
                matchedReviewSocialId: existingSocialId, // Already has a value
            });

            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Execute the task
            await initMatchedReviewSocialIdTask.execute();

            // Verify scan was not processed (matchedReviewSocialId unchanged)
            const scansAfterUpdate = await ScanModel.find({}).lean();
            expect(scansAfterUpdate.length).toBe(1);
            expect(scansAfterUpdate[0].matchedReviewSocialId).toBe(existingSocialId);
        });

        it('should not process scans that do not have matchedReviewId', async () => {
            // Create scan without matchedReviewId
            const scanData = getDefaultScan().build();
            await ScanModel.create({
                ...scanData,
                matchedReviewSocialId: null,
                // No matchedReviewId field
            });

            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Verify scan exists before task
            const scansBeforeUpdate = await ScanModel.find({}).lean();
            expect(scansBeforeUpdate.length).toBe(1);
            expect(scansBeforeUpdate[0].matchedReviewSocialId).toBeNull();

            // Execute the task
            await initMatchedReviewSocialIdTask.execute();

            // Verify scan was not processed (no changes)
            const scansAfterUpdate = await ScanModel.find({}).lean();
            expect(scansAfterUpdate.length).toBe(1);
            expect(scansAfterUpdate[0].matchedReviewSocialId).toBeNull();
            expect((scansAfterUpdate[0] as any).matchedReviewId).toBeUndefined();
        });

        it('should not process scans that have null matchedReviewId', async () => {
            // Create scan with null matchedReviewId
            const scanData = getDefaultScan().build();
            await ScanModel.create({
                ...scanData,
                matchedReviewId: null,
                matchedReviewSocialId: null,
            });

            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Execute the task
            await initMatchedReviewSocialIdTask.execute();

            // Verify scan was not processed
            const scansAfterUpdate = await ScanModel.find({}).lean();
            expect(scansAfterUpdate.length).toBe(1);
            expect(scansAfterUpdate[0].matchedReviewSocialId).toBeNull();
            expect((scansAfterUpdate[0] as any).matchedReviewId).toBeNull();
        });
    });

    describe('edge cases and mixed scenarios', () => {
        it('should handle empty database gracefully', async () => {
            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Execute the task on empty database
            await expect(initMatchedReviewSocialIdTask.execute()).resolves.not.toThrow();

            // Verify database is still empty
            const scansAfterUpdate = await ScanModel.find({}).lean();
            expect(scansAfterUpdate.length).toBe(0);
        });

        it('should only update scans that meet criteria in mixed scenario', async () => {
            const reviewId = newDbId();
            const socialId = 'review-social-id';
            const existingSocialId = 'existing-social-id';

            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview()._id(reviewId).socialId(socialId).key(PlatformKey.GMB).build()];
                        },
                    },
                },
            });

            await testCase.build();

            // Create different types of scans
            const scanData1 = getDefaultScan().build();
            const scanData2 = getDefaultScan().build();
            const scanData3 = getDefaultScan().build();
            const scanData4 = getDefaultScan().build();

            await ScanModel.create([
                // Should be updated: has matchedReviewId and null matchedReviewSocialId
                { ...scanData1, matchedReviewId: reviewId.toString(), matchedReviewSocialId: null },
                // Should NOT be updated: already has matchedReviewSocialId
                { ...scanData2, matchedReviewSocialId: existingSocialId },
                // Should NOT be updated: no matchedReviewId
                { ...scanData3, matchedReviewSocialId: null },
                // Should NOT be updated: null matchedReviewId
                { ...scanData4, matchedReviewId: null, matchedReviewSocialId: null },
            ]);

            const initMatchedReviewSocialIdTask = container.resolve(InitMatchedReviewSocialIdTask);

            // Execute the task
            await initMatchedReviewSocialIdTask.execute();

            // Verify results
            const allScans = await ScanModel.find({}).sort({ _id: 1 }).lean();
            expect(allScans.length).toBe(4);

            // First scan should be updated
            expect(allScans[0].matchedReviewSocialId).toBe(socialId);
            expect((allScans[0] as any).matchedReviewId).toBeUndefined();

            // Second scan should remain unchanged
            expect(allScans[1].matchedReviewSocialId).toBe(existingSocialId);
            expect((allScans[1] as any).matchedReviewId).toBeUndefined();

            // Third scan should remain unchanged
            expect(allScans[2].matchedReviewSocialId).toBeNull();
            expect((allScans[2] as any).matchedReviewId).toBeUndefined();

            // Fourth scan should remain unchanged
            expect(allScans[3].matchedReviewSocialId).toBeNull();
            expect((allScans[3] as any).matchedReviewId).toBeNull();
        });
    });
});
