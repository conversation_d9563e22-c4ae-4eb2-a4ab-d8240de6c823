import 'reflect-metadata';

import ':env';

import { chunk, maxBy, minBy } from 'lodash';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, ScanModel } from '@malou-io/package-models';
import { getWeeksFromPeriod } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import ':plugins/db';

interface AggregateScanResult {
    groupId: { reviewSocialId: string; platformKey: string; restaurantId: DbId };
    scanIds: string[];
    minScanRedirectedAt: Date;
    maxScanRedirectedAt: Date;
}

const agendaSingleton = container.resolve(AgendaSingleton);

const cleanScans = async (scansResult: AggregateScanResult[], minRedirectedAt: Date | null, maxRedirectedAt: Date | null) => {
    const bulkOperations: any[] = [];
    scansResult.forEach(async (scanResult, i) => {
        console.log(`[${i}]` + 'Cleaning matched review with multiple scans: ', scanResult.groupId);
        const { scanIds, minScanRedirectedAt, maxScanRedirectedAt } = scanResult;
        const [_mainScanId, ...otherScanIds] = scanIds;
        otherScanIds.forEach((scanId) => {
            bulkOperations.push({
                updateOne: {
                    filter: {
                        _id: scanId,
                    },
                    update: {
                        $unset: {
                            matchedReviewSocialId: 1,
                            isCheckedForMatchingReview: 1,
                        },
                    },
                },
            });
            minRedirectedAt = minRedirectedAt
                ? (minBy([minRedirectedAt, minScanRedirectedAt], (date) => date.getTime()) as Date)
                : minScanRedirectedAt;
            maxRedirectedAt = maxRedirectedAt
                ? (maxBy([maxRedirectedAt, maxScanRedirectedAt], (date) => date.getTime()) as Date)
                : maxScanRedirectedAt;
        });
    });
    await ScanModel.bulkWrite(bulkOperations);
    console.log('Cleaned matched reviews with multiple scans', bulkOperations.length);
    return { minRedirectedAt, maxRedirectedAt };
};

const scheduleJobs = async (minRedirectedAt: Date, maxRedirectedAt: Date) => {
    console.log('Scheduling jobs to match reviews to scans in the period');

    const weeks = getWeeksFromPeriod(minRedirectedAt, maxRedirectedAt);
    const promises = weeks.map((week, i) => {
        const minRedirectedAtEndOfDay = DateTime.fromJSDate(week.startDayInsideRange).startOf('day').toJSDate();
        const maxRedirectedAtEndOfDay = DateTime.fromJSDate(week.endDayInsideRange).endOf('day').toJSDate();
        console.log(`[${i}]` + 'Scheduling job for week: ', {
            minRedirectedAt: minRedirectedAtEndOfDay,
            maxRedirectedAt: maxRedirectedAtEndOfDay,
        });
        const scheduleDate = DateTime.local()
            .plus({ seconds: i * 5 })
            .toJSDate();

        return agendaSingleton.schedule(scheduleDate, AgendaJobName.MATCH_REVIEW_TO_SCAN, {
            minRedirectedAt: minRedirectedAtEndOfDay,
            maxRedirectedAt: maxRedirectedAtEndOfDay,
        });
    });
    return await Promise.all(promises);
};

const main = async () => {
    console.time('Clean matched reviews done in');
    const aggregate = [
        {
            $match: {
                matchedReviewSocialId: {
                    $ne: null,
                },
            },
        },
        {
            $group: {
                _id: {
                    matchedReviewSocialId: '$matchedReviewSocialId',
                    platformKey: '$nfcSnapshot.platformKey',
                    restaurantId: '$nfcSnapshot.restaurantId',
                },
                count: {
                    $sum: 1,
                },
                scanIds: {
                    $push: '$_id',
                },
                minRedirectedAt: {
                    $min: '$redirectedAt',
                },
                maxRedirectedAt: {
                    $max: '$redirectedAt',
                },
            },
        },
        {
            $match: {
                count: {
                    $gt: 1,
                },
            },
        },
        {
            $project: {
                groupId: '$_id',
                scanIds: '$scanIds',
                minScanRedirectedAt: '$minRedirectedAt',
                maxScanRedirectedAt: '$maxRedirectedAt',
            },
        },
    ];
    const scansResult = await ScanModel.aggregate(aggregate);
    if (scansResult.length === 0) {
        console.log('No matched reviews with multiple scans found');
        return;
    }
    const _agendaInstance = await agendaSingleton.getInstance(); // Needed else we got an error that the db is closed when used
    let minRedirectedAt: Date | null = null;
    let maxRedirectedAt: Date | null = null;
    const scansResultChunks = chunk(scansResult, 500);
    for (const scansResultChunk of scansResultChunks) {
        ({ minRedirectedAt, maxRedirectedAt } = await cleanScans(scansResultChunk, minRedirectedAt, maxRedirectedAt));
    }
    console.log('Cleaned matched reviews with multiple scans', { minRedirectedAt, maxRedirectedAt });
    console.timeEnd('Clean matched reviews done in');
    if (!minRedirectedAt || !maxRedirectedAt) {
        console.log('No min or max redirected at found');
        return;
    }
    console.time('Scheduling jobs done in');
    const res = await scheduleJobs(minRedirectedAt, maxRedirectedAt);
    console.log('Scheduled jobs', res.length);
    console.timeEnd('Scheduling jobs done in');
};

main()
    .then(() => {
        console.log('done');
        process.exit(0);
    })
    .catch((error) => {
        console.warn('err >>', error);
        process.exit(1);
    });
