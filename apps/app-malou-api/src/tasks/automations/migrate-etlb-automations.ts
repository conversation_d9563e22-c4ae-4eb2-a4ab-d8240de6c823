/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import ReviewReplyAutomationsRepository from ':modules/automations/features/review-replies/review-replies.repository';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(
        private readonly _reviewReplyAutomationsRepository: ReviewReplyAutomationsRepository,
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute() {
        const doNotMigrateRestaurantIds = [
            '675bf22aab7f5dfcc4fecc2f',
            '65a53d5e4abb9dc38ba4d927',
            '65a53d704abb9dc38ba4e905',
            '653fdd9dd8e107ebd36109d3',
            '68a5d24c4d3db77aa63ce1f4',
            '68640170fa36fe4cdb7dd01d',
            '68b817f878da28a09032356e',
            '68b818e878da28a09032672d',
            '68d3c66a5f731606c48b4c3d',
            '653a86c6d8e107ebd3f6e7b5', // <- aix for reference
        ];
        const aixRestaurantId = '653a86c6d8e107ebd3f6e7b5';
        const etlbRestaurants = await this._restaurantsRepository.find({
            filter: { organizationId: '653a6174f7d676ee28200073', active: true, _id: { $nin: doNotMigrateRestaurantIds.map(toDbId) } },
            projection: { _id: 1 },
            options: { lean: true },
        });
        const aixExistingAutomations = await this._reviewReplyAutomationsRepository.find({
            filter: {
                restaurantId: aixRestaurantId,
                feature: 'REPLY_TO_REVIEW',
                platformKey: { $in: [PlatformKey.GMB, PlatformKey.UBEREATS, PlatformKey.DELIVEROO] },
            },
            options: { lean: true },
        });
        const etlbRestaurantIds = etlbRestaurants.map((restaurant) => restaurant._id);
        for (const restaurantId of etlbRestaurantIds) {
            for (const automation of aixExistingAutomations) {
                const upsertData = {
                    feature: automation.feature,
                    platformKey: automation.platformKey,
                    ratingCategory: automation.ratingCategory,
                    restaurantId,
                    withComment: automation.withComment,
                    replyMethod: automation.replyMethod,
                    active: automation.active,
                    shouldValidateAiBeforeSend: automation.shouldValidateAiBeforeSend,
                    templateIds: automation.templateIds,
                };
                await this._reviewReplyAutomationsRepository.upsert({
                    filter: {
                        restaurantId,
                        feature: automation.feature,
                        platformKey: automation.platformKey,
                        ratingCategory: automation.ratingCategory,
                        withComment: automation.withComment,
                    },
                    update: upsertData,
                    options: { lean: true },
                });
            }
        }
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
