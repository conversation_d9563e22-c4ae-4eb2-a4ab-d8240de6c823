import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';

import { IMedia, IPost } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import PostsRepository from ':modules/posts/posts.repository';
import ':plugins/db';

enum CropOption {
    SQUARE = 1,
    PORTRAIT = 4 / 5,
    VERTICAL = 9 / 16,
    LANDSCAPE = 1.91,
    LANDSCAPE_16_9 = 16 / 9,
}

const parseAspectRatio = (aspectRatio: number): number => parseFloat(aspectRatio.toFixed(2));

@autoInjectable()
class UpdateInfectedPostMediasTask {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _mediaRepository: MediasRepository
    ) {}

    async execute() {
        const _getCorrespondingAspectRatio = (media: Pick<IMedia, 'dimensions'>): number => {
            if (!media.dimensions?.original?.width || !media.dimensions?.original?.height) {
                return CropOption.SQUARE;
            }
            const mediaAspectRatio = media.dimensions.original.width / media.dimensions.original.height;
            return mediaAspectRatio > 1 ? CropOption.LANDSCAPE : mediaAspectRatio < 1 ? CropOption.PORTRAIT : CropOption.SQUARE;
        };

        const getAspectRatio = (media: Pick<IMedia, 'dimensions'>): number => {
            if (!media.dimensions?.original?.width || !media.dimensions?.original?.height) {
                return 0;
            }
            return media.dimensions.original.width / media.dimensions.original.height;
        };

        const computeResizeMetadata = ({
            mediaAspectRatio,
            desiredAspectRatio,
            width,
            height,
        }: {
            mediaAspectRatio: number;
            desiredAspectRatio: number;
            width: number;
            height: number;
        }): any => {
            if (parseAspectRatio(mediaAspectRatio) === desiredAspectRatio) {
                return {
                    width,
                    height,
                    cropPosition: {
                        top: 0,
                        left: 0,
                    },
                    aspectRatio: desiredAspectRatio,
                };
            }

            const newWidth = parseAspectRatio(mediaAspectRatio) < desiredAspectRatio ? width : height * desiredAspectRatio;
            const newHeight = parseAspectRatio(mediaAspectRatio) < desiredAspectRatio ? width / desiredAspectRatio : height;
            return {
                width: newWidth,
                height: newHeight,
                cropPosition: {
                    top: newHeight < height ? (height - newHeight) / 2 : 0,
                    left: newWidth < width ? (width - newWidth) / 2 : 0,
                },
                aspectRatio: desiredAspectRatio,
            };
        };

        const _updateMediasResizeMetadata = async (
            medias: Pick<Media, 'dimensions' | 'id'>[],
            desiredAspectRatio: number
        ): Promise<any> => {
            return await Promise.all(
                medias.map((media) =>
                    this._mediaRepository.updateMedia(media.id, {
                        resizeMetadata: computeResizeMetadata({
                            mediaAspectRatio: getAspectRatio(media),
                            desiredAspectRatio: parseAspectRatio(desiredAspectRatio),
                            height: media.dimensions?.original?.height ?? 0,
                            width: media.dimensions?.original?.width ?? 0,
                        }),
                    })
                )
            );
        };

        const infectedPosts: IPost[] = await this._postsRepository.aggregate([
            {
                $match: {
                    published: {
                        $in: ['pending', 'draft', 'error', 'rejected'],
                    },
                },
            },
            {
                $lookup: {
                    from: 'media',
                    localField: 'attachments',
                    foreignField: '_id',
                    as: 'medias',
                },
            },
            {
                $match: {
                    'medias.0': {
                        $exists: true,
                    },
                },
            },
            {
                $unwind: {
                    path: '$medias',
                    includeArrayIndex: 'string',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $match: {
                    'medias.dimensions.original.width': {
                        $gte: 0,
                    },
                    'medias.resizeMetadata.width': {
                        $gte: 0,
                    },
                },
            },
            {
                $match: {
                    'medias.type': 'photo',
                    $expr: {
                        $gt: ['$medias.resizeMetadata.width', '$medias.dimensions.original.width'],
                    },
                    plannedPublicationDate: {
                        $exists: true,
                    },
                },
            },
            {
                $group: {
                    _id: '$_id',
                    post: {
                        $first: '$$ROOT',
                    },
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: [
                            '$post',
                            {
                                count: '$count',
                            },
                        ],
                    },
                },
            },
        ]);

        for (const post of infectedPosts) {
            const attachments = post.attachments?.filter(isNotNil) ?? [];
            const allMediasInTheRightOrder = await Promise.all(
                attachments.map(async (mediaId) => {
                    return await this._mediaRepository.findById(mediaId.toString());
                })
            );
            const [firstMedia] = allMediasInTheRightOrder.filter(isNotNil);
            if (!firstMedia.dimensions?.original?.width || !firstMedia.dimensions?.original?.height) {
                continue;
            }
            const correspondingAspectRatio = _getCorrespondingAspectRatio(firstMedia);
            await _updateMediasResizeMetadata(allMediasInTheRightOrder.filter(isNotNil), correspondingAspectRatio);
        }
    }
}

const task = container.resolve(UpdateInfectedPostMediasTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
