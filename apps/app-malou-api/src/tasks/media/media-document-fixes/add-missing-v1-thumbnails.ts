import 'reflect-metadata';

import ':env';

import { spawnSync } from 'child_process';
import * as fs from 'fs';
import assert from 'node:assert/strict';
import * as path from 'path';
import prompts from 'prompts';
import { autoInjectable, container } from 'tsyringe';

import { MediaModel } from '@malou-io/package-models';
import { MimeType } from '@malou-io/package-utils';

import { MediasRepository } from ':modules/media/medias.repository';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

// change this with your ffmpeg path on your local machine
const ffmpegPath = 'ffmpeg';
const thumbnailSecond = '1';

/**
 * Add a thumbnail url where it's missing
 */
@autoInjectable()
class CreateVideoThumbnailsTask {
    constructor(
        private readonly _mediaRepository: MediasRepository,
        private readonly _awsS3DistantStorageService: AwsS3DistantStorageService
    ) {}

    async execute() {
        const filter = {
            createdAt: { $gte: new Date('2012-01-01') },
            type: 'video',
            thumbnail: null,
            isV2: { $ne: true },
        };

        const count = await this._mediaRepository.countDocuments({ filter });

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `About to update ${count} documents`,
            initial: false,
        });

        if (!response.value) {
            console.log('exit...');
            process.exit(0);
        }

        const mediasCursor = MediaModel.find(filter, '', { lean: true }).cursor();

        for await (const media of mediasCursor) {
            try {
                const tmpThumbnailPath = path.join(__dirname, `./tmp/${media._id}.jpg`);
                const ffmpegParams = this._createFfmpegParams({ videoUrl: media.urls.original, tmpThumbnailPath });

                spawnSync(ffmpegPath, ffmpegParams);

                const read = fs.createReadStream(tmpThumbnailPath);
                const thumbnailS3Key = this._computeNewS3KeyInSameFolderAsUrlInParam(media.urls.original);

                await this._awsS3DistantStorageService.saveFromReadable(thumbnailS3Key, read, { contentType: MimeType.IMAGE_JPEG });
                const thumbnailUrl = await this._awsS3DistantStorageService.getPublicAccessibleUrl(thumbnailS3Key);

                await this._mediaRepository.findOneAndUpdate({ filter: media._id, update: { thumbnail: thumbnailUrl } });

                fs.unlinkSync(tmpThumbnailPath);
            } catch (error) {
                console.log('error', error);
            }
        }
    }

    private _computeNewS3KeyInSameFolderAsUrlInParam(originalUrl: string): string {
        const url = new URL(originalUrl);
        const path = url.pathname.split('/').slice(0, -1); // removes the file name
        path.push(`thumbnail.jpg`); // re-add the new filename
        return path.join('/');
    }

    private _createFfmpegParams(params: { videoUrl: string; tmpThumbnailPath: string }): string[] {
        return [
            '-ss',
            thumbnailSecond,
            '-i',
            params.videoUrl,
            '-vf',
            "thumbnail,scale='-1:min(400, iw)",
            '-vframes',
            '1',
            params.tmpThumbnailPath,
        ];
    }
}

// needed for storage.uploadMedia
assert(process.env.AWS_BUCKET);
assert(process.env.AWS_REGION);
const task = container.resolve(CreateVideoThumbnailsTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
