import 'reflect-metadata';

import ':env';

import ffprobe, { FFProbeStream } from 'ffprobe';
import assert from 'node:assert/strict';
import probe from 'probe-image-size';
import prompts from 'prompts';
import { autoInjectable, container } from 'tsyringe';

import { MediaModel } from '@malou-io/package-models';

import { MediasRepository } from ':modules/media/medias.repository';

const RATIO_TOLERANCE = 0.01;

/**
 * Fix media that does not have original dimensions.
 *
 * You will need a locally installed ffprobe.
 */
@autoInjectable()
class AddMissingOriginalDimensionsTask {
    constructor(private readonly _mediaRepository: MediasRepository) {}

    async execute() {
        const filter = {
            // createdAt: { $gte: new Date('2025-01-01') },
            isV2: { $ne: true },
            'dimensions.original': null,
        };

        const count = await this._mediaRepository.countDocuments({ filter });

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `About to update ${count} documents`,
            initial: false,
        });

        if (!response.value) {
            console.log('exit...');
            process.exit(0);
        }

        const mediasCursor = await MediaModel.find(filter, '', { lean: true }).cursor();

        let i = 0;
        let failedCount = 0;
        for await (const media of mediasCursor) {
            i++;
            if (i % 500 === 0) {
                console.log('i', i);
            }
            try {
                if (!media.urls.original) {
                    console.log('not enough data', media._id.toString());
                    continue;
                }

                let newOriginalDimensions: { width: number | undefined; height: number | undefined } | undefined;
                if (media.type === 'photo') {
                    const meta = await probe(media.urls.original);
                    newOriginalDimensions = { width: meta.width, height: meta.height };
                } else if (media.type === 'video') {
                    const meta = await ffprobe(media.urls.original, { path: 'ffprobe' });
                    const videoStream: FFProbeStream = meta.streams.find((stream) => stream.codec_type === 'video') as FFProbeStream;
                    newOriginalDimensions = { width: videoStream.width, height: videoStream.height };
                }
                assert(newOriginalDimensions?.width);
                assert(newOriginalDimensions?.height);
                await this._mediaRepository.updateOne({
                    filter: { _id: media._id },
                    update: { 'dimensions.original': newOriginalDimensions },
                });
            } catch (error) {
                failedCount++;
                console.log('error', media._id.toString(), error);
            }
        }
        console.log('failedCount', failedCount);
    }
}

const task = container.resolve(AddMissingOriginalDimensionsTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
