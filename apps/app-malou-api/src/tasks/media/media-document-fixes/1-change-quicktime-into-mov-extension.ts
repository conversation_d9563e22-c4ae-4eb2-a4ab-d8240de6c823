import 'reflect-metadata';

import ':env';

import prompts from 'prompts';
import { container } from 'tsyringe';

import { MediaModel } from '@malou-io/package-models';
import { MimeType } from '@malou-io/package-utils';

import { Config } from ':config';
import { MediasRepository } from ':modules/media/medias.repository';
import { aws } from ':plugins/aws';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

const mediaRepository = container.resolve(MediasRepository);
const awsS3DistantStorageService = container.resolve(AwsS3DistantStorageService);
const s3 = new aws.S3();

/**
 * Just rename the s3 object from .quicktime to .mov without modifying the content.
 */
(async () => {
    const filter = {
        'urls.original': /\.quicktime$/,
        isV2: { $ne: true },
    };

    const count = await mediaRepository.countDocuments({ filter });

    const response = await prompts({
        type: 'confirm',
        name: 'value',
        message: `About to update ${count} documents`,
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    const medias = MediaModel.find(filter, '', { lean: true }).cursor();

    for await (const media of medias) {
        if (!media.urls.original) {
            console.log('no original url', media._id.toString());
            continue;
        }
        const url = new URL(media.urls.original);
        const oldKey = url.pathname;
        const newkey = oldKey.replace(/.quicktime$/, '.mov');
        const copySource = encodeURIComponent(`${Config.services.s3.bucketName}${oldKey}`);

        await s3
            .copyObject({
                Bucket: Config.services.s3.bucketName,
                CopySource: copySource,
                Key: newkey.slice(1),
                ContentType: MimeType.VIDEO_QUICKTIME,
            })
            .promise();

        const newUrl = await awsS3DistantStorageService.getPublicAccessibleUrl(newkey);
        await mediaRepository.updateOne({
            filter: { _id: media._id },
            update: { 'urls.original': newUrl, format: 'mov' },
        });
    }

    console.log('DONE');

    process.exit(0);
})();
