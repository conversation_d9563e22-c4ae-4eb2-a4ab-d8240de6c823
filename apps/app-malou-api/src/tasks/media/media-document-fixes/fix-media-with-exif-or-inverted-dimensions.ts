import 'reflect-metadata';

import ':env';

import axios from 'axios';
import { <PERSON>ursor } from 'mongoose';
import assert from 'node:assert/strict';
import * as probeImageSize from 'probe-image-size';
import sharp from 'sharp';
import { container } from 'tsyringe';

import { IMedia, MediaModel } from '@malou-io/package-models';
import { IG_FIT_BIGGER_SIDE_MAX_SIZE, MimeType } from '@malou-io/package-utils';

import { randomString } from ':helpers/utils';
import { MediasRepository } from ':modules/media/medias.repository';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

const RATIO_TOLERANCE = 0.03;

const dryRun = true;

const mediaRepository = container.resolve(MediasRepository);
const awsS3DistantStorageService = container.resolve(AwsS3DistantStorageService);

class Mutex {
    private _listeners: (() => void)[] = [];
    private _locked: boolean = false;

    public lock(): Promise<() => void> {
        return new Promise((resolve) => {
            const unlock = () => {
                const listener = this._listeners[0];
                if (listener) {
                    listener();
                    this._listeners = this._listeners.filter((l) => l !== listener);
                } else {
                    this._locked = false;
                }
            };

            const onLock = () => {
                this._locked = true;
                resolve(unlock);
            };

            if (this._locked) {
                this._listeners = [...this._listeners, onLock];
            } else {
                onLock();
            }
        });
    }
}

class SharedCursor<Doc, Options = never> {
    private _mutex = new Mutex();
    private _cursor: Cursor<Doc, Options> | null;

    public constructor(cursor: Cursor<Doc, Options>) {
        this._cursor = cursor;
    }

    public async next(): Promise<Doc | null> {
        const unlock = await this._mutex.lock();
        try {
            if (!this._cursor) {
                return null;
            }
            const doc = await this._cursor.next();
            if (doc === null) {
                this._cursor = null;
            }
            return doc;
        } finally {
            unlock();
        }
    }
}

/**
 * If the media had exif orientation data, the dimensions.original could be inverted.
 * This was the case for old media and new medias duplicated from these old medias.
 */
const run = async (): Promise<void> => {
    const filter = {
        type: 'photo',
        isV2: { $ne: true },
    };

    const mediasCursor = new SharedCursor(MediaModel.find(filter, '', { lean: true }).sort({ createdAt: -1 }).cursor());

    let date: Date = new Date();
    let i: number = 0;

    setInterval(() => console.log('progress:', i, date.toISOString()), 1000 * 2);

    const loop = async () => {
        for (;;) {
            const media = await mediasCursor.next();
            if (!media) {
                return;
            }
            date = media.createdAt;
            i++;
            await processMedia(media);
        }
    };

    const parallelism = 16;
    await Promise.all([...Array(parallelism)].map(() => loop()));
};

const processMedia = async (media: IMedia): Promise<void> => {
    try {
        if (!media.urls.original) {
            console.log('not enough data', media._id.toString());
            return;
        }

        const meta = await probeImageSize.default(media.urls.original);
        assert(meta.width);
        assert(meta.height);
        if (meta.orientation !== undefined && meta.orientation !== 1) {
            const buffer = await axios.get(media.urls.original, { responseType: 'arraybuffer' });
            await processMediaWithExifOrientation(media, buffer.data);
        } else {
            await processMediaWithoutExifOrientation(media, { width: meta.width, height: meta.height });
        }
    } catch (error) {
        if (error instanceof probeImageSize.Error && error.message.includes('bad status code: 403')) {
            console.log(media._id.toString(), 'bad HTTP status code: 403');
            return;
        }
        if (error instanceof probeImageSize.Error && error.message.includes('unrecognized file format')) {
            console.log(media._id.toString(), 'unrecognized file format');
            return;
        }
        console.error('error', media._id.toString(), error);
    }
};

const processMediaWithExifOrientation = async (media: IMedia, buffer: Buffer): Promise<void> => {
    console.log('Fixing media WITH exif', media._id.toString());

    // we use .rotate() to strip the EXIF data and to re-encode the image with the correct orientation
    const bufferWithoutExif = await sharp(buffer).rotate().jpeg().toBuffer({ resolveWithObject: true });
    const newOriginalDimensions = {
        width: bufferWithoutExif.info.width,
        height: bufferWithoutExif.info.height,
    };

    if (!dryRun) {
        const newS3Key = computeNewS3KeyInSameFolderAsUrlInParam(media.urls.original, 'original-');
        await awsS3DistantStorageService.saveFromBuffer(newS3Key, bufferWithoutExif.data, {
            contentType: MimeType.IMAGE_JPEG,
        });
        const newOriginalUrl = await awsS3DistantStorageService.getPublicAccessibleUrl(newS3Key);
        await mediaRepository.updateOne({
            filter: { _id: media._id },
            update: { $set: { 'urls.original': newOriginalUrl, 'dimensions.original': newOriginalDimensions } },
        });
    }

    if (media.dimensions?.igFit && !areDimensionsConsistentWithIgFit(media, newOriginalDimensions) && !dryRun) {
        console.log('fixing igfit', media._id.toString());
        const igFit = await generateIgFitPicture(buffer);
        console.log(igFit.info, newOriginalDimensions);
        assert(doDimensionsMatch(igFit.info, newOriginalDimensions));

        const newS3Key = computeNewS3KeyInSameFolderAsUrlInParam(media.urls.original, 'igfit-');

        await awsS3DistantStorageService.saveFromBuffer(newS3Key, igFit.data, {
            contentType: MimeType.IMAGE_JPEG,
        });
        const newUrl = await awsS3DistantStorageService.getPublicAccessibleUrl(newS3Key);

        await mediaRepository.updateOne({
            filter: { _id: media._id },
            update: { $set: { 'urls.igFit': newUrl, 'dimensions.igFit': { width: igFit.info.width, height: igFit.info.height } } },
        });
    }
};

const processMediaWithoutExifOrientation = async (media: IMedia, meta: { width: number; height: number }): Promise<void> => {
    if (media.dimensions?.original && areDimensionsConsistentWithIgFit(media, media.dimensions.original)) {
        return;
    }

    console.log('Fixing media WITHOUT exif', media._id.toString());

    const newOriginalDimensions = {
        width: meta.width,
        height: meta.height,
    };

    if (media.dimensions?.igFit && !doDimensionsMatch(media.dimensions?.igFit, newOriginalDimensions)) {
        if (doDimensionsMatch({ width: media.dimensions?.igFit.height, height: media.dimensions?.igFit.width }, newOriginalDimensions)) {
            // swap the width and the height
            const update = {
                $set: {
                    'dimensions.igFit': {
                        width: media.dimensions?.igFit.height,
                        height: media.dimensions?.igFit.width,
                    },
                },
            };
            if (dryRun) {
                console.log('updating', media._id, update);
            } else {
                await mediaRepository.updateOne({
                    filter: { _id: media._id },
                    update,
                });
            }
        } else {
            console.log('need manual fix (b)', media._id.toString());
            return;
        }
    }

    const update = { $set: { 'dimensions.original': newOriginalDimensions } };
    if (dryRun) {
        console.log('updating', media._id, update);
    } else {
        await mediaRepository.updateOne({
            filter: { _id: media._id },
            update,
        });
    }
};

const computeNewS3KeyInSameFolderAsUrlInParam = (originalUrl: string, newPrefix: 'original-' | 'igfit-'): string => {
    const url = new URL(originalUrl);
    const path = url.pathname.split('/').slice(0, -1); // removes the file name
    const uuid = randomString(6);
    path.push(`${newPrefix}${uuid}.jpeg`); // re-add the modified file name
    return path.join('/');
};

const doDimensionsMatch = (a: { width: number; height: number }, b: { width: number; height: number }): boolean => {
    const ratioA = a.width / a.height;
    const ratioB = b.width / b.height;
    return Math.abs(ratioA - ratioB) < RATIO_TOLERANCE;
};

const generateIgFitPicture = async (originalImageBuffer: Buffer): Promise<{ data: Buffer; info: sharp.OutputInfo }> => {
    return await sharp(originalImageBuffer)
        .rotate()
        .resize(IG_FIT_BIGGER_SIDE_MAX_SIZE, IG_FIT_BIGGER_SIDE_MAX_SIZE)
        .jpeg()
        .toBuffer({ resolveWithObject: true });
};

/**
 * Returns true iff the provided dimensions are consistent with media.dimensions.igFit.
 *
 * We don’t check media.dimensions.small because a lot of old medias have a 200px square
 * as the “small” picture, with the field dimensions.small set to {width: 200, height: 200}.
 *
 * Here, “consistent” means “they have nearly the same aspect ratio”.
 */
const areDimensionsConsistentWithIgFit = (media: IMedia, dimensions: { width: number; height: number }): boolean => {
    if (media.dimensions?.igFit) {
        if (!doDimensionsMatch(dimensions, media.dimensions.igFit)) {
            return false;
        }
    }

    return true;
};

run()
    .then(() => {
        process.exit(0);
    })
    .catch((error: unknown) => {
        console.error(error);
        process.exit(1);
    });
