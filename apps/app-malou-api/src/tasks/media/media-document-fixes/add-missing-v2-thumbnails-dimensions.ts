import 'reflect-metadata';

import ':env';

import axios from 'axios';
import assert from 'node:assert/strict';
import prompts from 'prompts';
import sharp from 'sharp';
import { autoInjectable, container } from 'tsyringe';

import { MediasRepository } from ':modules/media/medias.repository';

/**
 * (For v2 media) Add dimensions for thumbnail1024Outside/thumbnail256Outside when it's missing.
 * This only fix the time where we were testing, this filter in this task should return 0 from now.
 */
@autoInjectable()
class AddThumbnailsDimensionsTask {
    constructor(private readonly _mediaRepository: MediasRepository) {}

    async execute() {
        const filter = {
            createdAt: { $gte: new Date('2012-01-01') },
            'storedObjects.thumbnail1024Outside': { $ne: null },
            'storedObjects.thumbnail256Outside': { $ne: null },
            $or: [{ 'dimensions.thumbnail1024Outside': null }, { 'dimensions.thumbnail256Outside': null }],
            isV2: true,
        };

        const count = await this._mediaRepository.countDocuments({ filter });

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `About to update ${count} documents`,
            initial: false,
        });

        if (!response.value) {
            console.log('exit...');
            process.exit(0);
        }

        const medias = await this._mediaRepository.find({
            filter,
            options: { lean: true, limit: 1000 },
        });

        for (const media of medias) {
            try {
                if (!media.storedObjects) {
                    console.log('not enough data', media._id.toString());
                    continue;
                }
                const thumbnail1024 = await axios.get(media.storedObjects.thumbnail1024Outside.publicUrl, { responseType: 'arraybuffer' });
                const thumbnail256 = await axios.get(media.storedObjects.thumbnail256Outside.publicUrl, { responseType: 'arraybuffer' });
                const metadata1024 = await sharp(thumbnail1024.data).metadata();
                const metadata256 = await sharp(thumbnail256.data).metadata();
                assert(metadata1024.width);
                assert(metadata1024.height);
                assert(metadata256.width);
                assert(metadata256.height);
                await this._mediaRepository.findOneAndUpdate({
                    filter: media._id,
                    update: {
                        'dimensions.thumbnail1024Outside': { width: metadata1024.width, height: metadata1024.height },
                        'dimensions.thumbnail256Outside': { width: metadata256.width, height: metadata256.height },
                    },
                });
            } catch (e) {
                console.log(`ERROR for media ${media._id.toString()}`, e);
            }
        }
    }
}

const task = container.resolve(AddThumbnailsDimensionsTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
