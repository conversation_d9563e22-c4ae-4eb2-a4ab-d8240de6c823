import 'reflect-metadata';

import ':env';

import prompts from 'prompts';
import { container } from 'tsyringe';

import { MediasRepository } from ':modules/media/medias.repository';

const mediaRepository = container.resolve(MediasRepository);

/**
 * Some video are flagged as { type: 'photo' }, this task fix that.
 */
(async () => {
    const filter = {
        filter: {
            'urls.original': RegExp('original.(mp4|MP4|mov|MOV)'),
            type: 'photo',
            isV2: { $ne: true },
        },
    };

    const count = await mediaRepository.countDocuments({ filter });

    const response = await prompts({
        type: 'confirm',
        name: 'value',
        message: `About to update ${count} documents`,
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    await mediaRepository.updateMany({ filter, update: { type: 'video' } });

    console.log('DONE');

    process.exit(0);
})();
