import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';

import { IMedia } from '@malou-io/package-models';

import { MediasRepository } from ':modules/media/medias.repository';

console.log('Modify the date filter');
process.exit(1);

/**
 * One-time task to fix thumbnails with 'undefined' in their URL instead of the S3 bucket name.
 * This issue was caused by a previously improperly executed task.
 */
@autoInjectable()
class UndefinedThumbnailUrlTask {
    constructor(private readonly _mediaRepository: MediasRepository) {}

    async execute() {
        const medias: Pick<IMedia, '_id' | 'thumbnail'>[] = await this._mediaRepository.find({
            filter: {
                createdAt: { $gte: new Date('2025-01-01') },
                type: 'video',
                thumbnail: /undefined/,
            },
            projection: { thumbnail: 1 },
            options: { lean: true },
        });
        console.log('medias', medias.length);
        for (const media of medias) {
            console.log('processing', media._id.toString());

            const thumbnail = media.thumbnail;

            const gotOnlyOneUndefined = thumbnail?.match(/undefined/g)?.length === 1;
            if (!gotOnlyOneUndefined) {
                console.log('more than one undefined');
                continue;
            }
            const isAtRightPlace = thumbnail?.match('s3.undefined.amazonaws')?.length === 1;
            if (!isAtRightPlace) {
                console.log('not at right place');
                continue;
            }

            const newThumbnail = thumbnail?.replace('undefined', 'eu-west-3');

            await this._mediaRepository.findOneAndUpdate({ filter: media._id, update: { thumbnail: newThumbnail } });
        }
    }
}

const task = container.resolve(UndefinedThumbnailUrlTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
