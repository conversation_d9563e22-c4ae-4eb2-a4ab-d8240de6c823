import 'reflect-metadata';

import { autoInjectable, container } from 'tsyringe';

import { MediaModel } from '@malou-io/package-models';

import { MediasRepository } from ':modules/media/medias.repository';

require(':env');

@autoInjectable()
class UpdateMediaSizesFromStringToNumberTask {
    executionStartedDate = new Date();

    constructor(private readonly _mediaRepository: MediasRepository) {}

    async execute() {
        console.log('***** Starting the execution of the update media sizes from string to number task *****');
        this.executionStartedDate = new Date();
        const BATCH_SIZE = 10000;
        let iteration = 0;

        const cursor = MediaModel.find({ 'sizes.original': { $type: 'string' } }, { _id: 1, sizes: 1 }, { lean: true }).cursor();

        await cursor.eachAsync(
            async (medias) => {
                console.log(
                    `Iteration n.${iteration + 1} - ${medias.length} medias to update... (${this._getExecutionTimeInMilliseconds()} ms)`
                );

                const bulkOperations = medias.map((media) => {
                    const sizes = {
                        original: media.sizes.original ? parseInt(media.sizes.original.toString(), 10) : undefined,
                        small: media.sizes.small ? parseInt(media.sizes.small.toString(), 10) : undefined,
                        cover: media.sizes.cover ? parseInt(media.sizes.cover.toString(), 10) : undefined,
                        smallCover: media.sizes.smallCover ? parseInt(media.sizes.smallCover.toString(), 10) : undefined,
                        igFit: media.sizes.igFit ? parseInt(media.sizes.igFit.toString(), 10) : undefined,
                    };

                    return {
                        updateOne: {
                            filter: { _id: media._id },
                            update: {
                                sizes,
                            },
                        },
                    };
                });

                if (bulkOperations.length > 0) {
                    await this._mediaRepository.bulkOperations({ operations: bulkOperations });
                    console.log(`Medias updated. (${this._getExecutionTimeInMilliseconds()} ms)`);
                }

                iteration++;
            },
            { batchSize: BATCH_SIZE, continueOnError: true }
        );

        console.log(`***** The update media sizes from string to number task ended in ${this._getExecutionTimeInMilliseconds()} ms *****`);
    }

    private _getExecutionTimeInMilliseconds(): number {
        const now = new Date();
        return now.getTime() - this.executionStartedDate.getTime();
    }
}

const task = container.resolve(UpdateMediaSizesFromStringToNumberTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
