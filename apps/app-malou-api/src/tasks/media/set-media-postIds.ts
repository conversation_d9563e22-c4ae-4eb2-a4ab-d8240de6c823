/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PostPublicationStatus } from '@malou-io/package-utils';

import { MediasRepository } from ':modules/media/medias.repository';
import PostsRepository from ':modules/posts/posts.repository';
import ':plugins/db';

@autoInjectable()
class DefaultTask {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _mediasRepository: MediasRepository
    ) {}

    async execute() {
        const posts = await this._postsRepository.find({
            filter: {
                restaurantId: toDbId('63175e4f262a75e11bbf9dac'),
                published: { $in: [PostPublicationStatus.PENDING, PostPublicationStatus.PUBLISHED] },
                socialCreatedAt: { $gte: new Date('2024-09-01') },
                attachments: { $exists: true, $not: { $size: 0 } },
            },
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });

        const x = posts.filter((p) => p.attachments?.length > 0);
        for (const post of x) {
            for (const attachment of post.attachments) {
                if (attachment.originalMediaId) {
                    const orginialMedia = await this._mediasRepository.findOne({
                        filter: { _id: attachment.originalMediaId },
                        options: { lean: true },
                    });
                    if (!orginialMedia) {
                        await this._mediasRepository.findOneAndUpdate({
                            filter: { _id: attachment._id },
                            update: { $addToSet: { postIds: post._id }, originalMediaId: null },
                        });
                        continue;
                    }
                }
                await this._mediasRepository.findOneAndUpdate({
                    filter: { _id: attachment._id },
                    update: { $addToSet: { postIds: post._id } },
                });
            }
        }

        console.log('media postIds updated !');
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
