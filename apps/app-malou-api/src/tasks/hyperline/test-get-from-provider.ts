/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import ':di';
import { container, singleton } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { GetRestaurantsFromProviderUseCase } from ':modules/restaurants/use-cases/get-restaurants-from-provider.use-case';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(private readonly _getRestaurantsFromProviderUseCase: GetRestaurantsFromProviderUseCase) {}

    async execute() {
        const result = await this._getRestaurantsFromProviderUseCase.execute({
            organizationId: '68daaa058c63afdcf6a19884',
            platformKey: PlatformKey.GMB,
            credentialId: '68db918cfcf6fa5e3861f312',
        });
        console.log('result :>> ', result);
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
