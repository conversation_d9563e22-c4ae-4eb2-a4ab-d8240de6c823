import { singleton } from 'tsyringe';

import { Config } from ':config';
import { GenericAiService, GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import {
    ChoosePostHashtagsPayload,
    GeneratePostSettingsPayload,
    GenerateSeoPostTextAdvancedSettingsPayload,
    GenerateSeoPostTextPayload,
    GenerateSocialNetworkPostTextAdvancedSettingsPayload,
    GenerateSocialNetworkPostTextPayload,
    OptimizeSeoPostTextAdvancedSettingsPayload,
    OptimizeSeoPostTextPayload,
    OptimizeSocialNetworkPostTextAdvancedSettingsPayload,
    OptimizeSocialNetworkPostTextPayload,
    TranslateTextPayload,
} from ':modules/ai/interfaces/ai.interfaces';
import ':modules/ai/services';

export interface AiTranslationsText {
    french: string;
    english: string;
    spanish: string;
    italian: string;
}

export interface AiPostGenerationResponse {
    captions: string[];
}

export interface AiPostSettingsGenerationResponse {
    tone: string[];
    length: string;
    containsEmojis: string;
    denomination: string;
}

type Payload =
    | GenerateSocialNetworkPostTextPayload
    | GenerateSocialNetworkPostTextAdvancedSettingsPayload
    | OptimizeSocialNetworkPostTextPayload
    | OptimizeSocialNetworkPostTextAdvancedSettingsPayload
    | GenerateSeoPostTextPayload
    | GenerateSeoPostTextAdvancedSettingsPayload
    | OptimizeSeoPostTextPayload
    | OptimizeSeoPostTextAdvancedSettingsPayload
    | ChoosePostHashtagsPayload
    | TranslateTextPayload
    | GeneratePostSettingsPayload;

@singleton()
export class AiTextGenerationService<T extends Payload> {
    async generateCompletion(
        payload: T
    ): Promise<GenericAiServiceResponseType<string | AiTranslationsText | AiPostGenerationResponse | AiPostSettingsGenerationResponse>> {
        const AiService = new GenericAiService<
            T,
            string | AiTranslationsText | AiPostGenerationResponse | AiPostSettingsGenerationResponse
        >({
            lambdaUrl: Config.services.aiTextGenerationService.functionName,
        });
        return AiService.generateCompletion(payload);
    }
}
