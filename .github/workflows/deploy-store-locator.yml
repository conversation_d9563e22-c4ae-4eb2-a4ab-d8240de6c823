name: Deploy Store Locator

on:
    workflow_dispatch:
        inputs:
            organizationId:
                description: 'Organization ID'
                required: true
                type: string
            baseApiUrl:
                description: 'API Base URL'
                type: string
            environment:
                type: choice
                description: 'Environment'
                required: true
                default: 'development'
                options:
                    - 'test'
                    - 'development'
                    - 'staging'
                    - 'production'
            jobId:
                description: 'Deployment job ID (optional)'
                type: string

run-name: Env ${{ inputs.environment }}, Org ${{ inputs.organizationId }}

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}-${{ inputs.environment }}-${{ inputs.organizationId }}
    cancel-in-progress: true

env:
    GIT_PKG_ACCESS_TOKEN: ${{ secrets.PAT }}
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    set-up-env-vars:
        name: Setup environment variables
        runs-on: ubuntu-latest
        outputs:
            api_base_url: ${{ steps.set-vars.outputs.api_base_url }}
            malou_api_key_env: ${{ steps.set-vars.outputs.malou_api_key_env }}
        steps:
            - id: set-vars
              name: Set environment variables depending on environment
              run: |
                  # Determine baseApiUrl
                  if [ -n "${{ inputs.baseApiUrl }}" ]; then
                    BASE_API_URL="${{ inputs.baseApiUrl }}"
                  elif [ "${{ inputs.environment }}" = "production" ] || [ "${{ inputs.environment }}" = "test" ]; then
                    BASE_API_URL="https://app.api.malou.io/api/v1"
                  elif [ "${{ inputs.environment }}" = "staging" ]; then
                    BASE_API_URL="https://staging.api.malou.io/api/v1"
                  else
                    BASE_API_URL="https://development.api.malou.io/api/v1"
                  fi
                  echo "api_base_url=$BASE_API_URL" >> $GITHUB_OUTPUT

                  if [ "${{ inputs.environment }}" = "production" ] || [ "${{ inputs.environment }}" = "test" ]; then
                    echo "malou_api_key_env=MALOU_API_KEY_PRODUCTION" >> $GITHUB_OUTPUT
                  elif [ "${{ inputs.environment }}" = "staging" ]; then
                    echo "malou_api_key_env=MALOU_API_KEY_STAGING" >> $GITHUB_OUTPUT
                  else
                    echo "malou_api_key_env=MALOU_API_KEY_DEVELOPMENT" >> $GITHUB_OUTPUT
                  fi

    fetch-api:
        name: Fetch configuration from API
        runs-on: ubuntu-latest
        needs: [set-up-env-vars]
        outputs:
            cloudfront_distribution_id: ${{ steps.get_api_data.outputs.cloudfront_distribution_id }}
            s3_bucket_name: ${{ steps.get_api_data.outputs.s3_bucket_name }}
            organization_name: ${{ steps.get_api_data.outputs.organization_name }}
            base_url: ${{ steps.get_api_data.outputs.base_url }}
        env:
            STORE_LOCATOR_CONFIGURATION_URL: ${{ needs.set-up-env-vars.outputs.api_base_url }}/store-locator/${{ inputs.organizationId }}/configuration
        steps:
            - name: Call API and parse response
              id: get_api_data
              run: |
                  # Call API
                  RESPONSE=$(curl -s "${{ env.STORE_LOCATOR_CONFIGURATION_URL }}?api_key=${{ secrets[needs.set-up-env-vars.outputs.malou_api_key_env] }}")

                  # Parse response
                  CLOUDFRONT_DISTRIBUTION_ID=$(echo "$RESPONSE" | jq -r '.data.cloudfrontDistributionId')
                  ORGANIZATION_NAME=$(echo "$RESPONSE" | jq -r '.data.organizationName')
                  BASE_URL=$(echo "$RESPONSE" | jq -r '.data.baseUrl')
                  S3_BUCKET_NAME=$(echo "$RESPONSE" | jq -r '.data.s3BucketName')

                  # Override values if environment is not production
                  if [ "${{ inputs.environment }}" = "test" ]; then
                    CLOUDFRONT_DISTRIBUTION_ID="E2H0RYJ95M33U7"
                    S3_BUCKET_NAME="s3://store-locator-qa"
                  elif [ "${{ inputs.environment }}" = "staging" ] || [ "${{ inputs.environment }}" = "development" ]; then
                    CLOUDFRONT_DISTRIBUTION_ID="EKW2DD8GQMQYE"
                    S3_BUCKET_NAME="s3://store-locator-all-env-qa"
                  fi

                  echo "Response from API: $RESPONSE"
                  echo "CLOUDFRONT_DISTRIBUTION_ID: $CLOUDFRONT_DISTRIBUTION_ID"
                  echo "ORGANIZATION_NAME: $ORGANIZATION_NAME"
                  echo "BASE_URL: $BASE_URL"
                  echo "S3_BUCKET_NAME: $S3_BUCKET_NAME"

                  # Export them as outputs
                  echo "cloudfront_distribution_id=$CLOUDFRONT_DISTRIBUTION_ID" >> $GITHUB_OUTPUT
                  echo "organization_name=$ORGANIZATION_NAME" >> $GITHUB_OUTPUT
                  echo "base_url=$BASE_URL" >> $GITHUB_OUTPUT
                  echo "s3_bucket_name=$S3_BUCKET_NAME" >> $GITHUB_OUTPUT

    deploy:
        runs-on: runs-on=${{ github.run_id }}/cpu=16/ram=32+64/family=c6in.*+m7g.*+m7gd.*+m6gd.*+g4dn.*+m6g.*+c7i.*/image=ubuntu24-full-x64/spot=price-capacity-optimized/retry=when-interrupted/extras=s3-cache
        name: Deploy Store Locator
        needs: [set-up-env-vars, fetch-api]
        outputs:
            status: ${{ job.status }}
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.ref_name }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install --filter @malou-io/store-locator...

            - name: Astro caching
              uses: actions/cache/restore@v4
              with:
                  path: |
                      apps/store-locator/.astro
                      apps/store-locator/node_modules/.vite
                  key: ${{ runner.os }}-store-locator-${{ inputs.organizationId }}-${{ inputs.environment }}
                  restore-keys: |
                      ${{ runner.os }}-store-locator-${{ inputs.organizationId }}-

            - name: Build dependencies
              run: pnpm run build --filter=@malou-io/store-locator... --cache-dir=.turbo

            - name: Define deployment variables
              id: deployment-variables
              run: |
                  if [ "${{ inputs.environment }}" = "test" ]; then
                    S3_BUCKET_NAME="s3://store-locator-qa"
                    CLOUDFRONT_DISTRIBUTION_ID="E2H0RYJ95M33U7"
                  else
                    S3_BUCKET_NAME="s3://store-locator-${{ inputs.environment }}-org-${{ inputs.organizationId }}"
                    CLOUDFRONT_DISTRIBUTION_ID="${{ needs.fetch-api.outputs.cloudfront_distribution_id }}"
                  fi

                  echo "s3_bucket_name=$S3_BUCKET_NAME" >> $GITHUB_OUTPUT
                  echo "cloudfront_distribution_id=$CLOUDFRONT_DISTRIBUTION_ID" >> $GITHUB_OUTPUT

            - name: Build Astro pages
              run: pnpm run deploy
              working-directory: apps/store-locator
              env:
                  ORGANIZATION_ID: ${{ inputs.organizationId }}
                  API_BASE_URL: ${{ needs.set-up-env-vars.outputs.api_base_url }}
                  API_KEY: ${{ secrets[needs.set-up-env-vars.outputs.malou_api_key_env] }}
                  ORGANIZATION_BASE_URL: ${{ needs.fetch-api.outputs.base_url }}
                  ORGANIZATION_NAME: ${{ needs.fetch-api.outputs.organization_name }}
                  ENVIRONMENT: ${{ inputs.environment }}

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Deploy to S3
              working-directory: apps/store-locator
              # Sync fingerprinted assets with long-term cache
              run: |
                  aws s3 sync ./dist ${{ needs.fetch-api.outputs.s3_bucket_name }} \
                    --acl public-read  \
                    --follow-symlinks \
                    --cache-control "public, max-age=31536000, immutable" \
                    --exclude "*" \
                    --include "_astro/*" \
                    --include "chunks/*" \
                    --delete 

                  aws s3 sync ./dist ${{ needs.fetch-api.outputs.s3_bucket_name }} \
                    --acl public-read  \
                    --follow-symlinks \
                    --cache-control "public, no-cache, must-revalidate" \
                    --exclude "_astro/*" \
                    --exclude "chunks/*" \
                    --delete

            # actions/cache@v4 won't save the cache afterwards if there's a cache hit
            # But we want to save it no matter what, so we have to do it manually, by using restore then save
            - name: Astro cache update
              uses: actions/cache/save@v4
              with:
                  path: |
                      .astro
                      node_modules/.vite
                  key: ${{ runner.os }}-store-locator-${{ inputs.organizationId }}-${{ inputs.environment }}

            # actions/cache@v4 won't save the cache afterwards if there's a cache hit
            # But we want to save it no matter what, so we have to do it manually, by using restore then save
            - name: Astro cache update
              uses: actions/cache/save@v4
              with:
                  path: |
                      apps/store-locator/.astro
                      apps/store-locator/node_modules/.vite
                  key: ${{ runner.os }}-store-locator-${{ inputs.organizationId }}-${{ inputs.environment }}

    update-job-status:
        name: Update job status
        runs-on: ubuntu-latest
        needs: [set-up-env-vars, deploy]
        if: ${{ always() && inputs.jobId != '' }}
        steps:
            - name: Update job status
              run: |
                  curl -X POST "${{ needs.set-up-env-vars.outputs.api_base_url }}/store-locator/deployment/update-status?api_key=${{ secrets[needs.set-up-env-vars.outputs.malou_api_key_env] }}" \
                    -H "Content-Type: application/json" \
                    -d '{"jobId": "${{ inputs.jobId }}", "status": "${{ needs.deploy.result }}", "workflowRunId": "${{ github.run_id }}"}'

    slack-notification:
        needs: [fetch-api, deploy]
        if: ${{ always() && (needs.deploy.result == 'success' || needs.deploy.result == 'failure') }}
        name: Send Slack notification
        runs-on: ubuntu-latest
        steps:
            - name: Set Slack channel ID
              id: get-slack-channel-id
              run: |
                  if [ "${{ inputs.environment }}" = "production" ]; then
                    echo "SLACK_CHANNEL_ID=${{ secrets.STORE_LOCATOR_SLACK_CHANNEL_ID_PROD }}" >> $GITHUB_OUTPUT
                  else
                    echo "SLACK_CHANNEL_ID=${{ secrets.STORE_LOCATOR_SLACK_CHANNEL_ID }}" >> $GITHUB_OUTPUT
                  fi

            - name: Send notification
              uses: slackapi/slack-github-action@v2.0.0
              with:
                  method: chat.postMessage
                  token: ${{ secrets.SLACK_BOT_TOKEN }}
                  payload: |
                      channel: ${{ steps.get-slack-channel-id.outputs.SLACK_CHANNEL_ID }}
                      attachments:
                        - color: ${{ needs.deploy.result == 'success' && '"#2eb886"' || (needs.deploy.result == 'failure' && '"#e01e5a"' || '"#cccccc"') }}
                          blocks:
                            - type: section
                              text:
                                type: mrkdwn
                                text: "*🚀 Deploy status: ${{ needs.deploy.result == 'success' && '✅ Success' || needs.deploy.result == 'failure' && '❌ Failure' || needs.deploy.result == 'cancelled' && '⚪ Cancelled' || 'Unknown' }}*"

                            - type: context
                              elements:
                                - type: mrkdwn
                                  text: "Triggered for: Organization `${{ needs.fetch-api.outputs.organization_name }}` | ID `${{ inputs.organizationId }}` | Environment: `${{ inputs.environment }}` | Branch: `${{ github.ref_name }}`"


                                - type: mrkdwn
                                  text: "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
