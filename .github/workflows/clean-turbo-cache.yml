name: Clean Turbo Cache

on:
    workflow_dispatch:
    schedule:
        - cron: '0 3 * * THU'

jobs:
    clean-cache:
        name: Clean cache
        runs-on: runs-on=${{ github.run_id }}/cpu=2/ram=4/family=c6id.*+c7i+m7i-flex+c5d.*+t2.*+m7i.*/image=ubuntu24-full-x64/spot=price-capacity-optimized/retry=when-interrupted/extras=s3-cache
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/blob/master/.github/workflows/deploy.yml
            - name: Turbo cache
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-main

            - name: Clean cache
              run: rm -rf .turbo/*

            # actions/cache@v4 won't save the cache afterwards if there's a cache hit
            # But we want to save it no matter what, so we have to do it manually, by using restore then save
            - name: Turbo cache save
              uses: actions/cache/save@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-main
