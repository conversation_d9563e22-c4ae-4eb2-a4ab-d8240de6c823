name: Playwright Tests
on:
    workflow_dispatch:
        inputs:
            apps:
                type: choice
                description: 'On which envs should tests be run?'
                required: true
                default: 'staging'
                options:
                    - 'development'
                    - 'staging'
                    - 'production'
    ## Run the tests every day at 7am
    schedule:
        - cron: '0 7 * * *'

run-name: Playwright Tests ${{ inputs.apps && inputs.apps || 'production 7AM' }}

env:
    TOTAL_SHARDS: 5
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    setup:
        name: Setup
        runs-on: ubuntu-latest
        outputs:
            shard_matrix: ${{ steps.generate-matrix.outputs.matrix }}
            shard_total: ${{ steps.generate-matrix.outputs.total }}
        steps:
            - name: Generate Shard Matrix
              id: generate-matrix
              run: |
                  # Get the total number of shards from the environment
                  TOTAL_SHARDS=${{ env.TOTAL_SHARDS}}

                  # Generate an array of shard indices dynamically
                  # MATRIX = [1, 2, 3, 5]
                  # SHARD_TOTAL = [5]
                  MATRIX=$(seq 1 $TOTAL_SHARDS | jq -R . | jq -s . | tr -d '[:space:]')
                  SHARD_TOTAL="[${TOTAL_SHARDS}]"
                  echo "matrix=$MATRIX"
                  echo "total=$SHARD_TOTAL"

                  # Output the matrix as a string
                  echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
                  echo "total=$SHARD_TOTAL" >> $GITHUB_OUTPUT

    playwright-tests:
        name: Playwright tests
        needs: [setup]
        timeout-minutes: 10
        runs-on: runs-on=${{ github.run_id }}/cpu=8/ram=16+32/family=c6gn.*+c7i-flex.*+c6in.*+t4g.*+t2.*+t3.*+m7gd.*+c7i.*/image=ubuntu24-full-x64/spot=price-capacity-optimized/retry=when-interrupted/extras=s3-cache
        strategy:
            fail-fast: false
            matrix:
                shardIndex: ${{ fromJson(needs.setup.outputs.shard_matrix) }}
                shardTotal: ${{ fromJson(needs.setup.outputs.shard_total) }}
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Get latest release branch
              if: ${{ github.event_name == 'schedule' }}
              id: get_latest_release
              uses: ./.github/actions/get-latest-release-branch
              with:
                  token: ${{ secrets.GITHUB_TOKEN }}

            - name: Checkout latest release branch
              if: ${{ github.event_name == 'schedule' }}
              uses: actions/checkout@v4
              with:
                  ref: ${{ steps.get_latest_release.outputs.latest_release_branch }}
                  fetch-depth: 0

            - name: Install Dependencies
              run: pnpm install --filter @malou-io/app-e2e... --frozen-lockfile

            - name: Install Playwright Browsers
              ## only install chromium because it's the only browser we use now
              run: cd apps/app-malou-e2e && pnpm exec playwright install chromium

            - name: Set env variables
              id: set-env
              run: |
                  touch apps/app-malou-e2e/.env
                  printf ${{ secrets.E2E_TESTS_ENV_FILE }} | base64 --decode > apps/app-malou-e2e/.env

            # Restore cache only
            - name: Turbo cache
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-main
                  restore-keys: ${{ runner.os }}-turbo-

            - name: Build Playwright tests
              run: pnpm run build --cache-dir=.turbo --filter @malou-io/app-e2e

            - name: Run Playwright tests
              env:
                  NODE_ENV: ${{ github.event.schedule && 'production' || inputs.apps }}
                  TESTING_TARGET: ${{ github.event.schedule && 'production' || inputs.apps }}
              run: |
                  cd apps/app-malou-e2e && npx playwright test --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }} --trace on > test_output.txt || true

                  if [ ! -s test_output.txt ]; then
                    echo "0 passed" > test_output.txt
                    echo "0 failed" >> test_output.txt
                    echo "0 flaky" >> test_output.txt
                  fi

                  passed=$(grep -Eo '([0-9]+) passed' test_output.txt | awk '{print $1}')
                  failed=$(grep -Eo '([0-9]+) failed' test_output.txt | awk '{print $1}')
                  flaky=$(grep -Eo '([0-9]+) flaky' test_output.txt | awk '{print $1}')

                  passed=${passed:-0}
                  failed=${failed:-0}
                  flaky=${flaky:-0}

                  echo "Passed: $passed, Failed: $failed, Flaky: $flaky"

                  # Store results in a file
                  echo "$passed" > results-passed-${{ matrix.shardIndex }}.txt
                  echo "$failed" > results-failed-${{ matrix.shardIndex }}.txt
                  echo "$flaky" > results-flaky-${{ matrix.shardIndex }}.txt

            - name: Upload blob report to GitHub Actions Artifacts
              if: ${{ !cancelled() }}
              uses: actions/upload-artifact@v4
              with:
                  name: blob-report-${{ matrix.shardIndex }}
                  path: apps/app-malou-e2e/blob-report
                  retention-days: 1

            - name: Upload test results
              if: ${{ !cancelled() }}
              uses: actions/upload-artifact@v4
              with:
                  name: test-results-${{ matrix.shardIndex }}
                  path: |
                      apps/app-malou-e2e/results-passed-${{ matrix.shardIndex }}.txt
                      apps/app-malou-e2e/results-failed-${{ matrix.shardIndex }}.txt
                      apps/app-malou-e2e/results-flaky-${{ matrix.shardIndex }}.txt
                  retention-days: 1

    merge-reports:
        name: Merge reports
        if: ${{ !cancelled() }}
        needs: [playwright-tests]
        runs-on: ubuntu-latest
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Install dependencies
              run: pnpm install --filter @malou-io/app-e2e... --frozen-lockfile

            - name: Download blob reports from GitHub Actions Artifacts
              uses: actions/download-artifact@v4
              with:
                  path: apps/app-malou-e2e/all-blob-reports
                  pattern: blob-report-*
                  merge-multiple: true

            - name: Download test results
              uses: actions/download-artifact@v4
              with:
                  pattern: test-results-*

            - name: Aggregate test results
              run: |
                  total_passed=0
                  total_failed=0
                  total_flaky=0

                  for shard in {1..${{ env.TOTAL_SHARDS }}}; do
                      passed=$(cat test-results-$shard/results-passed-$shard.txt)
                      failed=$(cat test-results-$shard/results-failed-$shard.txt)
                      flaky=$(cat test-results-$shard/results-flaky-$shard.txt)

                      total_passed=$((total_passed + passed))
                      total_failed=$((total_failed + failed))
                      total_flaky=$((total_flaky + flaky))
                  done

                  echo "Total Passed: $total_passed, Total Failed: $total_failed, Total Flaky: $total_flaky"

                  # Store totals in environment variables
                  echo "TOTAL_PASSED=$total_passed" >> $GITHUB_ENV
                  echo "TOTAL_FAILED=$total_failed" >> $GITHUB_ENV
                  echo "TOTAL_FLAKY=$total_flaky" >> $GITHUB_ENV

            - name: Merge into HTML Report
              working-directory: apps/app-malou-e2e
              run: pnpm exec playwright merge-reports --reporter html ./all-blob-reports

            - name: Configure AWS Credentials
              uses: aws-actions/configure-aws-credentials@v4
              if: always()
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: 'eu-west-3'

            - name: Upload to S3 bucket
              id: S3
              if: always()
              env:
                  REPORT_DIR: 'playwright-report-${{ github.run_id }}'
              run: |
                  echo "REPORT_DIR=$REPORT_DIR" >> $GITHUB_ENV
                  aws s3 cp apps/app-malou-e2e/playwright-report/. s3://malou-playwright-traces/$REPORT_DIR --recursive

            - name: Generate Report URL
              id: generate_report_url
              run: echo "::set-output name=report_url::https://djj1pfvigd2g9.cloudfront.net/${{ env.REPORT_DIR }}/index.html"

            - name: Setup Job Summary
              if: always()
              run: |
                  echo " 🔗 [View Playwright Report](${{ steps.generate_report_url.outputs.report_url }})" >> $GITHUB_STEP_SUMMARY

            - name: Slack notification
              if: always()
              id: slack
              uses: slackapi/slack-github-action@v1.27.0
              with:
                  payload: |
                      {
                          "reportUrl": "${{ steps.generate_report_url.outputs.report_url }}",
                          "env": "${{ github.event.schedule && 'production' || inputs.apps }}",
                          "totalPassed": "${{ env.TOTAL_PASSED }}",
                          "totalFailed": "${{ env.TOTAL_FAILED }}",
                          "totalFlaky": "${{ env.TOTAL_FLAKY }}"
                      }
              env:
                  SLACK_WEBHOOK_URL: 'https://hooks.slack.com/triggers/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'
