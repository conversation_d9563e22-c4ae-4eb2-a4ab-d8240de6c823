name: Deploy AWS services

on:
    workflow_dispatch:
        inputs:
            environment:
                type: choice
                description: 'Environment'
                required: true
                default: 'test'
                options:
                    - 'test'
                    - 'development'
                    - 'staging'
                    - 'production'

run-name: Deploy AWS services to ${{ inputs.environment }} environment

env:
    # GIT_PKG_ACCESS_TOKEN: ${{ secrets.PAT }}
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    deploy:
        runs-on: runs-on=${{ github.run_id }}/cpu=16/ram=32+64/family=c6in.*+m7g.*+m7gd.*+m6gd.*+g4dn.*+m6g.*+c7i.*/image=ubuntu24-full-x64/spot=price-capacity-optimized/retry=when-interrupted/extras=s3-cache
        name: Deploy AWS services
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Install dependencies
              run: pnpm install

            - name: Install Docker
              uses: docker/setup-buildx-action@v3

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Set up env variables
              working-directory: services/aws
              run: |
                  echo "Creating .env.${{ inputs.environment }} file"

                  # Conditionally select the secret based on the environment
                  if [[ "${{ inputs.environment }}" == "test" ]]; then
                    echo "Using development environment variables"
                    ENV_VARIABLES="${{ secrets.AWS_SERVICES_ENV_VARIABLES_DEVELOPMENT }}"
                  elif [[ "${{ inputs.environment }}" == "development" ]]; then
                    echo "Using development environment variables"
                    ENV_VARIABLES="${{ secrets.AWS_SERVICES_ENV_VARIABLES_DEVELOPMENT }}"
                  elif [[ "${{ inputs.environment }}" == "staging" ]]; then
                    echo "Using staging environment variables"
                    ENV_VARIABLES="${{ secrets.AWS_SERVICES_ENV_VARIABLES_STAGING }}"
                  elif [[ "${{ inputs.environment }}" == "production" ]]; then
                    echo "Using production environment variables"
                    ENV_VARIABLES="${{ secrets.AWS_SERVICES_ENV_VARIABLES_PRODUCTION }}"
                  else
                    echo "Invalid environment specified"
                    exit 1
                  fi

                  # Write the environment variables into the .env file
                  printf "%s" "$ENV_VARIABLES" > .env.${{ inputs.environment }}

            - name: Deploy AWS services
              working-directory: services/aws
              env:
                  NODE_ENV: ${{ inputs.environment }}
              run: pnpm run deploy --require-approval never

    slack-notification:
        needs: [deploy]
        if: ${{ always() && (needs.deploy.result == 'success' ) }}
        name: Send Slack notification
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - name: Send Slack notification
              uses: ./.github/actions/send-slack-notification
              with:
                  notification-origin: malou-${{ inputs.environment }}-aws-services
                  slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
                  github-token: ${{ secrets.GITHUB_TOKEN }}
