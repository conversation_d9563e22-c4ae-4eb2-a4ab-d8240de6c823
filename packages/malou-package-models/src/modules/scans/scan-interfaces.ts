import { OverwriteOrAssign } from ':core/index';
import { INfc } from ':modules/nfcs/nfc-model';
import { IReview } from ':modules/reviews/review-model';
import { IScan } from ':modules/scans/scan-model';

export type IScanWithNfc = OverwriteOrAssign<IScan, { nfc: INfc }>;
export type IScanWithReview = OverwriteOrAssign<IScan, { matchedReview?: IReview }>;
export type IScanForAggregatedInsights = Pick<IScan, '_id' | 'nfcId' | 'matchedReviewSocialId'> & {
    nfcSnapshot: nfcSnapshotForScanForInsights;
};
export type IScanForRestaurantInsightsWithReviewRating = OverwriteOrAssign<
    IScanForRestaurantInsights,
    { matchedReview?: Pick<IReview, '_id' | 'rating'> }
>;

type nfcSnapshotForScanForInsights = Pick<IScan['nfcSnapshot'], 'name' | 'chipName' | 'restaurantId' | 'platformKey' | 'redirectionLink'>;
type IScanForRestaurantInsights = Pick<IScan, '_id' | 'nfcId' | 'matchedReviewSocialId' | 'scannedAt' | 'starClicked'> & {
    nfcSnapshot: nfcSnapshotForScanForInsights;
};
