import { IFeedback } from ':modules/feedbacks/feedback-model';
import { IMedia } from ':modules/media/media-model';
import { IPlatform } from ':modules/platforms/platform-model';
import { IRestaurant } from ':modules/restaurants/restaurant-model';

export interface VirtualPost {
    restaurant: IRestaurant;
    feedback: IFeedback;
    duplicatedFromRestaurant: IRestaurant;
    platform: IPlatform;
    attachments: IMedia[];
    thumbnail: IMedia;

    /**
     * @deprecated use virtual instead
     */
    duplicatedFromRestaurantId: IRestaurant;
}
