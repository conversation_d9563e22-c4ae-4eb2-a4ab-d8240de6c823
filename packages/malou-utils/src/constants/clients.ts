export enum ProviderClientSource {
    MANUAL = 'manual',
    MALOU = 'malou',
    ZENCHEF = 'zenchef',
    ZELTY = 'zelty',
    LAFOURCHETTE = 'lafourchette',
    PULP = 'pulp',
    WHEEL_OF_FORTUNE = 'wheel_of_fortune',
}

export enum ProviderClientCivility {
    MALE = 'male',
    FEMALE = 'female',
    OTHER = 'other',
}

export interface ProviderClientPhone {
    prefix: number;
    digits: number;
}
