import { Body, Container, Font, Head, Html, Tailwind } from '@react-email/components';

import { EmailPadding } from '@malou-io/package-utils';

import { MalouFooter } from ':shared/components/layout/footer/footer';
import { <PERSON>ouBasicHeader } from ':shared/components/layout/header';
import { tailwindConfig } from ':shared/tailwind-config';
import { MalouLayoutProps } from ':shared/types';
import { EmailMargin } from ':shared/types/report.enum';

export const MalouLayout = ({
    children,
    context,
    showFooter,
    showHeader,
    customHeader,
    paddingX,
    emailMarginY,
    footerMarginTop,
}: MalouLayoutProps) => (
    <Html>
        <Tailwind config={tailwindConfig as any}>
            <Head>
                <Font
                    fontFamily="Roboto"
                    fallbackFontFamily="Verdana"
                    webFont={{
                        url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                        format: 'woff2',
                    }}
                    fontWeight={400}
                    fontStyle="normal"
                />
                <Font
                    fontFamily="Roboto"
                    fallbackFontFamily="Verdana"
                    webFont={{
                        url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                        format: 'woff2',
                    }}
                    fontWeight={100}
                />
                <Font
                    fontFamily="Roboto"
                    fallbackFontFamily="Verdana"
                    webFont={{
                        url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',
                        format: 'woff2',
                    }}
                    fontWeight={600}
                    fontStyle="normal"
                />
                <style>
                    {
                        /* CSS */ `
    
                p {
                    font-size: 14px;
                    line-height: 24px;
                    color: ${tailwindConfig.theme.extend.colors['malou-color-text-1']};
                    word-break: break-word;
                    font-weight: 400;
                }

                .malou-text-ellipsis {
                    width: 160px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                @media only screen and (max-width: 600px) {
                    #malouBody {
                        padding: 0px !important;
                    }
                    .malou-visible {
                        display: block!important;
                    }
                    .malou-visible-inline-table {
                        display: inline-table!important;
                    }
                    .malou-hidden {
                        display: none!important;
                    }
                    .malou-review-rating-width {
                        width: 65px!important;
                    }
                    .malou-width-100 {
                        width: 150px!important;
                    }
                    .malou-text-ellipsis {
                        width: 180px!important;
                    }
                }
            `
                    }
                </style>
            </Head>

            <Body className={`font-sans`} id="malouBody" style={{ background: '#F9FAFF', padding: '10px' }}>
                <Container className="my-[40px] !max-w-[600px] rounded border border-solid border-malou-color-border-primary bg-white ">
                    {showHeader && (customHeader || <MalouBasicHeader locale={context.locale} trackingUrl={context.trackingUrl} />)}
                    <Container className={`${getMarginY(emailMarginY)} !max-w-[600px]`}>
                        <Container className={`${getPaddingX(paddingX)} !max-w-[600px]`}>{children}</Container>
                    </Container>
                    {showFooter && (
                        <MalouFooter
                            locale={context.locale}
                            unsubscribeLink={context.unsubscribeLink}
                            footerMarginTop={footerMarginTop}></MalouFooter>
                    )}
                </Container>
            </Body>
        </Tailwind>
    </Html>
);

const getPaddingX = (padding: EmailPadding = EmailPadding.MEDIUM): string =>
    padding === EmailPadding.HIGH ? 'px-[5vw]' : padding === EmailPadding.MEDIUM ? 'px-[3.5vw]' : 'px-[0vw]';

const getMarginY = (margin: EmailMargin = EmailMargin.MEDIUM): string =>
    margin === EmailMargin.HIGH ? 'my-[40px]' : margin === EmailMargin.MEDIUM ? 'my-[20px]' : 'my-[0px]';

MalouLayout.defaultProps = {
    padding: EmailPadding.HIGH,
    showFooter: true,
    showHeader: true,
    paddingX: EmailPadding.HIGH,
    emailMarginY: EmailMargin.HIGH,
    footerMarginTop: EmailMargin.HIGH,
};
