import { Text } from '@react-email/components';
import { DateTime } from 'luxon';

import { SubscriptionRequestProps } from '@malou-io/package-dto';

import { Hello, MalouLayout } from ':shared/components';

export const subscriptionRequestTemplate = (props: SubscriptionRequestProps) => {
    const { locale, user, organizationName } = props;
    const now = DateTime.now().toFormat('dd/MM/yyyy à HH:mm');
    return (
        <MalouLayout context={{ locale }}>
            <Hello locale={locale} />
            <Text>{`${organizationName} à 
              fait une demande d'informations sur le Store locator depuis la MalouApp le ${now}. Contacte ${user.fullName} - ${
                  user.email
              } pour lui proposer un abonnement.`}</Text>
        </MalouLayout>
    );
};

export default subscriptionRequestTemplate;
