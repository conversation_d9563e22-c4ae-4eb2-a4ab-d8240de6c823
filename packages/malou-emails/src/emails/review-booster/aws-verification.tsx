import { Text } from '@react-email/components';

import { BaseEmailProps } from '@malou-io/package-dto';

import { Hello, MalouLayout } from ':shared/components';
import { Translation } from ':shared/services';

interface Props extends BaseEmailProps {}

export const AwsVerificationMailTemplate = ({ locale, receiver = 'User' }: Props) => {
    const translator = new Translation(locale).getTranslator();
    return (
        <MalouLayout context={{ locale, receiver }} showFooter={false}>
            <Hello locale={locale} receiver={receiver} />
            <Text>{translator.review_booster.verification.content()}</Text>
        </MalouLayout>
    );
};

export default AwsVerificationMailTemplate;
