import { EmailPadding, Locale } from '@malou-io/package-utils';

import { MalouLayout } from ':shared/components';
import { EmailMargin } from ':shared/types/report.enum';

import type { ClientReviewBoosterProps } from './content';
import { Content } from './content';

export const ClientReviewBoosterMailTemplate = (props: ClientReviewBoosterProps) => {
    const { locale, unsubscribeLink, trackingUrl } = props;
    return (
        <MalouLayout
            showHeader={false}
            showFooter={false}
            context={{ locale, unsubscribeLink, trackingUrl }}
            emailMarginY={EmailMargin.LOW}
            paddingX={EmailPadding.LOW}>
            <Content {...props} />
        </MalouLayout>
    );
};

ClientReviewBoosterMailTemplate.defaultProps = {
    locale: Locale.EN,
    restaurantName: 'Bon gueuleton',
    restaurantLogo: 'https://picsum.photos/200/300',
    campaignText:
        'Merci d\'avoir commandé chez nous ! <a style="color: red;" href="https://malou.io"> <ins>**HAHA**</ins> </a> [mdr](https://malou.io)',
    unsubscribeLink: 'https://malou.io',
    campaignSubject: 'Comment avez-vous trouvé votre repas ?',
    starLink: 'https://malou.io',
};

export default ClientReviewBoosterMailTemplate;
