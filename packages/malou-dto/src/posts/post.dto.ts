import {
    DeviceType,
    HashtagType,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    PublicationErrorCode,
    SeoPostTopic,
    SocialAttachmentsMediaTypes,
    TiktokPrivacyStatus,
} from '@malou-io/package-utils';

import { MediaDto } from '../media';

export interface PostDto {
    _id: string;
    platformId: string;
    socialId: string;
    socialCreatedAt: Date;
    socialLink: string;
    socialUpdatedAt: Date;
    socialAttachments: SocialAttachment[];
    attachments?: MediaDto[];
    createdAt: Date;
    hashtags: PostHashtags;
    isReelDisplayedInFeed: boolean;
    key: PlatformKey;
    keys: PlatformKey[];
    postTopic: SeoPostTopic;
    postType: PostType;
    published: PostPublicationStatus;
    isPublishing?: boolean;
    restaurantId: string;
    shouldDuplicateInOtherPlatforms?: boolean;
    source: PostSource;
    text: string;
    tries?: number;
    updatedAt: string;
    userTags: UserTagsList[];
    userTagsList: UserTagsList[][];
    language?: string;
    plannedPublicationDate: Date;
    author: Author;
    authors?: Author[];
    bindingId?: string;
    duplicatedFromRestaurantId?: string;
    callToAction?: CallToAction | null;
    event?: Event;
    location?: PostSchemaLocation | null;
    offer?: Offer;
    attachmentsName?: string | null;
    feedbackId?: string;
    isStory: boolean;
    title?: string;
    errorData?: string | null;
    errorStage?: string | null;
    publicationErrors?: PublicationError[];
    malouStoryId?: string;
    keywordAnalysis?: KeywordAnalysis;
    thumbnail?: string | null;
    thumbnailOffsetTimeInMs?: number | null;
    tiktokPublishId?: string | null;
    tiktokOptions?: TiktokOptions;
    tiktokPublishFailureReason?: string | null;
    sortDate?: string;
    instagramCollaboratorsUsernames?: string[];
    createdFromDeviceType?: DeviceType;
}

export interface SocialAttachment {
    urls: Urls;
    socialId?: string | null;
    type: SocialAttachmentsMediaTypes;
    thumbnailUrl?: string | null;
}

export interface Urls {
    original: string;
}

export interface PostHashtags {
    selected?: PostHashtag[];
    suggested?: PostHashtag[];
}

export interface PostHashtag {
    _id: string;
    text: string;
    isCustomerInput: boolean;
    isMain: boolean;
    type: HashtagType;
    createdAt: Date;
    updatedAt: Date;
}

export interface UserTagsList {
    username: string;
    x: number;
    y: number;
}

export interface Author {
    _id: string;
    name?: string;
    lastname?: string;
    picture?: string | null;
}

export interface CallToAction {
    actionType: string;
    url: string;
}

export interface Event {
    title?: string | null;
    startDate?: Date | null;
    endDate?: Date | null;
}

export interface PostSchemaLocation {
    id: string;
    name: string;
    link: string;
    location?: LocationLocation;
}

export interface LocationLocation {
    city?: string;
    country?: string;
    latitude: number;
    longitude: number;
    street?: string;
    zip?: string;
}

export interface Offer {
    couponCode?: string | null;
    onlineUrl?: string | null;
    termsConditions?: string | null;
}

export interface PublicationError {
    data?: string;
    code?: PublicationErrorCode;
    happenedAt: string;
}

export interface KeywordAnalysis {
    keywords: string[];
    score?: number;
    count?: number;
}

export interface TiktokOptions {
    privacyStatus: TiktokPrivacyStatus;
    interactionAbility: {
        duet: boolean;
        stitch: boolean;
        comment: boolean;
    };
    contentDisclosureSettings: {
        isActivated: boolean;
        yourBrand: boolean;
        brandedContent: boolean;
    };
}
